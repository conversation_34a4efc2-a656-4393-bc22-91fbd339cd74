---
title: Customize Templates
description: Customize how timestamps and screenshots are inserted into your notes.
icon: Blocks
---

Media Extended uses templates to control the format of timestamps and screenshots when you insert them into your notes. You can customize these templates to match your personal workflow.

All template settings are located in `Settings > Media Extended > Note Taking`.

## Timestamp Template

This template controls the output of the `Take timestamp` command.

- **Setting:** `Timestamp Template`
- **Default:** `- {{TIMESTAMP}}`
- **Placeholder:**
  - `{{TIMESTAMP}}`: The generated media link, like `[[media.mp4#t=1m2s]]`.

### Use Case: Creating Task Lists

If you use timestamps to mark points in a video for later review, you can format them as a task list.

- **Template:** `- [ ] {{TIMESTAMP}}`
- **Output:** `- [ ] [[media.mp4#t=1m2s]]`

This automatically creates a checklist item each time you insert a timestamp.

## Screenshot Templates

Two templates work together to format screenshots. This gives you fine-grained control over the output.

### How They Work Together

<div className="fd-steps [&_h4]:fd-step">

#### Screenshot Embed Template

This template defines the content of the `{{SCREENSHOT}}` placeholder, specifically the alt-text (or display text) for the image embed, and its size.

-   **Setting:** `Screenshot Embed Template`
-   **Default:** `{{TITLE}}{{DURATION}}|50`
-   **Placeholders:**
    -   `{{TITLE}}`: The title of the media file (e.g., `My Video`).
    -   `{{DURATION}}`: The formatted timestamp (e.g., `- 00:01:02`).
-   **Syntax:** The part after `|` in the embed, like `|50`, controls the display width of the image.

When you take a screenshot of `My Video.mp4` at `1m2s`, this template generates the image embed part: `![[screenshot-xyz.jpg|My Video - 00:01:02|50]]`.

#### Screenshot Template

This template defines the overall structure of the text inserted when you take a screenshot. It uses the output from the embed template.

-   **Setting:** `Screenshot Template`
-   **Default:** `\n!{{SCREENSHOT}} {{TIMESTAMP}}`
-   **Placeholders:**
    -   `{{SCREENSHOT}}`: The complete image embed generated by the *Screenshot Embed Template*.
    -   `{{TIMESTAMP}}`: The timestamp link to the media, like `[[media.mp4#t=1m2s]]`.

This template takes the result from the previous step and adds the timestamp link, producing the final output:
`![[screenshot-xyz.jpg|My Video - 00:01:02|50]] [[My Video.mp4#t=1m2s]]`

</div>

<Callout title="Controlling Line Breaks and Spacing">
  To add line breaks, press `Enter` in the template text area. Your newlines will be preserved in the output.
  
  The default templates often start with a newline to place content on a new line, and may end with a space for comfortable editing after insertion.
</Callout>

### Use Case: Simple Image Embed

If you only want the image and don't need a separate timestamp link, you can simplify the templates.

- **Screenshot Template:** `!{{SCREENSHOT}}`
- **Screenshot Embed Template:** `{{TITLE}}{{DURATION}}`
- **Output:** `![[screenshot-xyz.jpg|My Video - 00:01:02]]`
