{"name": "@mx/db", "version": "1.0.0", "description": "", "main": "index.js", "type": "module", "bin": {"api-keys": "./dist/scripts/api-keys.js"}, "scripts": {"dev": "tsc --watch", "build": "tsc", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:push": "drizzle-kit push"}, "exports": {"./schema": "./dist/schema/index.js", "./index": "./dist/index.js"}, "keywords": [], "author": "", "license": "ISC", "packageManager": "pnpm@10.6.4", "engines": {"node": ">=24"}, "dependencies": {"@supabase/supabase-js": "^2.47.12", "@upstash/redis": "^1.35.0", "drizzle-orm": "^0.38.4", "jose": "^5.9.6", "postgres": "^3.4.5", "yargs": "^18.0.0"}, "devDependencies": {"@types/node": "22.14.1", "@types/yargs": "^17.0.33", "concurrently": "^9.1.2", "drizzle-kit": "^0.30.5", "typescript": "~5.8.2"}}