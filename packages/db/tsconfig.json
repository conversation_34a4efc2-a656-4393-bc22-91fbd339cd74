{"extends": "../../tsconfig.json", "compilerOptions": {"declaration": true, "declarationMap": true, "incremental": true, "lib": ["es2022", "ESNext.Disposable", "DOM", "DOM.Iterable"], "module": "NodeNext", "moduleDetection": "force", "moduleResolution": "NodeNext", "target": "ES2022", "outDir": "dist", "rootDir": "src"}, "include": ["./src/**/*.ts", "./env.d.ts"], "exclude": ["./node_modules", "./dist/**/*"]}