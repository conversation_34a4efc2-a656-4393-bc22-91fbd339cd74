@custom-variant vol-active {
  /**
   * data-[active]: keep showing when dragging the slider
   */
  &[data-active],
  &:has(> [data-active]),
  /**
   * group hover: allow mobile to reveal the slider when region tapped
   * data-[active]: keep showing when dragging the slider
   */
  &:is(:where(.group\/vol-ctrl):hover *),
  &:is(:where(.group\/vol-ctrl) > :focus-visible ~ &),
  &:is(:where(.group\/vol-ctrl) > &:focus-visible),
  &:is(:where(.group\/vol-ctrl) > &:has(> :focus-visible)) {
    @slot;
  }
}