import { createClient } from "~/lib/supabase/.server";
import type { Route } from "./+types/action.user.delete";
import { withCookieStore, type Data } from "~/lib/cookie-store.server";
import { data } from "react-router";
import { cookieLogout } from "~/lib/auth/logout.server";
import { deleteUserConfirm } from "@/const";
import { toLocalStorageKey } from "~/lib/oauth2/token.def";

// export const config = { runtime: "edge" };

export type DeleteUserState =
  | { type: "error"; message: string }
  | { type: "success"; clientSideKeys: string[] }
  | { type: "idle" };

export async function action({ request }: Route.ActionArgs) {
  return withCookieStore(
    request,
    async (cookieStore): Promise<Data<DeleteUserState>> => {
      const formData = await request.formData();
      const email = formData.get("email");
      const supabase = await createClient({ admin: true, cookieStore });

      if (formData.get("confirm") !== deleteUserConfirm) {
        return data({
          type: "error",
          message: "Please type 'delete my account' to confirm",
        });
      }

      const {
        data: { user },
        error: userError,
      } = await supabase.auth.getUser();

      if (userError) {
        console.error("Failed to get user", userError);
        return data({ type: "error", message: "Failed to get user info" });
      }

      if (!user?.email) {
        return data({ type: "error", message: "No email for user" });
      }
      if (user.email !== email) {
        return data({
          type: "error",
          message: "Email does not match",
        });
      }

      const { data: delUser, error: deleteError } =
        await supabase.auth.admin.deleteUser(user.id);

      if (deleteError) {
        console.error("Failed to delete user", deleteError);
        return data({ type: "error", message: "Failed to delete user" });
      }

      const { error: signOutError } = await supabase.auth.signOut();
      if (signOutError) {
        return data({ type: "error", message: "Failed to sign out" });
      }
      await cookieLogout(cookieStore);
      const googleKey = toLocalStorageKey("google", user);
      return data({
        type: "success",
        clientSideKeys: googleKey ? [googleKey.key] : [],
      });
    },
  ).catch((error) => {
    console.error("Failed to delete user", error);
    return data({
      type: "error",
      message: error instanceof Error ? error.message : "Internal server error",
    });
  });
}
