import { createClient } from "~/lib/supabase/.server";
import { getPKCERedirectTarget } from "~/lib/oauth2/.server/pkce-redirect";

import type { Route } from "./+types/action.user.login.$provider";
import { redirect, data } from "react-router";
import { withCookieStore, type Data } from "~/lib/cookie-store.server";
import { getOrigin } from "~/lib/get-origin.server";

// export const config = { runtime: "edge" };

export type LoginState =
  | { type: "idle" }
  | { type: "error"; message: string }
  | { type: "success" };

export async function action({ request, params }: Route.ActionArgs) {
  const { provider } = params;
  if (provider !== "google" && provider !== "github") {
    return new Response("Not found", { status: 404 });
  }
  return withCookieStore(
    request,
    async (cookieStore): Promise<Data<LoginState> | Response> => {
      const formData = await request.formData();
      const supabase = await createClient({ cookieStore });
      const redirectTo =
        (await getOrigin(request.headers)) +
        (await getPKCERedirectTarget(formData));
      const { data: signIn, error } = await supabase.auth.signInWithOAuth({
        provider,
        options: { redirectTo },
      });
      if (error) {
        console.error("Error signing in with GitHub:", error);
        return data({
          type: "error",
          message:
            error instanceof Error ? error.message : "Internal server error",
        });
      }
      if (signIn.url) {
        return redirect(signIn.url);
      }
      return data({ type: "success" });
    },
  ).catch((error) => {
    console.error("Error signing in with GitHub:", error);
    return data({
      type: "error",
      message: error instanceof Error ? error.message : "Internal server error",
    });
  });
}
