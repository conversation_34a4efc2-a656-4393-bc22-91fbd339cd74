import type { Route } from "./+types/api.media.youtube.$id";
import type { youtube_v3 } from "googleapis";

import dayjs from "dayjs";
import duration from "dayjs/plugin/duration";
dayjs.extend(duration);

// export const config = { runtime: "edge" };

export interface YouTubeVideoInfo {
  title: string | null;
  duration: number;
  description: string | null;
  tags: string[];
  dimensions: {
    width: number;
    height: number;
  } | null;
  thumbnails: youtube_v3.Schema$ThumbnailDetails | null;
}

export async function loader({
  params: { id: videoId },
  request,
}: Route.LoaderArgs) {
  const apiKey = process.env.GOOGLE_API_KEY;
  if (!apiKey) {
    console.error("GOOGLE_API_KEY is not set");
    return new Response("Internal server error", { status: 500 });
  }

  const parts = ["snippet", "contentDetails"] as const;
  const query = new URLSearchParams({
    part: parts.join(","),
    id: videoId,
    key: apiKey,
  });
  const resp = await fetch(
    `https://www.googleapis.com/youtube/v3/videos?${query}`,
    { signal: request.signal },
  );
  if (!resp.ok) {
    console.error(await resp.text().catch(() => "unknown error"));
    return new Response("Internal server error", { status: 500 });
  }
  const payload = (await resp.json()) as youtube_v3.Schema$VideoListResponse;
  const videoInfo = payload.items?.[0] as Required<
    Pick<youtube_v3.Schema$Video, (typeof parts)[number]>
  >;
  if (!videoInfo) {
    // 404
    return new Response("Not found", { status: 404 });
  }
  const duration = videoInfo.contentDetails?.duration
    ? dayjs.duration(videoInfo.contentDetails.duration).asSeconds()
    : -1;

  // Cache for 1 hour, allow serving stale content for up to 1 day while revalidating
  return Response.json(
    {
      title: videoInfo.snippet.title ?? null,
      duration,
      description: videoInfo.snippet.description ?? null,
      tags: videoInfo.snippet.tags ?? [],
      thumbnails: videoInfo.snippet.thumbnails ?? null,
      dimensions: parseAspectRatio(videoInfo.snippet.thumbnails),
    } satisfies YouTubeVideoInfo,
    {
      headers: {
        "Cache-Control": "public, max-age=3600, stale-while-revalidate=86400",
      },
    },
  );
}

function parseAspectRatio(
  thumbnails: youtube_v3.Schema$ThumbnailDetails | undefined,
): { width: number; height: number } | null {
  if (!thumbnails) {
    return null;
  }
  const highest =
    thumbnails.maxres ??
    thumbnails.standard ??
    thumbnails.high ??
    thumbnails.medium ??
    thumbnails.default;
  if (!highest || !highest.width || !highest.height) {
    return null;
  }
  return {
    width: highest.width,
    height: highest.height,
  };
}
