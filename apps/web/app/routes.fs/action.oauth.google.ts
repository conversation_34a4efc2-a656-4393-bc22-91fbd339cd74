import { get<PERSON><PERSON>in } from "~/lib/get-origin.server";
import { createClient } from "~/lib/supabase/.server";
import {
  generateRandomCodeVerifier,
  calculatePKCECodeChallenge,
} from "oauth4webapi";
import { setPkceCookie } from "~/lib/oauth2/.server/pkce-cookie";
import { signState } from "~/lib/oauth2/.server/pkce-state";
import { data, redirect } from "react-router";
import type { Route } from "./+types/action.oauth.google";
import { withCookieStore } from "~/lib/cookie-store.server";
// we use Token Mediating Backend (TMB) pattern for 3rd party auth
// https://auth0.com/blog/the-backend-for-frontend-pattern-bff/
// https://datatracker.ietf.org/doc/html/draft-ietf-oauth-browser-based-apps#name-token-mediating-backend

// export const config = { runtime: "edge" };

export type State =
  | {
      type: "success";
    }
  | {
      type: "error";
      message: string;
    };

export async function action({ request }: Route.ActionArgs) {
  try {
    const form = await request.formData();
    if (!process.env.LOCAL_AUTH_GOOGLE_REDIRECT_PATH) {
      throw new Error("LOCAL_AUTH_GOOGLE_REDIRECT_PATH is not set");
    }
    if (!process.env.SUPABASE_AUTH_EXTERNAL_GOOGLE_CLIENT_ID) {
      throw new Error("SUPABASE_AUTH_EXTERNAL_GOOGLE_CLIENT_ID is not set");
    }
    if (!process.env.SUPABASE_AUTH_EXTERNAL_GOOGLE_SECRET) {
      throw new Error("SUPABASE_AUTH_EXTERNAL_GOOGLE_SECRET is not set");
    }

    const redirectUrl = new URL(
      (await getOrigin(request.headers)) +
        process.env.LOCAL_AUTH_GOOGLE_REDIRECT_PATH,
    );

    const newScopes = form.getAll("scope").filter((v) => typeof v === "string");
    const promptConsent = form.get("prompt") === "consent";
    const next = form.get("next");
    if (next) {
      if (typeof next !== "string" || !next.startsWith("/")) {
        throw new Error("Invalid next URL");
      }
    }

    return await withCookieStore(request, async (cookieStore) => {
      const supabase = await createClient({ cookieStore });

      const { data } = await supabase.auth.getUser();

      if (!data.user) {
        throw new Error("Not logged in");
      }

      const googleIdentity = data.user.identities?.find(
        (i) => i.provider === "google",
      );
      if (!googleIdentity) {
        throw new Error("No google identity linked to user");
      }

      const providerId = googleIdentity.id;

      // Generate PKCE code verifier and challenge
      const codeVerifier = generateRandomCodeVerifier();
      const codeChallenge = await calculatePKCECodeChallenge(codeVerifier);
      const sessionId = crypto.randomUUID();

      // Generate signed jwt to prevent CSRF attacks
      const state = await signState({
        userId: data.user.id,
        providerId,
        sessionId,
        next,
      });

      // Store code verifier in secure cookie
      await setPkceCookie(
        { sessionId, codeVerifier, redirectUriPath: redirectUrl.pathname },
        cookieStore,
      );

      // Build authorization URL with query parameters
      const authUrl = new URL("https://accounts.google.com/o/oauth2/v2/auth");

      // Set required OAuth parameters
      authUrl.searchParams.set(
        "client_id",
        process.env.SUPABASE_AUTH_EXTERNAL_GOOGLE_CLIENT_ID,
      );
      authUrl.searchParams.set("redirect_uri", redirectUrl.href);
      authUrl.searchParams.set("response_type", "code");

      // 'offline' to get refresh_token
      authUrl.searchParams.set("access_type", "offline");

      // consent will generate new refresh_token
      authUrl.searchParams.set("prompt", promptConsent ? "consent" : "none");

      // hint to login with the same account
      authUrl.searchParams.set("login_hint", providerId);

      // PKCE parameters
      authUrl.searchParams.set("code_challenge_method", "S256");
      authUrl.searchParams.set("code_challenge", codeChallenge);

      // Set scopes
      const scopes = ["openid", ...newScopes];
      authUrl.searchParams.set("scope", scopes.join(" "));

      // Enable incremental authorization
      authUrl.searchParams.set("include_granted_scopes", "true");

      // Add state parameter to prevent CSRF
      authUrl.searchParams.set("state", state);

      return redirect(authUrl.toString());
    });
  } catch (e) {
    return data<State>({
      type: "error",
      message: e instanceof Error ? e.message : "Internal server error",
    });
  }
}
