import { But<PERSON> } from "@/components/ui/button";
import type { Route } from "./+types/_index";
import { Link } from "react-router";

export function meta({ data }: Route.MetaArgs) {
  return [
    { title: "Media Extended" },
    { name: "description", content: "Welcome to Media Extended" },
  ];
}

export default function Home() {
  return (
    <Button asChild>
      {/* force hard navigation to /app, don't open in new tab */}
      <a href="/app">Go to app</a>
    </Button>
  );
}
