export default function Page() {
  return (
    <div className="flex flex-1 flex-col gap-4 px-4 py-10">
      <ClientSuspense fallback={<div>Loading...</div>}>
        <PingTest />
        <BilibiliScreenshotTest />
        <CourseraScreenshotTest />
        <CourseraPlaybackTest />
      </ClientSuspense>
    </div>
  );
}

import { Button } from "@/components/ui/button";
import { useMemo } from "react";
import ExtensionExternalAdapter from "@mx/shared/rpc/adapter/ext-external";

import { type Fn as PingFn, name as pingName } from "@mx/shared/rpc/ping";
import {
  type Fn as FnScreenshot,
  name as fnScreenshotName,
} from "@mx/ext-fn/app2ext/screenshot";
import ClientSuspense from "@/components/client-suspense";

class ExtensionConnection {
  constructor() {
    const adapter = new ExtensionExternalAdapter({
      sendTarget: window.ENV.NEXT_PUBLIC_EXTENSION_ID,
      verifyTlsChannel: false,
    });
    const client = defineConsumer(adapter);
    this.ping = client.createRemoteFn<PingFn>(pingName);
    this.captureScreenshot =
      client.createRemoteFn<FnScreenshot>(fnScreenshotName);
    this.control = client.createRemoteFn<FnPlayback>(fnPlaybackName);
  }
  ping;
  captureScreenshot;
  control;
}

function useExtension() {
  const conn = useMemo(() => new ExtensionConnection(), []);
  return conn;
}

function PingTest() {
  const conn = useExtension();
  return (
    <Button
      onClick={() =>
        conn
          .ping()
          .then((v) => v && console.info("Extension Inited"))
          .catch((err) => {
            if (err instanceof Error) {
              console.error(err.message);
            } else {
              console.error(err);
              console.error("Unknown error");
            }
          })
      }
    >
      Ping
    </Button>
  );
}

function BilibiliScreenshotTest() {
  const conn = useExtension();
  return (
    <Button
      onClick={() =>
        conn
          .captureScreenshot({
            type: "bvid",
            bvid: "BV1Q5411K7Q5",
            host: "bilibili",
            page: 1,
            t: null,
          })
          .then((v) => {
            console.log("screenshot", v);
            v && console.info("Screenshot Success");
          })
          .catch((err) => {
            if (err instanceof Error) {
              console.error(err.message);
            } else {
              console.error(err);
              console.error("Unknown error");
            }
          })
      }
    >
      bilibili Screenshot
    </Button>
  );
}

function CourseraScreenshotTest() {
  const conn = useExtension();

  return (
    <Button
      onClick={() =>
        conn
          .captureScreenshot({
            type: "lecture",
            courseId: "english-common-interactions-workplace-basic-level",
            lectureId: "fsRKC",
            host: "coursera",
          })
          .then((v) => {
            console.log("screenshot", v);
            v && console.info("Screenshot Success");
          })
          .catch((err) => {
            if (err instanceof Error) {
              console.error(err.message);
            } else {
              console.error(err);
              console.error("Unknown error");
            }
          })
      }
    >
      coursera Screenshot
    </Button>
  );
}

import {
  type Fn as FnPlayback,
  name as fnPlaybackName,
} from "@mx/ext-fn/app2ext/playback";
import type { PlaybackAction } from "@mx/ext-fn/playback-actions";
import { defineConsumer } from "@mx/shared/rpc";
function CourseraPlaybackTest() {
  const conn = useExtension();

  return (
    <div className="flex flex-wrap gap-2">
      {(
        [
          { label: "play/pause", action: { type: "play-pause", paused: null } },
          { label: "play", action: { type: "play-pause", paused: false } },
          { label: "pause", action: { type: "play-pause", paused: true } },
          {
            label: "set volume 0.5",
            action: { type: "set-volume", volume: 0.5 },
          },
          {
            label: "set volume 1.0",
            action: { type: "set-volume", volume: 1.0 },
          },
          { label: "set muted", action: { type: "set-muted", muted: true } },
          { label: "set unmuted", action: { type: "set-muted", muted: false } },
          {
            label: "set playback rate 0.5",
            action: { type: "set-playback-rate", playbackRate: 0.5 },
          },
          {
            label: "set playback rate 1.0",
            action: { type: "set-playback-rate", playbackRate: 1.0 },
          },
          { label: "seek 10", action: { type: "seek", time: 10 } },
          { label: "seek 60", action: { type: "seek", time: 60 } },
          { label: "seek 120", action: { type: "seek", time: 120 } },
          { label: "seek 180", action: { type: "seek", time: 180 } },
        ] as {
          label: string;
          action: PlaybackAction;
        }[]
      ).map(({ label, action }) => (
        <Button
          key={label}
          onClick={() =>
            conn
              .control(
                {
                  type: "lecture",
                  courseId: "english-common-interactions-workplace-basic-level",
                  lectureId: "fsRKC",
                  host: "coursera",
                },
                action,
              )
              .then((v) => {
                console.info(`${label} Success`);
              })
              .catch((err) => {
                if (err instanceof Error) {
                  console.error(label, err.message);
                } else {
                  console.error(label, err);
                }
              })
          }
        >
          coursera {label}
        </Button>
      ))}
    </div>
  );
}
