import type { Route } from "./+types/action.user.logout";
import { createClient } from "~/lib/supabase/.server";
import { cookieLogout } from "~/lib/auth/logout.server";
import { withCookieStore, type Data } from "~/lib/cookie-store.server";
import { data } from "react-router";
import { toLocalStorageKey } from "~/lib/oauth2/token.def";

// export const config = { runtime: "edge" };

export type State =
  | { type: "success"; clientSideKeys: string[] }
  | { type: "error"; error: Error };

export async function action({ request }: Route.ActionArgs) {
  return withCookieStore(request, async (cookieStore): Promise<Data<State>> => {
    const supabase = await createClient({ cookieStore });
    const session = await supabase.auth.getSession();
    if (session.error) {
      return data({ type: "error", error: session.error });
    }
    if (!session.data.session) {
      return data({ type: "success", clientSideKeys: [] });
    }
    const { error: signOutError } = await supabase.auth.signOut();
    if (signOutError) {
      return data({ type: "error", error: signOutError });
    }
    await cookieLogout(cookieStore);
    const googleKey = toLocalStorageKey("google", session.data.session.user);
    return data({
      type: "success",
      clientSideKeys: googleKey ? [googleKey.key] : [],
    });
  }).catch((error) => {
    console.error("Error logging out:", error);
    return data({ type: "error", message: "Internal server error" });
  });
}
