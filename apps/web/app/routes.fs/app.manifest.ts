import { urlScheme } from "@/const";
import type { Route } from "./+types/app.manifest";

// export const config = { runtime: "edge" };

const isProd = process.env.NODE_ENV === "production";
const skewHeaders: Record<string, string> =
  process.env.VERCEL_SKEW_PROTECTION_ENABLED === "1" &&
  process.env.VERCEL_DEPLOYMENT_ID
    ? { "x-deployment-id": process.env.VERCEL_DEPLOYMENT_ID }
    : {};

export function loader(_: Route.LoaderArgs) {
  const manifest: Manifest = {
    name: isProd ? "Media Extended" : "Media Extended (Dev)",
    short_name: isProd ? "Media Extended" : "Media Extended (Dev)",
    description: "A Progressive Web App for managing your media library",
    start_url: "/app",
    scope: "/app",
    display: "minimal-ui",
    background_color: "#ffffff",
    theme_color: "#000000",
    icons: [
      {
        src: "/icon-192x192.svg",
        sizes: "192x192",
        type: "image/svg+xml",
      },
    ],
    launch_handler: {
      // file/protocol handler will focus existing tab without navigation if already open
      client_mode: "focus-existing",
    },
    protocol_handlers: [
      {
        protocol: urlScheme,
        url: "/app/launch/url-scheme#url=%s",
      },
    ],
    file_handlers: [
      {
        action: "/app/launch/local-file",
        accept: {
          "video/mp4": [".mp4"],
          "video/webm": [".webm"],
          "audio/wav": [".wav"],
          "audio/x-wav": [".wav"],
          "audio/mpeg": [".mp3"],
          "audio/mp4": [".m4a"],
          "audio/ogg": [".ogg"],
          "application/ogg": [".ogg"],
          "audio/webm": [".webm"],
          "audio/flac": [".flac"],
        },
      },
    ],
  };
  return Response.json(manifest, {
    headers: {
      ...skewHeaders,
      // this should be statically generated
      "Cache-Control": "public, max-age=31536000, immutable",
    },
  });
}

// https://github.com/vercel/next.js/blob/8986037d33c2247dcc5af2bade73bf260dfb4cf0/packages/next/src/lib/metadata/types/manifest-types.ts

type ClientModeEnum =
  | "auto"
  | "focus-existing"
  | "navigate-existing"
  | "navigate-new";

type File = {
  name: string;
  accept: string | string[];
};

type Icon = {
  src: string;
  type?: string | undefined;
  sizes?: string | undefined;
  purpose?: "any" | "maskable" | "monochrome" | undefined;
};

export type Manifest = {
  background_color?: string | undefined;
  categories?: string[] | undefined;
  description?: string | undefined;
  dir?: "ltr" | "rtl" | "auto" | undefined;
  display?: "fullscreen" | "standalone" | "minimal-ui" | "browser" | undefined;
  display_override?:
    | (
        | "fullscreen"
        | "standalone"
        | "minimal-ui"
        | "browser"
        | "window-controls-overlay"
      )[]
    | undefined;
  file_handlers?:
    | {
        action: string;
        accept: {
          [mimeType: string]: string[];
        };
      }[]
    | undefined;
  icons?: Icon[] | undefined;
  id?: string | undefined;
  lang?: string | undefined;
  launch_handler?:
    | {
        client_mode: ClientModeEnum | ClientModeEnum[];
      }
    | undefined;
  name?: string | undefined;
  orientation?:
    | "any"
    | "natural"
    | "landscape"
    | "portrait"
    | "portrait-primary"
    | "portrait-secondary"
    | "landscape-primary"
    | "landscape-secondary"
    | undefined;
  prefer_related_applications?: boolean | undefined;
  protocol_handlers?:
    | {
        protocol: string;
        url: string;
      }[]
    | undefined;
  related_applications?:
    | {
        platform: string;
        url: string;
        id?: string | undefined;
      }[]
    | undefined;
  scope?: string | undefined;
  screenshots?:
    | {
        form_factor?: "narrow" | "wide" | undefined;
        label?: string | undefined;
        platform?:
          | "android"
          | "chromeos"
          | "ipados"
          | "ios"
          | "kaios"
          | "macos"
          | "windows"
          | "xbox"
          | "chrome_web_store"
          | "itunes"
          | "microsoft-inbox"
          | "microsoft-store"
          | "play"
          | undefined;
        src: string;
        type?: string | undefined;
        sizes?: string | undefined;
      }[]
    | undefined;
  share_target?:
    | {
        action: string;
        method?: "get" | "post" | "GET" | "POST" | undefined;
        enctype?:
          | "application/x-www-form-urlencoded"
          | "multipart/form-data"
          | undefined;
        params: {
          title?: string | undefined;
          text?: string | undefined;
          url?: string | undefined;
          files?: File | File[] | undefined;
        };
      }
    | undefined;
  short_name?: string | undefined;
  shortcuts?:
    | {
        name: string;
        short_name?: string | undefined;
        description?: string | undefined;
        url: string;
        icons?: Icon[] | undefined;
      }[]
    | undefined;
  start_url?: string | undefined;
  theme_color?: string | undefined;
};
