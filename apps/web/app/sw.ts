import { urlPatternPolyfillScriptPath } from "@/const";
import launchRouteRedirect from "@/routes.client/launch";
import { Router } from "@/lib/router";
import driveApiClientRoute from "@/routes.client/drive-api";

declare const self: ServiceWorkerGlobalScope;

// @ts-expect-error
if (!globalThis.URLPattern) {
  self.importScripts(urlPatternPolyfillScriptPath);
}

self.addEventListener("install", () => {
  self.skipWaiting();
});

const router = new Router();
router.get({ pathname: "/launch/:path*" }, ({ pattern, url }) =>
  launchRouteRedirect(pattern.pathname.groups.path, url),
);
router.get(
  { pathname: "/api/drive/:provider/:id" },
  ({
    pattern: {
      pathname: { groups: params },
    },
    request,
    url,
  }) =>
    driveApiClientRoute(
      { provider: params.provider!, id: params.id! },
      request,
      url.searchParams,
    ),
);

// Handle fetch events with the router
self.addEventListener("fetch", (evt) => {
  const routerResponse = router.handleRequest(evt.request);

  if (routerResponse) {
    // Use respondWith with the router's response
    evt.respondWith(routerResponse);
  } else if (evt.request.mode === "navigate") {
    // For navigation requests, try to use preloadResponse if available
    evt.respondWith(
      (async () => {
        try {
          // Try to use the preloaded response first
          const preloadResponse = await evt.preloadResponse;
          if (preloadResponse) {
            return preloadResponse;
          }

          // Otherwise, do a normal fetch
          return fetch(evt.request);
        } catch (error) {
          console.error("Navigation preload failed:", error);
          return fetch(evt.request);
        }
      })(),
    );
  }
});
