import { queryClient } from "@/data/client";
import clientLogout from "~/lib/auth/logout.client";
import { useFetcher } from "react-router";
import { useCallback, useEffect } from "react";

export function useLogoutMutation() {
  const { data, submit, state } = useFetcher();
  useEffect(() => {
    if (data?.type === "success") {
      clientLogout({ clientSideKeys: data.clientSideKeys });
      queryClient.invalidateQueries({ queryKey: ["user-session"] });
    }
  }, [data]);

  return {
    logout: useCallback(
      () => submit(null, { method: "post", action: "/action/user/logout" }),
      [submit],
    ),
    isLoading: state !== "idle",
  };
}
