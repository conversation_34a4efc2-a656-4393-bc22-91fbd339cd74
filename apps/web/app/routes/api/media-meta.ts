import { parseInfoJson } from "@/lib/info-json";
// import type { Route } from "./+types/media-meta";
import { data } from "react-router";

// export const config = { runtime: "edge" };

export async function loader({
  params,
}: { params: { provider: string; id: string } }) {
  try {
    const { provider: host, id: paramId } = params;
    const reqHeaders = new Headers({
      Authorization: `Bearer ${process.env.MX_HOME_TOKEN}`,
    });
    let resp: Response;
    if (host === "bilibili") {
      // use format av/BV..._p...
      const [id, p] = paramId.split("_p");
      if (Number.isNaN(Number.parseInt(p ?? "1", 10))) {
        return new Response("Invalid bilibili id", { status: 400 });
      }
      resp = await fetch(
        `${process.env.MX_HOME_ENTRYPOINT}/info/${host}/${id}?p=${p}`,
        { headers: reqHeaders },
      );
    } else {
      resp = await fetch(
        `${process.env.MX_HOME_ENTRYPOINT}/info/${host}/${paramId}`,
        { headers: reqHeaders },
      );
    }
    if (!resp.ok) {
      console.error("Failed to fetch media meta", resp.statusText);
      return new Response("Failed to fetch media meta", {
        status: resp.status,
      });
    }
    return data(parseInfoJson(await resp.json()));
  } catch (e) {
    console.error("Failed to parse media meta", e);
    return new Response("Failed to parse media meta", { status: 500 });
  }
}
