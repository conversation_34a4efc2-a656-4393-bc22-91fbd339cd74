import { withCookieStore } from "~/lib/cookie-store.server";
import { isOAuth2Provider } from "~/lib/oauth2/def";
import { updateToken } from "~/lib/oauth2/token.server";
import type { Route } from "./+types/auth.token";

// export const config = { runtime: "edge" };

export async function loader({
  params: { provider },
  request,
}: Route.LoaderArgs): Promise<Response> {
  const scopes = new URL(request.url).searchParams.getAll("scope");
  if (!isOAuth2Provider(provider)) {
    return new Response(null, { status: 400 });
  }
  try {
    return await withCookieStore(request, async (cookies) =>
      Response.json(
        await updateToken({
          provider,
          targetScopes: scopes,
          cookieStore: cookies,
        }),
      ),
    );
  } catch (e) {
    return new Response("Failed to refresh token", { status: 500 });
  }
}
