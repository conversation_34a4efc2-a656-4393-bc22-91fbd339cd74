import { withCookieStore } from "~/lib/cookie-store.server";
import { replace } from "react-router";
import type { Route } from "./+types/auth.callback.google";

import { getPkceCookie } from "~/lib/oauth2/.server/pkce-cookie";
import { refreshTokenFor } from "~/lib/oauth2/.server/refresh-store";
import { verifyState } from "~/lib/oauth2/.server/pkce-state";
import { getOrigin } from "~/lib/get-origin.server";
import { parseNextTarget } from "~/lib/next-target";
import { redirectRoute } from "@/const";

// export const config = { runtime: "edge" };

export async function loader({ request }: Route.LoaderArgs): Promise<Response> {
  const { searchParams } = new URL(request.url);
  const code = searchParams.get("code");
  const state = searchParams.get("state");
  if (!code || !state) {
    return new Response("Missing code or state", { status: 400 });
  }

  const clientId = process.env.SUPABASE_AUTH_EXTERNAL_GOOGLE_CLIENT_ID;
  const clientSecret = process.env.SUPABASE_AUTH_EXTERNAL_GOOGLE_SECRET;
  const redirectUri =
    (await getOrigin(request.headers)) +
    process.env.LOCAL_AUTH_GOOGLE_REDIRECT_PATH;

  const statePayload = await verifyState(state);

  return withCookieStore(request, async (cookieStore) => {
    const pkceCookie = await getPkceCookie(statePayload.sessionId, cookieStore);
    if (!pkceCookie) {
      return new Response("no pkce session", { status: 400 });
    }

    const { codeVerifier } = pkceCookie;

    // Exchange authorization code for tokens
    const tokenResponse = await fetch("https://oauth2.googleapis.com/token", {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body: new URLSearchParams({
        code,
        client_id: clientId!,
        client_secret: clientSecret!,
        redirect_uri: redirectUri,
        grant_type: "authorization_code",
        code_verifier: codeVerifier,
      }),
    });

    if (!tokenResponse.ok) {
      return new Response(
        "Failed to get tokens: " + (await tokenResponse.text()),
        {
          status: tokenResponse.status,
        },
      );
    }

    const tokens = await tokenResponse.json();

    // openid scope is required
    if (!tokens.scope?.includes("openid")) {
      return new Response("Missing openid scope", { status: 400 });
    }

    if (!tokens.access_token) {
      return new Response("Missing access token", { status: 400 });
    }

    if (!tokens.refresh_token) {
      return new Response("Missing refresh token", { status: 400 });
    }

    if (!tokens.expires_in) {
      return new Response("Missing expires time", { status: 400 });
    }

    if (!tokens.scope) {
      return new Response("Missing scope", { status: 400 });
    }

    // Get user info using the access token
    const tokenInfoQuery = new URLSearchParams({
      access_token: tokens.access_token,
    });
    const tokenInfoResponse = await fetch(
      `https://oauth2.googleapis.com/tokeninfo?${tokenInfoQuery.toString()}`,
    );

    if (!tokenInfoResponse.ok) {
      return new Response(
        `Failed to get token info: ${await tokenInfoResponse.text()}`,
        {
          status: tokenInfoResponse.status,
        },
      );
    }

    const tokenInfo = await tokenInfoResponse.json();

    const providerId = tokenInfo.sub;
    if (!providerId) {
      return new Response("Missing user id", { status: 400 });
    }

    if (providerId !== statePayload.providerId) {
      return new Response("User id mismatch", { status: 400 });
    }

    const refreshToken = refreshTokenFor(
      {
        provider: "google",
        providerId,
        userId: statePayload.userId,
      },
      cookieStore,
    );

    await refreshToken.set({
      refreshToken: tokens.refresh_token,
      scopes: tokens.scope.split(" "),
    });

    const redirectSearch = new URLSearchParams();
    redirectSearch.set("next", parseNextTarget(statePayload.next));

    // redirect to the redirect page to save cookies client side
    return replace(`${redirectRoute}?${redirectSearch.toString()}`);
  });
}
