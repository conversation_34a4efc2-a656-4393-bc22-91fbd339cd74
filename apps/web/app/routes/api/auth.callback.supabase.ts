import { withCookieStore } from "~/lib/cookie-store.server";
import { createClient } from "~/lib/supabase/.server";
import { redirect } from "react-router";
import type { Route } from "./+types/auth.callback.supabase";
import { authErrorRoute } from "@/const";
import { parseNextTarget } from "~/lib/next-target";

// export const config = { runtime: "edge" };

export async function loader({ request }: Route.LoaderArgs): Promise<Response> {
  const { searchParams } = new URL(request.url);
  const code = searchParams.get("code");
  // if "next" is in param, use it as the redirect URL
  const next = parseNextTarget(searchParams.get("next"));

  if (!code) {
    return redirect(authErrorRoute);
  }

  return withCookieStore(request, async (cookieStore) => {
    const supabase = await createClient({ cookieStore });
    const { error } = await supabase.auth.exchangeCodeForSession(code);
    if (error) {
      console.log("auth callback error:", error);
      return redirect(authErrorRoute);
    }
    return redirect(next);
  });
}
