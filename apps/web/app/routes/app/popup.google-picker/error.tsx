import { Button } from "@/components/ui/button";
import { useEffect } from "react";

export default function ErrorFallback({
  error,
  noLogError = false,
}: {
  error: Error & { digest?: string };
  noLogError?: boolean;
  reset?: () => void;
}) {
  useEffect(() => {
    if (!noLogError) {
      console.error(error);
    }
  }, [error, noLogError]);
  return (
    <div className="flex min-h-[100dvh] flex-col items-center justify-center bg-background px-4 py-12 sm:px-6 lg:px-8">
      <div className="mx-auto max-w-md text-center">
        <div className="mx-auto h-12 w-12 text-primary" />
        <h1 className="mt-4 text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
          Oops, something went wrong!
        </h1>
        <p className="mt-4 text-muted-foreground">{error.message}</p>
        <p className="mt-2 text-sm text-muted-foreground">
          Check the console for more details.
        </p>
        <div className="mt-6">
          <Button type="button" onClick={() => window.close()}>
            Close
          </Button>
        </div>
      </div>
    </div>
  );
}
