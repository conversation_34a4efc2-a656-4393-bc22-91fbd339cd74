import { useCallback, useEffect, useRef, useState } from "react";
import GoogleDrivePickerContainer, {
  type GooglePickerChannel,
  type GooglePickerMessage,
} from "@/components/google-picker";
import { Loader2 } from "lucide-react";
import ErrorFallback from "./error";
import { useBroadcastChannel } from "@/hooks/use-broadcast-channel";
import {
  isRouteErrorResponse,
  useRouteError,
  useSearchParams,
} from "react-router";
import { ErrorBoundary as ReactErrorBoundary } from "react-error-boundary";

export default function GooglePickerPage() {
  const [searchParams] = useSearchParams();
  const id = searchParams.get("id");

  const { postMessage } = useBroadcastChannel<GooglePickerMessage>(
    id && (`google-picker#${id}` satisfies GooglePickerChannel),
  );
  const [loaded, setLoaded] = useState(false);

  const submittedRef = useRef(false);

  useEffect(() => {
    const onBeforeUnload = () => {
      if (!submittedRef.current) {
        postMessage({ type: "cancel" });
      }
    };
    window.addEventListener("beforeunload", onBeforeUnload);
    return () => {
      window.removeEventListener("beforeunload", onBeforeUnload);
    };
  }, [postMessage]);

  const onSelect = useCallback(
    (fileId: string) => {
      submittedRef.current = true;
      postMessage({ type: "submit", fileIds: [fileId] });
      window.close();
    },
    [postMessage],
  );
  const onCancel = useCallback(() => {
    submittedRef.current = true;
    postMessage({ type: "cancel" });
    window.close();
  }, [postMessage]);
  const onLoaded = useCallback(() => {
    setLoaded(true);
    console.log("loaded");
  }, []);

  if (!id) {
    throw new Error("No ID provided for google picker");
  }

  return (
    <ReactErrorBoundary
      fallbackRender={({ error, resetErrorBoundary }) => (
        <ErrorFallback error={error} reset={resetErrorBoundary} />
      )}
      onError={(error) => {
        submittedRef.current = true;
        postMessage({ type: "error", error });
      }}
    >
      <div>
        <GoogleDrivePickerContainer
          onSelect={onSelect}
          onCancel={onCancel}
          onLoaded={onLoaded}
        >
          {!loaded && <Loading />}
        </GoogleDrivePickerContainer>
      </div>
    </ReactErrorBoundary>
  );
}

export function HydrateFallback() {
  return (
    <div className="contents">
      <Loading />
    </div>
  );
}

function Loading() {
  return (
    <div className="fixed inset-0 grid place-items-center">
      <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
    </div>
  );
}

export function ErrorBoundary() {
  const error = useRouteError();

  if (isRouteErrorResponse(error)) {
    return (
      <div>
        <h1>
          {error.status} {error.statusText}
        </h1>
        <p>{error.data}</p>
      </div>
    );
  }
  if (error instanceof Error) {
    return <ErrorFallback error={error} noLogError />;
  }
  return <h1>Unknown Error</h1>;
}
