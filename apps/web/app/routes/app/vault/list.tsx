import { But<PERSON> } from "@/components/ui/button";
import { mediaListQuery<PERSON>tom } from "@/data/media-list/query";
import { removeMediaRefMutation } from "@/data/media-ref/mut/remove.atom";
import { useAtomValue } from "jotai";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Skeleton } from "@/components/ui/skeleton";
import React from "react";
import { href, Link } from "react-router";

export default function MediaList() {
  const { data = [] } = useAtomValue(mediaListQueryAtom);
  const { mutateAsync: removeMediaRef } = useAtomValue(removeMediaRefMutation);

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Title</TableHead>
            <TableHead>Description</TableHead>
            <TableHead className="w-[100px]">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {data.map(({ id, title, description }) => {
            return (
              <TableRow key={id}>
                <TableCell className="font-medium">
                  <div className="flex items-center gap-2 max-h-24 overflow-scroll">
                    <Link
                      to={`${href("/app/media")}?${new URLSearchParams({ id })}`}
                    >
                      {title}
                    </Link>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="whitespace-pre-line max-h-24 overflow-scroll">
                    {description || "-"}
                  </div>
                </TableCell>
                <TableCell>
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={() => {
                      removeMediaRef(id).then((removed) => {
                        console.log("media ref removed", removed);
                      });
                    }}
                  >
                    Remove
                  </Button>
                </TableCell>
              </TableRow>
            );
          })}
          {data.length === 0 && (
            <TableRow>
              <TableCell colSpan={4} className="text-center">
                No media files added yet.
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  );
}

export function MediaListFallback() {
  const [show, setShow] = React.useState(false);

  React.useEffect(() => {
    const timer = setTimeout(() => setShow(true), 1000);
    return () => clearTimeout(timer);
  }, []);

  if (!show) return null;

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Title</TableHead>
            <TableHead>Description</TableHead>
            <TableHead>Duration</TableHead>
            <TableHead className="w-[100px]">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {Array.from({ length: 5 }).map((_, index) => (
            // biome-ignore lint/suspicious/noArrayIndexKey: placeholder
            <TableRow key={index}>
              <TableCell>
                <Skeleton className="h-4 w-3/4" />
              </TableCell>
              <TableCell>
                <Skeleton className="h-4 w-full" />
              </TableCell>
              <TableCell>
                <Skeleton className="h-4 w-1/2" />
              </TableCell>
              <TableCell>
                <Skeleton className="h-6 w-20" />
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
