import { MediaListFallback } from "./list";
import MediaList from "./list";
import type { BreadcrumbHandle } from "../layout/route";
import {
  Breadcrumb,
  BreadcrumbList,
  BreadcrumbItem,
  BreadcrumbPage,
} from "@/components/ui/breadcrumb";
import ClientSuspense from "@/components/client-suspense";

export const handle: BreadcrumbHandle = {
  breadcrumb: (
    <Breadcrumb>
      <BreadcrumbList>
        <BreadcrumbItem>
          <BreadcrumbPage>Vault</BreadcrumbPage>
        </BreadcrumbItem>
      </BreadcrumbList>
    </Breadcrumb>
  ),
};

export default function VaultPage() {
  return (
    <div className="flex flex-1 flex-col gap-4 px-4 py-10">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Media Vault</h1>
      </div>
      <ClientSuspense fallback={<MediaListFallback />}>
        <MediaList />
      </ClientSuspense>
    </div>
  );
}
