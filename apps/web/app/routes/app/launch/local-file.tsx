import { launchSupport<PERSON>tom } from "@/hooks/pwa/launch";
import { useAtomValue } from "jotai";
import { Suspense, useEffect } from "react";
import { useNavigate } from "react-router";
import { existingMediaFromLaunchFileAtom } from "@/hooks/pwa/file-query";
import { AddLocalMediaCard } from "@/components/media-import/add-media";

function RedirectHandler({ children }: React.PropsWithChildren) {
  const [targetRef] = useAtomValue(existingMediaFromLaunchFileAtom);
  const navigate = useNavigate();

  useEffect(() => {
    if (targetRef) {
      navigate(`/app/media?id=${targetRef.id}`);
    }
  }, [targetRef, navigate]);

  if (targetRef) {
    // return <div>Redirecting to {targetRef.title}</div>;
    return null;
  }
  return children;
}

export default function LocalFileHandlerClient() {
  const launchSupport = useAtomValue(launchSupportAtom);
  const launchFile = useAtomValue(launchFileAtomAtom);

  if (!launchSupport) {
    return <div>Loading...</div>;
  }
  if (!launchSupport.file) {
    return <div>File launch not supported</div>;
  }

  if (launchFile === "na") return <div>No file provided</div>;
  if (launchFile === "not-a-file") return <div>Not a file</div>;
  if (launchFile === "permission-denied")
    return <div>Given file permission denied</div>;

  return (
    <Suspense>
      <RedirectHandler>
        <AddLocalMediaCard fileInput={launchFile} />
      </RedirectHandler>
    </Suspense>
  );
}

import { launchParamsAtom } from "@/hooks/pwa/launch";
import { atom } from "jotai";
import type { IFileHandleInput } from "@/lib/file-input-utils";

const launchFileAtomAtom = atom(async (get) => {
  const launchParams = get(launchParamsAtom);
  if (!launchParams?.files?.length) return "na";
  // currently supports only one file
  const systemHandle = launchParams.files[0];
  if (!(systemHandle instanceof FileSystemFileHandle)) return "not-a-file";
  const permission = await systemHandle.queryPermission({ mode: "read" });
  if (permission !== "granted") {
    console.error("Permission denied", systemHandle.name);
    return "permission-denied";
  }
  const file = await systemHandle.getFile();
  return {
    type: "file-handle",
    file,
    systemHandle,
  } satisfies IFileHandleInput;
});
