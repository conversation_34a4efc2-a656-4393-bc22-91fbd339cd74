import { useLayoutEffect, useState } from "react";
import { parseWebMxScheme } from "@/lib/protocol-handler/api";
import { useNavigate } from "react-router";

// this page only for client-side redirect
export default function UrlSchemeHandlerPage() {
  const [message, setMessage] = useState<string | null>(null);
  const navigate = useNavigate();
  useLayoutEffect(() => {
    const handleHashChange = () => {
      const pageHash = window.location.hash;
      const params = new URLSearchParams(pageHash.replace(/^#/, ""));
      const schemeURL = parseWebMxScheme(params.get("url"));
      if (!schemeURL) {
        setMessage("Scheme not provided");
        return;
      }
      const { pathname, hash, search } = schemeURL;
      if (pathname.startsWith("/media/")) {
        const id = pathname.split("/").pop();
        if (!id) {
          setMessage("Invalid /media/:id scheme");
          return;
        }
        navigate(`/app/media?id=${id + search + hash}`, { replace: true });
      } else {
        setMessage(`Scheme ${pathname} not supported`);
      }
    };
    handleHashChange();
    window.addEventListener("hashchange", handleHashChange);
    return () => {
      window.removeEventListener("hashchange", handleHashChange);
    };
  }, [navigate]);

  return <div>{message}</div>;
}
