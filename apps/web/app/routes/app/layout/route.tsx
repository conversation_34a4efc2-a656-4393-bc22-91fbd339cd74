import { useMediaLaunchHandler } from "~/lib/media-launch/use-media-launch";
import { Outlet, useMatches, type UIMatch } from "react-router";
import type { Route } from "./+types/route";
import { AppSidebar } from "./app-sidebar";
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar";
import { StorageWarning } from "@/components/storage-warning";
import { Separator } from "@/components/ui/separator";
import type { JSX } from "react";
import { crossOriginIsolationHeaders } from "@/const";

export function headers(_: Route.HeadersArgs) {
  return {
    ...crossOriginIsolationHeaders,
  };
}

export const meta: Route.MetaFunction = () => {
  return [{ title: "Media Extended App" }];
};

export default function Layout() {
  return (
    <SidebarProvider defaultOpen>
      <AppSidebar />
      <SidebarInset>
        <StorageWarning />
        <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12">
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger className="-ml-1" />
            <AppBreadcrumb />
          </div>
        </header>
        <Outlet />
      </SidebarInset>
    </SidebarProvider>
  );
}

function AppBreadcrumb() {
  const matches = useMatches();
  const breadcrumb = (
    matches[matches.length - 1] as UIMatch<unknown, BreadcrumbHandle>
  ).handle?.breadcrumb;

  if (!breadcrumb) return null;
  return (
    <>
      <Separator
        orientation="vertical"
        className="mr-2 data-[orientation=vertical]:h-4"
      />
      {breadcrumb}
    </>
  );
}

export type BreadcrumbHandle = {
  breadcrumb?: JSX.Element | null;
};
