import { ChevronRight, type LucideIcon } from "lucide-react";

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from "@/components/ui/sidebar";
import { Link } from "react-router";

export interface NavMainItemProps {
  title: string;
  url: string;
  icon: LucideIcon;
  isActive?: boolean;
  items?: NavMainSubItemProps[];
}

export function NavMain({
  items,
}: {
  items: NavMainItemProps[];
}) {
  return (
    <SidebarGroup>
      <SidebarMenu>
        {items.map((item) =>
          item.items ? (
            <Collapsible
              key={item.title}
              asChild
              defaultOpen={item.isActive}
              className="group/collapsible"
            >
              <SidebarMenuItem>
                <CollapsibleTrigger asChild>
                  <SidebarMenuButton tooltip={item.title}>
                    {item.icon && <item.icon />}
                    <span>{item.title}</span>
                    <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
                  </SidebarMenuButton>
                </CollapsibleTrigger>
                <CollapsibleContent>
                  <SidebarMenuSub>
                    {item.items?.map((subItem) => (
                      <NavMainSubItem key={subItem.title} {...subItem} />
                    ))}
                  </SidebarMenuSub>
                </CollapsibleContent>
              </SidebarMenuItem>
            </Collapsible>
          ) : (
            <NavMainItemButton
              key={item.title}
              title={item.title}
              icon={item.icon}
              url={item.url}
            />
          ),
        )}
      </SidebarMenu>
    </SidebarGroup>
  );
}

type NavMainSubItemProps = {
  title: string;
  url: string;
};

function NavMainSubItem({ title, url }: NavMainSubItemProps) {
  return (
    <SidebarMenuSubItem>
      <SidebarMenuSubButton asChild>
        <Link to={url}>
          <span>{title}</span>
        </Link>
      </SidebarMenuSubButton>
    </SidebarMenuSubItem>
  );
}

function NavMainItemButton({
  title,
  icon: Icons,
  url,
}: Omit<NavMainItemProps, "items">) {
  return (
    <SidebarMenuButton tooltip={title} asChild>
      <Link to={url}>
        {Icons && <Icons />}
        <span>{title}</span>
        <ChevronRight className="ml-auto" />
      </Link>
    </SidebarMenuButton>
  );
}

function NavMainSubItemGroup({
  title,
  icon: Icons,
  items,
  isActive,
}: Omit<NavMainItemProps, "items"> & { items: NavMainSubItemProps[] }) {
  return (
    <Collapsible
      key={title}
      asChild
      defaultOpen={isActive}
      className="group/collapsible"
    >
      <SidebarMenuItem>
        <CollapsibleTrigger asChild>
          <SidebarMenuButton tooltip={title}>
            {Icons && <Icons />}
            <span>{title}</span>
            <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
          </SidebarMenuButton>
        </CollapsibleTrigger>
        <CollapsibleContent>
          <SidebarMenuSub>
            {items.map((subItem) => (
              <NavMainSubItem key={subItem.title} {...subItem} />
            ))}
          </SidebarMenuSub>
        </CollapsibleContent>
      </SidebarMenuItem>
    </Collapsible>
  );
}
