import { Loader2, UserRound } from "lucide-react";

import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar";
import { Dialog, DialogTrigger } from "@/components/ui/dialog";
import { Skeleton } from "@/components/ui/skeleton";

export function NavLoginSkeleton() {
  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <Dialog>
          <SidebarMenuButton disabled size="lg" tooltip="Login / Sign up">
            <div className="relative flex size-8 shrink-0 overflow-hidden rounded-lg">
              <Skeleton className="absolute inset-0" />
              <div className="relative z-10 flex size-full items-center justify-center">
                <UserRound className="size-4 text-muted-foreground/50" />
              </div>
            </div>
            <div className="grid flex-1 text-left gap-1">
              <Skeleton className="h-4 w-16" />
              <Skeleton className="h-3 w-24" />
            </div>
            <Loader2 className="ml-auto size-4 text-muted-foreground/50 animate-spin" />
          </SidebarMenuButton>
        </Dialog>
      </SidebarMenuItem>
    </SidebarMenu>
  );
}
