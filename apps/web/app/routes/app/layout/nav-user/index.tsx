import { NavLogin } from "./login";
import { NavUserProfile } from "./profile";
import { isAuthError } from "@supabase/supabase-js";
import { createClient } from "~/lib/supabase/.client";
import { useSuspenseQuery } from "@tanstack/react-query";
import ClientSuspense from "@/components/client-suspense";
import { NavLoginSkeleton } from "./skeleton";
export { NavLoginSkeleton } from "./skeleton";

export function NavUserLoader() {
  const { data: session } = useSuspenseQuery({
    queryKey: ["user-session"],
    queryFn: async () => {
      const supabase = createClient();
      const { data, error } = await supabase.auth.getSession();
      if (error) {
        throw error;
      }
      return data.session;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 15 * 60 * 1000, // 15 minutes
    refetchOnWindowFocus: false,
    retry: (failureCount, error) => {
      // Don't retry on 401 errors
      if (isAuthError(error) && error.status === 401) return false;
      return failureCount < 3;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });

  if (session === null) {
    return <NavLogin />;
  }
  return (
    <NavUserProfile
      user={{
        name: session.user.user_metadata.name,
        email: session.user.email || "?",
        avatar: session.user.user_metadata.avatar_url,
      }}
    />
  );
}

export default function NavUser() {
  return (
    <ClientSuspense fallback={<NavLoginSkeleton />}>
      <NavUserLoader />
    </ClientSuspense>
  );
}
