import { <PERSON>ader2, LogIn, UserRound } from "lucide-react";

import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar";
import {
  <PERSON><PERSON>,
  DialogTrigger,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { FaGithub } from "react-icons/fa";
import { Button } from "@/components/ui/button";
import { FaGoogle } from "react-icons/fa6";
import { useEffect } from "react";
import { toast } from "sonner";
import { Link, useFetcher, useLocation } from "react-router";
import type { action } from "~/routes.fs/action.user.login.$provider";

const icons = {
  github: FaGithub,
  google: FaGoogle,
} satisfies Record<string, React.ComponentType<{ className?: string }>>;

function SocialLoginButton({
  provider,
  label,
}: { provider: "google" | "github"; label: string }) {
  const { pathname: currentPath, search } = useLocation();
  const fetcher = useFetcher<typeof action>();
  const Icon = icons[provider];
  useEffect(() => {
    if (fetcher.data?.type === "error") {
      console.error(fetcher.data);
      toast.error("Error signing in with GitHub", {
        description: fetcher.data.message,
      });
    }
  }, [fetcher.data]);
  return (
    <fetcher.Form
      className="contents"
      method="post"
      action={`/action/user/login/${provider}`}
    >
      <input type="hidden" name="next" value={currentPath + search} />
      <Button
        variant="outline"
        className="w-full"
        disabled={fetcher.state !== "idle"}
        type="submit"
      >
        {fetcher.state !== "idle" ? (
          <Loader2 className="animate-spin" />
        ) : (
          <Icon />
        )}
        {label}
      </Button>
    </fetcher.Form>
  );
}

function SocialLoginForm() {
  return (
    <div className="flex flex-col gap-4">
      <SocialLoginButton provider="github" label="Login with GitHub" />
      <SocialLoginButton provider="google" label="Login with Google" />
    </div>
  );
}

export function NavLogin() {
  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <Dialog>
          <DialogTrigger asChild>
            <SidebarMenuButton size="lg" tooltip="Login / Sign up">
              <div className="relative flex size-8 shrink-0 overflow-hidden rounded-lg">
                <div className="bg-sidebar-accent text-sidebar-accent-foreground flex size-full items-center justify-center rounded-lg">
                  <UserRound className="size-4" />
                </div>
              </div>
              <div className="grid flex-1 text-left text-sm leading-tight">
                <span className="font-medium">Login</span>
                <span className="text-xs">or create an account</span>
              </div>
              <LogIn className="ml-auto size-4" />
            </SidebarMenuButton>
          </DialogTrigger>
          <DialogContent className="max-w-sm sm:max-w-sm">
            <DialogHeader className="text-center">
              <DialogTitle className="text-xl">Welcome back</DialogTitle>
              <DialogDescription>
                Login with your GitHub or Google account
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-6">
              <SocialLoginForm />
              {/* <div className="relative text-center text-sm after:absolute after:inset-0 after:top-1/2 after:z-0 after:flex after:items-center after:border-t after:border-border">
                <span className="relative z-10 bg-background px-2 text-muted-foreground">
                  Or continue with
                </span>
              </div>
              <form
                className="grid gap-6"
                action={emailLoginAction}
                method="post"
              >
                <div className="grid gap-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    autoComplete="email"
                    placeholder="<EMAIL>"
                    required
                    disabled={!emailLoginAction}
                  />
                </div>
                <div className="grid gap-2">
                  <div className="flex items-center">
                    <Label htmlFor="password">Password</Label>
                    <Link
                      href="#"
                      className="ml-auto text-sm underline-offset-4 hover:underline"
                    >
                      Forgot your password?
                    </Link>
                  </div>
                  <Input
                    id="password"
                    type="password"
                    autoComplete="current-password"
                    required
                    disabled={!emailLoginAction}
                  />
                </div>
                <Button
                  type="submit"
                  className="w-full"
                  disabled={!emailLoginAction}
                >
                  Login
                </Button>
              </form>
              <div className="text-center text-sm">
                {"Don't have an account? "}
                <Link href="#" className="underline underline-offset-4">
                  Sign up
                </Link>
              </div> */}
            </div>
            <DialogFooter>
              <div className="text-balance text-center text-xs text-muted-foreground [&_a]:underline [&_a]:underline-offset-4 hover:[&_a]:text-primary  ">
                By clicking continue, you agree to our{" "}
                <Link to="#" className="underline underline-offset-4">
                  Terms of Service
                </Link>{" "}
                and{" "}
                <Link to="#" className="underline underline-offset-4">
                  Privacy Policy
                </Link>
                .
              </div>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </SidebarMenuItem>
    </SidebarMenu>
  );
}
