import { Settings2 } from "lucide-react";

import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar";
import { SettingsDialog } from "@/components/settings/settings-dialog";

export function NavSettings() {
  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <SettingsDialog>
          <SidebarMenuButton asChild tooltip="Settings">
            <SidebarMenuButton>
              <Settings2 />
              <span>Settings</span>
            </SidebarMenuButton>
          </SidebarMenuButton>
        </SettingsDialog>
      </SidebarMenuItem>
    </SidebarMenu>
  );
}
