import {
  <PERSON><PERSON>udio,
  <PERSON>Video,
  Folder,
  Forward,
  MoreHorizontal,
  Trash2,
  type LucideIcon,
} from "lucide-react";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuAction,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@/components/ui/sidebar";
import { Link } from "react-router";
import { useRecentMedia } from "@/data/media-list/use-recents";
import { Skeleton } from "@/components/ui/skeleton";

export interface RecentItemProps {
  name: string;
  url: string;
  icon: React.ComponentType<{ className?: string }>;
}

export function NavRecents() {
  return (
    <SidebarGroup className="group-data-[collapsible=icon]:hidden">
      <SidebarGroupLabel>Recents</SidebarGroupLabel>
      <ClientSuspense fallback={<NavRecentsListLoading />}>
        <NavRecentsListData />
      </ClientSuspense>
    </SidebarGroup>
  );
}

import { FaBilibili, FaGoogleDrive, FaYoutube } from "react-icons/fa6";
import ClientSuspense from "@/components/client-suspense";

function NavRecentsListLoading() {
  return (
    <SidebarMenu>
      {[...Array(4)].map((_, i) => (
        // biome-ignore lint/suspicious/noArrayIndexKey: <explanation>
        <SidebarMenuItem key={i}>
          <SidebarMenuButton disabled>
            <FileVideo />
            <Skeleton className="h-4 w-full" />
          </SidebarMenuButton>
        </SidebarMenuItem>
      ))}
    </SidebarMenu>
  );
}

function NavRecentsListData() {
  // const { isMobile } = useSidebar();

  const items = useRecentMedia(12);

  const recents: RecentItemProps[] = items.map((item) => {
    const source = item.sources[0].dataSource.data;
    const icon =
      source.type === "url-youtube"
        ? FaYoutube
        : source.type === "url-bilibili"
          ? FaBilibili
          : source.type === "drive-google"
            ? FaGoogleDrive
            : source.metadata.media?.dimension
              ? FileVideo
              : FileAudio;
    return {
      name: item.title,
      url: `/app/media?id=${item.id}`,
      icon,
    };
  });
  return (
    <SidebarMenu>
      {recents.map((item) => (
        <SidebarMenuItem key={item.name}>
          <SidebarMenuButton asChild>
            <Link to={item.url}>
              <item.icon />
              <span>{item.name}</span>
            </Link>
          </SidebarMenuButton>
          {/* <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <SidebarMenuAction showOnHover>
            <MoreHorizontal />
            <span className="sr-only">More</span>
          </SidebarMenuAction>
        </DropdownMenuTrigger>
        <DropdownMenuContent
          className="w-48 rounded-lg"
          side={isMobile ? "bottom" : "right"}
          align={isMobile ? "end" : "start"}
        >
          <DropdownMenuItem>
            <Folder className="text-muted-foreground" />
            <span>View Project</span>
          </DropdownMenuItem>
          <DropdownMenuItem>
            <Forward className="text-muted-foreground" />
            <span>Share Project</span>
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem>
            <Trash2 className="text-muted-foreground" />
            <span>Delete Project</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu> */}
        </SidebarMenuItem>
      ))}
      {/* <SidebarMenuItem>
    <SidebarMenuButton className="text-sidebar-foreground/70">
      <MoreHorizontal className="text-sidebar-foreground/70" />
      <span>More</span>
    </SidebarMenuButton>
  </SidebarMenuItem> */}
    </SidebarMenu>
  );
}
