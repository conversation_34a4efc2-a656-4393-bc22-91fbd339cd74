import { Layers } from "lucide-react";

import { NavMain, type NavMainItemProps } from "./nav-main";
import { NavRecents } from "./nav-recents";
import { HomeLink } from "./home-link";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
} from "@/components/ui/sidebar";
import { NavSettings } from "./nav-settings";
import NavUser from "./nav-user";
import { href } from "react-router";
const navMain: NavMainItemProps[] = [
  {
    title: "Vault",
    url: href("/app/vault"),
    icon: Layers,
  },
];

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <HomeLink />
      </SidebarHeader>
      <SidebarContent className="gap-0">
        <NavMain items={navMain} />
        <NavRecents />
      </SidebarContent>
      <SidebarFooter>
        <NavSettings />
        <NavUser />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}
