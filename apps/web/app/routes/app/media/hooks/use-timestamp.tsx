import type { MediaPlayerInstance } from "@vidstack/react";

import { useStore } from "jotai";
import { useCallback } from "react";

import { toast } from "sonner";
import { mediaIdAtom } from "@/data/media-ref/query/base";
import { formatDuration } from "@mx/shared/time/format";
import { pref } from "@/db-local/pref";
import { assertNever } from "@std/assert/unstable-never";
import { urlScheme } from "@/const";

export function useCopyTimestampLink() {
  const store = useStore();

  return useCallback(
    async (player: MediaPlayerInstance) => {
      const timestamp = player.currentTime;
      const id = store.get(mediaIdAtom);
      const linktext = `${player.state.title || id}@${formatDuration(timestamp)}`;
      const paths = `/media/${id}#t=${timestamp}`;

      const linkType = await pref.get("link-style");
      const url =
        linkType === "custom-scheme"
          ? `${urlScheme}:${paths}`
          : linkType === "http"
            ? `${window.location.origin}/launch${paths}`
            : assertNever(linkType);

      const plaintext = `[${linktext}](${url})`;
      const plaintextBlob = new Blob([`[${linktext}](${url})`], {
        type: "text/plain",
      });
      const anchor = document.createElement("a");
      anchor.href = url;
      anchor.textContent = linktext;
      const htmlBlob = new Blob([anchor.outerHTML], {
        type: "text/html",
      });
      if (navigator.clipboard.write) {
        await navigator.clipboard.write([
          new ClipboardItem({
            [plaintextBlob.type]: plaintextBlob,
            [htmlBlob.type]: htmlBlob,
          }),
        ]);
        toast.success(
          `Timestamp link at ${formatDuration(timestamp)} copied to clipboard`,
        );
      } else {
        await navigator.clipboard.writeText(plaintext);
        toast.success(
          `Timestamp markdown link at ${formatDuration(timestamp)} copied to clipboard`,
        );
      }
    },
    [store],
  );
}
