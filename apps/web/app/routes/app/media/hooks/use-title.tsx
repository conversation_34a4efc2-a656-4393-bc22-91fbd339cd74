import { useAtom } from "jotai";

export default function useTitle() {
  useAtom(docTitleEffect);
}

import { atomEffect } from "jotai-effect";
import { mediaTitleAtom } from "@/data/media-ref/query/meta";
import { mediaIdAtom } from "@/data/media-ref/query/base";

const docTitleEffect = atomEffect((get) => {
  if (typeof document === "undefined") return;
  const title = get(mediaTitleAtom);
  const id = get(mediaIdAtom);
  if (!title) {
    document.title = `Media ${id}`;
  } else {
    document.title = title;
  }
});
