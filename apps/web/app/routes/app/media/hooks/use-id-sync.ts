import { use<PERSON>et<PERSON><PERSON> } from "jotai";
import { mediaIdAtom } from "@/data/media-ref/query/base";
import { queryClient } from "@/data/client";
import { query } from "@/db-local";
import { mediaRefs } from "@/db-local/schema";
import { eq } from "drizzle-orm";
import { useLocation, useNavigate, useSearchParams } from "react-router";
import { useEffect } from "react";

function useMediaIdSync() {
  const [searchParams] = useSearchParams();
  const { pathname } = useLocation();

  const id = searchParams.get("id");
  const setMediaId = useSetAtom(mediaIdAtom);
  const navigate = useNavigate();
  useEffect(() => {
    // sync media id changes to media id atom
    if (pathname === "/app/media" || pathname === "/app/media/") {
      // handle client side 404 logic
      if (!id) {
        navigate("/not-found", { replace: true });
      } else {
        queryClient.prefetchQuery({ queryKey: ["media", { id }] });
        query((db) =>
          db.$count(mediaRefs, eq(mediaRefs.id, id)).then((count) => {
            if (count === 0) navigate("/not-found", { replace: true });
            setMediaId(id);
          }),
        );
      }
    } else {
      setMediaId(null);
    }
  }, [pathname, id, navigate, setMediaId]);
}

export default useMediaIdSync;
