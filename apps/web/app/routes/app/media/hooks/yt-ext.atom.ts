import { isYouTubeProvider } from "@vidstack/react";
import { atom } from "jotai";
import { withAtomEffect } from "jotai-effect";
import { player<PERSON>tom } from "@mx/shared/hooks/use-player";
import { connectYouTubeIframe, type YouTubeConnection } from "@/lib/conn-ytb";
import { CodeError } from "@mx/shared/rpc";
import { connInitFnAtom } from "@/lib/extension";

export const youtubeExtControllerAtom = withAtomEffect(
  atom<
    | { status: "ready"; controller: YouTubeConnection }
    | { status: "pending" }
    | { status: "na" }
    | { status: "error"; error: Error }
  >({ status: "pending" }),
  (get, set) => {
    const connInitFn = get(connInitFnAtom);
    if (connInitFn.status !== "ready") {
      set(youtubeExtControllerAtom, connInitFn);
      return;
    }
    const player = get(playerAtom);
    if (!player) {
      set(youtubeExtControllerAtom, { status: "pending" });
      return;
    }
    let disposeCanPlayHandler: (() => void) | undefined;

    const disposeProviderChange = player.listen("provider-change", () => {
      disposeCanPlayHandler?.();
      disposeCanPlayHandler = undefined;
      if (!isYouTubeProvider(player.provider)) {
        const prev = get(youtubeExtControllerAtom);
        if (prev.status === "ready") {
          // prev.controller[Symbol.dispose]();
        }
        set(youtubeExtControllerAtom, { status: "pending" });
        return;
      }
      disposeCanPlayHandler = player.listen("can-play", async () => {
        const prev = get(youtubeExtControllerAtom);
        if (prev.status === "ready") {
          // prev.controller[Symbol.dispose]();
        }
        if (!isYouTubeProvider(player.provider)) return;
        try {
          const channel = await connectYouTubeIframe(
            player.provider.iframe,
            connInitFn,
          );
          console.log("Connected to YouTube iframe");
          set(youtubeExtControllerAtom, {
            status: "ready",
            controller: channel,
          });
        } catch (err) {
          if (err instanceof CodeError && err.code === "ext-na") {
            set(youtubeExtControllerAtom, { status: "na" });
            console.log("Extension not available");
          } else {
            console.error("Failed to connect to YouTube iframe", err);
            set(youtubeExtControllerAtom, {
              status: "error",
              error: err instanceof Error ? err : new Error("Unknown error"),
            });
          }
        } finally {
          // handle can-play event once
          disposeCanPlayHandler?.();
        }
      });
    });
    return () => {
      disposeProviderChange();
      disposeCanPlayHandler?.();
    };
  },
);
