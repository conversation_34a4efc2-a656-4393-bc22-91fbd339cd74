import {
  isVideoProvider,
  isYou<PERSON><PERSON><PERSON><PERSON>ider,
  type MediaProviderAdapter,
} from "@vidstack/react";

import { useAtomValue, useStore } from "jotai";
import { useCallback } from "react";

import { useMediaProvider } from "@mx/shared/hooks/use-player";
import { toast } from "sonner";
import { formatDuration } from "@mx/shared/time/format";
import {
  captureScreenshot,
  handleScreenshotResponse,
} from "@mx/shared/dom/screenshot";
import { youtubeExtControllerAtom } from "./yt-ext.atom";
import { assertNever } from "@std/assert/unstable-never";
import { pref } from "@/db-local/pref";
import { persistAtchStore } from "@/db-local/atch-store";

function useScreenshot() {
  const store = useStore();
  const capture = useCallback(
    (
      provider: MediaProviderAdapter,
      format: "image/png" | "image/webp" | "image/jpeg",
    ): Promise<{
      blob: Blob;
      timestamp: number;
    } | null> | null => {
      const youtubeController = store.get(youtubeExtControllerAtom);
      if (isVideoProvider(provider)) {
        // Regular video screenshot
        return captureScreenshot(provider.video, { format }).then(
          handleScreenshotResponse,
        );
      }
      if (isYouTubeProvider(provider)) {
        if (youtubeController.status === "pending") {
          throw new Error("Browser Extension loading, please wait...");
        }
        if (youtubeController.status === "na") {
          throw new Error(
            "Browser Extension not installed, please install it from the extension store",
          );
        }
        if (youtubeController.status === "error") {
          throw new Error(
            `Failed to load browser extension: ${youtubeController.error.message}`,
          );
        }
        if (youtubeController.status !== "ready") {
          assertNever(youtubeController);
        }
        return youtubeController.controller
          .captureScreenshot({ format })
          .then(handleScreenshotResponse);
      }
      return null;
    },
    [store],
  );
  return capture;
}

export function useCopyScreenshot() {
  const captureScreenshot = useScreenshot();
  const provider = useMediaProvider();
  const youtubeController = useAtomValue(youtubeExtControllerAtom);
  const handler = useCallback(async () => {
    if (!provider) return null;
    try {
      const task = captureScreenshot(provider, "image/png");
      if (!task) return null;
      toast.promise(
        task.then(async (payload) => {
          if (!payload) return null;
          await navigator.clipboard.write([
            new ClipboardItem({
              [payload.blob.type]: payload.blob,
            }),
          ]);
          return payload.timestamp;
        }),
        {
          loading: "Capturing screenshot...",
          success: (data) => {
            if (data !== null) {
              return `Screenshot at ${formatDuration(data)} copied to clipboard`;
            }
            return "Nothing to copy";
          },
          error: (err) => {
            if (err instanceof Error) {
              return err.message;
            }
            return "Failed to copy screenshot";
          },
        },
      );
    } catch (e) {
      toast.error(e instanceof Error ? e.message : "Failed to copy screenshot");
    }
  }, [provider, captureScreenshot]);
  if (
    typeof document === "undefined" ||
    !("clipboard" in navigator) ||
    !("write" in navigator.clipboard) ||
    !(
      isVideoProvider(provider) ||
      (isYouTubeProvider(provider) &&
        // if extension na, prompt installing extension
        youtubeController.status === "na") ||
      youtubeController.status === "ready"
    )
  )
    return undefined;
  return handler;
}

export function useSaveScreenshot() {
  const captureScreenshot = useScreenshot();
  const provider = useMediaProvider();
  const youtubeController = useAtomValue(youtubeExtControllerAtom);
  const handler = useCallback(async () => {
    if (!provider) return null;
    try {
      const task = captureScreenshot(provider, "image/webp");
      if (!task) return null;
      toast.promise(
        task.then(async (payload) => {
          if (!payload) return null;
          const filePath = await saveScreenshot(
            `screenshot-${Date.now()}.webp`,
            payload.blob,
          );
          const alt = `Screenshot at ${formatDuration(payload.timestamp)}`;
          await navigator.clipboard.writeText(`![${alt}](${filePath})`);
          return {
            timestamp: payload.timestamp,
            filePath,
          };
        }),
        {
          loading: "Capturing screenshot...",
          success: (data) => {
            if (data !== null) {
              return `Screenshot at ${formatDuration(data.timestamp)} saved in ${data.filePath}`;
            }
            return "Nothing to save";
          },
          error: (err) => {
            if (err instanceof Error) {
              return err.message;
            }
            return "Failed to save screenshot";
          },
        },
      );
    } catch (e) {
      toast.error(e instanceof Error ? e.message : "Failed to copy screenshot");
    }
  }, [provider, captureScreenshot]);
  if (
    typeof navigator === "undefined" ||
    !navigator.clipboard.write ||
    !(
      isVideoProvider(provider) ||
      (isYouTubeProvider(provider) &&
        // if extension na, prompt installing extension
        youtubeController.status === "na") ||
      youtubeController.status === "ready"
    )
  )
    return undefined;
  return handler;
}

async function saveScreenshot(fileName: string, blob: Blob) {
  const attachmentDirConfigs = await pref.get("attachment-dir");
  const attachmentDirConfig = attachmentDirConfigs?.[0];
  if (!attachmentDirConfig) {
    toast.warning("No attachment directory set, go to settings");
    return;
  }
  const dir = await persistAtchStore.get(attachmentDirConfig.dataId);
  if (!dir || dir.type !== "fs-dir") {
    throw new Error("No attachment directory available, go to settings");
  }
  const attachmentDir: FileSystemDirectoryHandle = dir.data;
  const state = await attachmentDir.requestPermission({
    mode: "readwrite",
  });
  if (state !== "granted") {
    throw new Error("No permission to write to attachment directory");
  }
  const fileHandle = await attachmentDir.getFileHandle(fileName, {
    create: true,
  });
  const writable = await fileHandle.createWritable();
  await writable.write(blob);
  await writable.close();
  return `${attachmentDirConfig.path}/${fileHandle.name}`;
}
