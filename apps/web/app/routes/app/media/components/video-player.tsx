import type { PlayerSrc } from "@vidstack/react";
import { MediaProvider } from "@vidstack/react";
import { AudioLayout, VideoLayout, MediaPlayer } from "@/components/player";

import { useAtomValue } from "jotai";
import { useEffect } from "react";

import { useMediaPlayer, usePlayerInitRef } from "@mx/shared/hooks/use-player";
import { mediaTitleAtom } from "@/data/media-ref/query/meta";
import Tracks from "@/components/tracks/tracks";
import { useActiveSrcLoadInfo } from "@/data/media-ref/query/use-src";
import { useCopyScreenshot } from "../hooks/use-screenshot";
import { useCopyTimestampLink } from "../hooks/use-timestamp";
import { mediaIdAtom } from "@/data/media-ref/query/base";
import { parseHashProps } from "@mx/shared/utils/hash-prop";
import { isTimestamp } from "@mx/shared/time/temporal-frag";

export default function Player() {
  const src = usePlayerSrc();
  const ref = usePlayerInitRef();
  useInitHash();

  const handleTimestamp = useCopyTimestampLink();
  const handleScreenshot = useCopyScreenshot();

  return (
    <MediaPlayer
      suppressHydrationWarning
      logLevel={process.env.NODE_ENV === "development" ? "debug" : "error"}
      ref={ref}
      crossOrigin
      {...src}
      onTimestamp={handleTimestamp}
      onScreenshot={handleScreenshot}
    >
      <MediaProvider iframeProps={{ credentialless: "" } as any}>
        <Tracks />
      </MediaProvider>
      <VideoLayout />
      <AudioLayout />
    </MediaPlayer>
  );
}

function useInitHash() {
  const player = useMediaPlayer();
  const id = useAtomValue(mediaIdAtom);
  useEffect(() => {
    // on media id changes, read initial hash params
    void id;
    if (typeof document === "undefined" || !window.location.hash) return;
    const hash = window.location.hash;
    const hashParams = parseHashProps(hash);
    if (player && isTimestamp(hashParams?.tempFragment)) {
      // player.play();
      player.currentTime = hashParams.tempFragment.start;
    }
  }, [id, player]);
}

function usePlayerSrc() {
  const title = useAtomValue(mediaTitleAtom);
  const loadInfo = useActiveSrcLoadInfo();
  if (!loadInfo)
    return {
      title,
      src: "",
      aspectRatio: "16/9",
    };
  let type = loadInfo.type;
  // compatible with vidstack, it expects limited mime types but only uses it to determine layout
  // so we only need to set it to a compatible type and let html player to check if it's playable
  if (
    loadInfo.type !== "video/youtube" &&
    loadInfo.type !== "video/vimeo" &&
    loadInfo.type !== "video/remotion"
  ) {
    type = loadInfo.type.startsWith("video/") ? "video/mp4" : "audio/mp3";
  }
  const src = { src: loadInfo.src, type: type as any } as PlayerSrc;
  return {
    src,
    title,
    aspectRatio: loadInfo.aspectRatio || "16 / 9",
  };
}
