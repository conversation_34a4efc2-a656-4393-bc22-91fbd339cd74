import { format } from "date-fns";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import {
  Clock,
  Layers,
  MoreHorizontal,
  Pen,
  Play,
  Plus,
  Trash,
} from "lucide-react";
import { formatDuration } from "@mx/shared/time/format";
import { useAtomValue } from "jotai";
import { mediaMetadataAtom } from "@/data/media-ref/query/meta";
import { Skeleton } from "@/components/ui/skeleton";
import MediaSourcesLabel from "./src-label";

function MediaMetadataData() {
  const metadata = useAtomValue(mediaMetadataAtom);
  if (!metadata) return <MediaMetadataSkeleton />;

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-start gap-2 text-balance">
          <CardTitle className="text-lg">{metadata.title}</CardTitle>
          <MoreOptionsMenu />
        </div>
        {metadata.description ? (
          <ScrollArea className="">
            <CardDescription className="whitespace-pre-wrap max-h-24">
              {metadata.description}
            </CardDescription>
          </ScrollArea>
        ) : (
          <CardDescription>No description available</CardDescription>
        )}
      </CardHeader>

      {/* <CardContent>
        <div className="flex flex-wrap gap-2">
          {metadata.tags.map((tag) => (
            <Badge
              key={tag}
              variant="secondary"
              className="px-3 py-1 bg-gray-100 text-gray-700 hover:bg-gray-200 cursor-pointer"
            >
              {tag}
            </Badge>
          ))}
        </div>
      </CardContent> */}
      <CardFooter>
        <div className="flex items-center text-sm text-gray-500 h-4">
          <span className="flex items-center">
            <Clock className="mr-2 size-4 inline" />
            {format(metadata.createdAt, "MMM dd, yyyy")}
          </span>
          <Separator orientation="vertical" className="mx-3" />
          <span className="flex items-center">
            <Play className="mr-2 size-4 inline" />
            {formatDuration(metadata.duration)}
          </span>
          <Separator orientation="vertical" className="ml-3 mr-1" />
          <MediaSourcesLabel />
        </div>
      </CardFooter>
    </Card>
  );
}

import * as SourceSelect from "@/components/src-select";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";
import ClientSuspense from "@/components/client-suspense";
import { ScrollArea } from "@/components/ui/scroll-area";
import { removeMediaRefMutation } from "@/data/media-ref/mut/remove.atom";
import { mediaIdAtom } from "@/data/media-ref/query/base";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { href, useNavigate } from "react-router";

function MoreOptionsMenu() {
  const id = useAtomValue(mediaIdAtom);
  const { mutateAsync: removeMediaRef } = useAtomValue(removeMediaRefMutation);
  const navigate = useNavigate();
  return (
    <Sheet>
      <AlertDialog>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="iconSm" className="mb-2">
              <MoreHorizontal />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="">
            <SheetTrigger asChild>
              <DropdownMenuItem>
                <Layers />
                Manage sources
              </DropdownMenuItem>
            </SheetTrigger>
            <DropdownMenuSeparator />
            <DropdownMenuItem disabled>
              <Pen />
              Edit metadata
            </DropdownMenuItem>
            <AlertDialogTrigger asChild>
              <DropdownMenuItem disabled={!id} variant="destructive">
                <Trash />
                Delete from vault
              </DropdownMenuItem>
            </AlertDialogTrigger>
          </DropdownMenuContent>
        </DropdownMenu>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              variant="destructive"
              onClick={() =>
                id &&
                removeMediaRef(id).then(() => {
                  navigate(href("/app"));
                })
              }
              disabled={!id}
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      <SheetContent>
        <SheetHeader>
          <SheetTitle>Manage sources</SheetTitle>
          <SheetDescription>
            You could assign multiple sources pointing to the same media as
            backup.
          </SheetDescription>
        </SheetHeader>
        <SourceSelect.Root>
          <div className="flex flex-col gap-4 px-4">
            <div className="group/src-list w-full max-w-2xl border rounded-lg overflow-hidden divide-y">
              <SourceSelect.SourceListContent />
            </div>
            <div className="flex justify-end items-center">
              <SourceSelect.AddNewButton>
                <Button>
                  <Plus />
                  Link new
                </Button>
              </SourceSelect.AddNewButton>
            </div>
          </div>
        </SourceSelect.Root>
      </SheetContent>
    </Sheet>
  );
}

function MediaMetadataSkeleton() {
  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-start gap-2">
          <div className="space-y-2 flex-1">
            <Skeleton className="h-6 w-3/4" />
            <div className="space-y-1.5 mr-4">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-2/3" />
            </div>
          </div>
          <Skeleton className="size-9 rounded-md" />
        </div>
      </CardHeader>
      <CardFooter>
        <div className="flex items-center text-sm text-gray-500 gap-3 h-4">
          <div className="flex items-center gap-2">
            <Clock className="size-4 text-muted-foreground/50" />
            <Skeleton className="h-4 w-24" />
          </div>
          <Separator orientation="vertical" />
          <div className="flex items-center gap-2">
            <Play className="size-4 text-muted-foreground/50" />
            <Skeleton className="h-4 w-16" />
          </div>
          <Separator orientation="vertical" />
          <Skeleton className="h-4 w-16" />
        </div>
      </CardFooter>
    </Card>
  );
}

export default function MediaMetadata() {
  return (
    <ClientSuspense fallback={<MediaMetadataSkeleton />}>
      <MediaMetadataData />
    </ClientSuspense>
  );
}
