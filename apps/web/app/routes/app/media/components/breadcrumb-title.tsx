import { BreadcrumbItem, BreadcrumbPage } from "@/components/ui/breadcrumb";
import { Skeleton } from "@/components/ui/skeleton";
import { mediaTitleAtom } from "@/data/media-ref/query/meta";
import { useAtomValue } from "jotai";
import { Suspense } from "react";

function Data() {
  const title = useAtomValue(mediaTitleAtom);
  return <BreadcrumbPage>{title}</BreadcrumbPage>;
}

function Loading() {
  return (
    <BreadcrumbPage>
      <Skeleton className="w-20 h-4" />
    </BreadcrumbPage>
  );
}

export default function MediaTitleBreadcrumbItem() {
  return (
    <BreadcrumbItem>
      <Suspense fallback={<Loading />}>
        <Data />
      </Suspense>
    </BreadcrumbItem>
  );
}
