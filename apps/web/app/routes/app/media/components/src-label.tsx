import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { But<PERSON> } from "@/components/ui/button";
import { Link } from "lucide-react";
import { useAtomValue } from "jotai";
import { mediaMetadataAtom } from "@/data/media-ref/query/meta";
import { toURL } from "@mx/shared/utils/to-url";

export default function MediaSourcesLabel() {
  const metadata = useAtomValue(mediaMetadataAtom);
  if (!metadata) return null;
  const sources = metadata.sources;

  if (
    sources.length === 1 ||
    // all sources are not linkable
    sources.every((source) => !getSourceLabel(source).href)
  ) {
    const sourceInfo = getSourceLabel(sources[0]);
    const Icon = sourceInfo.icon;
    if (!sourceInfo.href) {
      return (
        <span className="ml-2 flex items-center max-w-24 truncate">
          <Icon className="mr-2 size-4 inline" />
          {sourceInfo.label}
        </span>
      );
    }
    return (
      <Button
        asChild
        size="iconTextSm"
        variant="link"
        className="cursor-pointer"
      >
        <a href={sourceInfo.href} target="_blank" rel="noopener noreferrer">
          <Link />
          {sourceInfo.label}
        </a>
      </Button>
    );
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button size="iconTextSm" variant="ghost">
          <Link />
          <span className="truncate max-w-24">{getSourcesLabel(sources)}</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start" className="w-48">
        {sources.map((source) => {
          const sourceInfo = getSourceLabel(source);
          const Icon = sourceInfo.icon;
          if (!sourceInfo.href) return null;
          return (
            <DropdownMenuItem key={source.id} asChild>
              <a
                href={sourceInfo.href}
                target="_blank"
                rel="noopener noreferrer"
              >
                <Icon className="mr-1" /> {sourceInfo.label}
              </a>
            </DropdownMenuItem>
          );
        })}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

import { FileVideo } from "lucide-react";
import { FaBilibili, FaGoogleDrive, FaYoutube } from "react-icons/fa6";
import { SiCoursera } from "react-icons/si";
import { assertNever } from "@std/assert/unstable-never";
import type { DataSource } from "@/db-local/schema/data-types";

export interface SourceLabelInfo {
  icon: React.ComponentType<{ className?: string }>;
  label: string;
  href?: string;
}

export function getSourceLabel(source: { data: DataSource }): SourceLabelInfo {
  const data = source.data;

  if (data.type === "url-youtube") {
    return {
      icon: FaYoutube,
      label: "YouTube",
      href: data.url,
    };
  }
  if (data.type === "url-bilibili") {
    return {
      icon: FaBilibili,
      label: "Bilibili",
      href: data.url,
    };
  }
  if (data.type === "url-coursera") {
    return {
      icon: SiCoursera,
      label: "Coursera",
      href: data.url,
    };
  }
  if (data.type === "url-file") {
    const hostname = toURL(data.url)?.hostname;
    return {
      icon: FileVideo,
      label: hostname ?? "Remote URL",
      href: data.url,
    };
  }
  if (data.type === "drive-google") {
    return {
      icon: FaGoogleDrive,
      label: "Google Drive",
      href: `https://drive.google.com/file/d/${data.driveId}/view`,
    };
  }
  if (
    data.type === "file-handle" ||
    data.type === "file-ref" ||
    data.type === "file-inline"
  ) {
    return {
      icon: FileVideo,
      label: data.metadata.name,
    };
  }
  assertNever(data.type);
}

export function getSourcesLabel(sources: { data: DataSource }[]): string {
  return sources.map((source) => getSourceLabel(source).label).join(", ");
}
