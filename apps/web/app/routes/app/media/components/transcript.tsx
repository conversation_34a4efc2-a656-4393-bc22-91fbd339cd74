import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Plus, Trash, MoreHorizontal } from "lucide-react";
import { toast } from "sonner";
import type { TextTrack } from "@vidstack/react";
import * as AddTrackMenu from "@/components/tracks/track-menu";
import { TextTrackSelector } from "@/components/tracks/track-selector";
import { useMediaPlayerRef, useMediaStore } from "@mx/shared/hooks/use-player";
import { useAtomValue } from "jotai";
import { removeMediaTrackAtom } from "@/data/track/mut/remove.atom";
import { useWebVTTData } from "./webvtt";
import { useRef } from "react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { cn } from "@/lib/utils";
import WebVTTEditor from "@/components/vtt-editor/editor";
import ClientSuspense from "@/components/client-suspense";
import { Skeleton } from "@/components/ui/skeleton";

type Transcript = {
  id: string;
  title: string;
  content: string;
};

function TranscriptData() {
  const { textTracks, textTrack } = useMediaStore();
  const hasTextTracks = textTracks.length > 0;
  const textTrackData = useWebVTTData();
  const containerRef = useRef<HTMLDivElement>(null);
  return (
    <Card
      data-no-track={textTracks.length === 0 ? "" : undefined}
      data-active-track={textTrack ? "" : undefined}
    >
      <CardHeader className="flex flex-row justify-center pb-4 pr-4">
        <CardTitle className="flex justify-center items-center">
          <span className={cn(textTrack && "sr-only")}>Transcript</span>
          {textTrack && <TrackSelector />}
        </CardTitle>
        <div
          className="flex flex-1 justify-end items-center"
          data-slot="card-header-controls"
        >
          {textTrack && (
            <>
              <AddTrackHeaderMenu />
              <MoreOptionsMenu />
            </>
          )}
        </div>
      </CardHeader>
      <CardContent ref={containerRef} className="max-h-96 overflow-y-auto px-5">
        {textTrack && textTrackData ? (
          <WebVTTEditor
            defaultContent={textTrackData}
            getScrollContainer={() => containerRef.current}
          />
        ) : (
          <div className="flex flex-col items-center justify-center h-40 space-y-4">
            {hasTextTracks && <TrackSelector />}
            <AddTrackMainMenu />
          </div>
        )}
      </CardContent>
    </Card>
  );
}

function TranscriptSkeleton() {
  return (
    <Card>
      <CardHeader className="flex flex-row justify-center pb-4 pr-4">
        <CardTitle className="flex justify-center items-center">
          <span>Transcript</span>
        </CardTitle>
        <div
          className="flex flex-1 justify-end items-center"
          data-slot="card-header-controls"
        >
          <Skeleton className="h-8 w-8 rounded-md" />
        </div>
      </CardHeader>
      <CardContent className="max-h-96 overflow-y-auto px-5">
        <div className="flex flex-col items-center justify-center h-30 space-y-4">
          <div className="w-full space-y-2">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-5/6" />
            <Skeleton className="h-4 w-4/6" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-3/4" />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export default function Transcript() {
  return (
    <ClientSuspense fallback={<TranscriptSkeleton />}>
      <TranscriptData />
    </ClientSuspense>
  );
}

function AddTrackHeaderMenu() {
  const playerRef = useMediaPlayerRef();
  return (
    <AddTrackMenu.Root
      onTrackAdded={async (trackId) => {
        const newTrack = await new Promise<TextTrack | null>((resolve) => {
          playerRef.current?.textTracks.addEventListener(
            "add",
            (evt) => resolve(evt.target.getById(trackId)),
            { once: true },
          );
        });
        if (!newTrack) return;
        await new Promise((resolve) => {
          newTrack.addEventListener("load-start", resolve, { once: true });
        });
        newTrack.setMode("showing");
        toast.success("Subtitle added");
      }}
    >
      <AddTrackMenu.MenuTrigger asChild>
        <Button variant="ghost" size="iconSm">
          <Plus />
          <span className="sr-only">Add Subtitle</span>
        </Button>
      </AddTrackMenu.MenuTrigger>
      <AddTrackMenu.MenuContent align="end">
        <AddTrackMenu.FileImportOption />
        <AddTrackMenu.TranscribeOption />
      </AddTrackMenu.MenuContent>
    </AddTrackMenu.Root>
  );
}

function AddTrackMainMenu() {
  const playerRef = useMediaPlayerRef();

  return (
    <AddTrackMenu.Root
      onTrackAdded={async (trackId) => {
        const newTrack = await new Promise<TextTrack | null>((resolve) => {
          playerRef.current?.textTracks.addEventListener(
            "add",
            (evt) => resolve(evt.target.getById(trackId)),
            { once: true },
          );
        });
        if (!newTrack) return;
        await new Promise((resolve) => {
          newTrack.addEventListener("load-start", resolve, { once: true });
        });
        newTrack.setMode("showing");
        toast.success("Subtitle added");
      }}
    >
      <AddTrackMenu.MenuTrigger asChild>
        <Button type="button">
          <Plus /> Add New
        </Button>
      </AddTrackMenu.MenuTrigger>
      <AddTrackMenu.MenuContent>
        <AddTrackMenu.FileImportOption />
        <AddTrackMenu.TranscribeOption />
      </AddTrackMenu.MenuContent>
    </AddTrackMenu.Root>
  );
}

function TrackSelector() {
  const { textTracks, textTrack } = useMediaStore();
  return (
    <TextTrackSelector
      className="mr-2"
      textTracks={textTracks}
      activeTrackId={textTrack?.id}
      onSelect={(id) => {
        for (const track of textTracks) {
          track.setMode(track.id === id ? "showing" : "hidden");
        }
      }}
    />
  );
}

function MoreOptionsMenu() {
  const { textTrack } = useMediaStore();
  const activeTrackId = textTrack?.id;
  const playerRef = useMediaPlayerRef();
  const { mutateAsync: removeTrack, isPending } =
    useAtomValue(removeMediaTrackAtom);

  if (!activeTrackId) return null;

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="iconSm">
          <MoreHorizontal />
          <span className="sr-only">More options</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem
          variant="destructive"
          disabled={isPending}
          onClick={async () => {
            await removeTrack({ id: activeTrackId });
            const textTracks = playerRef.current?.textTracks;
            if (!textTracks) return;
            const track = textTracks.getById(activeTrackId);
            if (!track) return;
            textTracks.remove(track);
            toast.success("Track removed");
          }}
        >
          <Trash className="mr-1" />
          Remove
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
