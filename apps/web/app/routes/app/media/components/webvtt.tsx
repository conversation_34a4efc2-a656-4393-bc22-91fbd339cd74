import { useEffect, useState } from "react";
import { parseVtt } from "@/components/vtt-editor/parse";
import type { NodeJSON } from "prosekit/core";
import { useMediaPlayer } from "@mx/shared/hooks/use-player";

export function useWebVTTData() {
  const player = useMediaPlayer();
  const [data, setData] = useState<NodeJSON | null>(null);
  useEffect(() => {
    if (!player) return;
    return player.subscribe(({ textTrack }) => {
      if (!textTrack) {
        setData(null);
        return;
      }
      const load = () => {
        setData({
          type: "doc",
          content: parseVtt(textTrack.cues),
        });
      };
      if (textTrack.readyState === 2) {
        load();
        return;
      }
      if (textTrack.readyState === 1) {
        textTrack.addEventListener("load", load, { once: true });
        return () => {
          textTrack.removeEventListener("load", load);
        };
      }
      console.error("textTrack cannot become ready", textTrack.readyState);
    });
  }, [player]);
  return data;
}
