// import { ScopeProvider } from "jotai-scope";
// import { mediaId<PERSON>tom } from "@/data/media-ref/query/base";
// import { player<PERSON>tom, playerRef<PERSON><PERSON> } from "@mx/shared/hooks/use-player";
import useTitle from "./hooks/use-title";
import useMediaIdSync from "./hooks/use-id-sync";

export default function MediaPageLayout({
  children,
}: { children: React.ReactNode }) {
  useTitle();
  return (
    // <ScopeProvider atoms={[playerRef<PERSON>tom, playerAtom]}>
    <AtomHookLoader>
      <main className="container mx-auto p-4 space-y-6">{children}</main>
    </AtomHookLoader>
  );
}

function AtomHookLoader({ children }: { children: React.ReactNode }) {
  useTitle();
  useMediaIdSync();
  return children;
}
