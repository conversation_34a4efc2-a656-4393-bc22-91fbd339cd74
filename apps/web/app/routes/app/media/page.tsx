import VideoPlayer from "./components/video-player";
import Metadata from "./components/metadata";
import Transcript from "./components/transcript";
import type { BreadcrumbHandle } from "../layout/route";
import {
  Breadcrumb,
  BreadcrumbList,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Link, href } from "react-router";
import MediaPageLayout from "./layout";
import MediaTitleBreadcrumbItem from "./components/breadcrumb-title";

export const handle: BreadcrumbHandle = {
  breadcrumb: (
    <Breadcrumb>
      <BreadcrumbList>
        <BreadcrumbItem className="hidden md:block">
          <BreadcrumbLink asChild>
            <Link to={href("/app/vault")}>Media</Link>
          </BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbSeparator className="hidden md:block" />
        <MediaTitleBreadcrumbItem />
      </BreadcrumbList>
    </Breadcrumb>
  ),
};

export default function Home() {
  return (
    <MediaPageLayout>
      <div className="grid grid-cols-1 lg:grid-cols-[minmax(0,2fr)_minmax(24rem,1fr)] lg:grid-rows-2 gap-6 mx-auto">
        {/* Video Player */}
        <div className="lg:col-span-1 lg:row-start-1">
          <VideoPlayer />
        </div>

        {/* Transcript */}
        <div className="order-1 lg:order-none lg:col-start-2 lg:row-span-2">
          <Transcript />
        </div>

        {/* Metadata */}
        <div className="lg:col-span-1 lg:row-start-2 order-2 lg:order-none">
          <Metadata />
        </div>
      </div>
    </MediaPageLayout>
  );
}
