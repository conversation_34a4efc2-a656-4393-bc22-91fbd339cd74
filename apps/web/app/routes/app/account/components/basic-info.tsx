import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON>,
  CardH<PERSON>er,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { Dialog, DialogTrigger } from "@/components/ui/dialog";
import { Camera, UserRound } from "lucide-react";
import DeleteAccountDialog from "./dialog/delete-account";
import { Skeleton } from "@/components/ui/skeleton";
import DateClient from "@/components/date";
import { Suspense, use } from "react";
import { FormControl, FormItem, FormLabel } from "@/components/ui/form-item";

export interface UserProfile {
  avatar?: string;
  name?: string;
  email?: string;
  lastLogin?: string;
}

export interface UserProfileProps {
  profile: Promise<{
    profile: UserProfile;
  }>;
}

function UserProfile({ profile: p }: UserProfileProps) {
  const {
    profile: { avatar, email, lastLogin, name },
  } = use(p);
  return (
    <>
      <div className="flex items-center space-x-6">
        <Avatar className="size-10">
          <AvatarImage src={avatar} alt={name} />
          <AvatarFallback>
            <UserRound />
          </AvatarFallback>
        </Avatar>
        <Button variant="outline" disabled>
          <Camera />
          Change Photo
        </Button>
      </div>
      <div className="space-y-4">
        <FormItem>
          <FormLabel>Full Name</FormLabel>
          <FormControl>
            <Input className="w-48" name="name" defaultValue={name} disabled />
          </FormControl>
        </FormItem>
        <FormItem>
          <FormLabel>Email</FormLabel>
          <FormControl>
            <Input
              className="w-48"
              name="email"
              defaultValue={email}
              disabled
            />
          </FormControl>
        </FormItem>
        {lastLogin && (
          <div className="text-sm text-muted-foreground flex items-center gap-2">
            <span>Last login:</span>
            <DateClient date={lastLogin}>
              <Skeleton className="w-24 h-4" />
            </DateClient>
          </div>
        )}
      </div>
    </>
  );
}
function UserProfileSkeleton() {
  return (
    <>
      <div className="flex items-center space-x-6">
        <Avatar className="size-10">
          <AvatarFallback>
            <UserRound />
          </AvatarFallback>
        </Avatar>
        <Button variant="outline" disabled>
          <Camera />
          Change Photo
        </Button>
      </div>
      <div className="space-y-4">
        <FormItem>
          <FormLabel>Full Name</FormLabel>
          <FormControl>
            <div className="flex items-center gap-2 h-9 w-48">
              <Skeleton className="w-full h-4" />
            </div>
          </FormControl>
        </FormItem>
        <FormItem>
          <FormLabel>Email</FormLabel>
          <FormControl>
            <div className="flex items-center gap-2 h-9 w-48">
              <Skeleton className="w-full h-4" />
            </div>
          </FormControl>
        </FormItem>
        <div className="text-sm text-muted-foreground flex items-center gap-2">
          <span>Last login:</span>
          <Skeleton className="w-24 h-4" />
        </div>
      </div>
    </>
  );
}

export default function BasicInfo(props: UserProfileProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Profile Information</CardTitle>
        <CardDescription>
          Manage your account details and preferences
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <Suspense fallback={<UserProfileSkeleton />}>
          <UserProfile {...props} />
        </Suspense>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Dialog>
          <DialogTrigger asChild>
            <Button
              variant="link"
              className="text-muted-foreground not-disabled:hover:text-destructive px-1 not-disabled:cursor-pointer"
            >
              Delete my account
            </Button>
          </DialogTrigger>
          <DeleteAccountDialog />
        </Dialog>
        <Button disabled>Save Changes</Button>
      </CardFooter>
    </Card>
  );
}
