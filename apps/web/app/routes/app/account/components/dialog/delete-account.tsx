import { <PERSON><PERSON>, <PERSON><PERSON>Title, AlertDescription } from "@/components/ui/alert";
import { But<PERSON> } from "@/components/ui/button";
import {
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "@/components/ui/form-item";
import { Input } from "@/components/ui/input";
import { AlertTriangle } from "lucide-react";
import { useEffect } from "react";
import { toast } from "sonner";
import { href, useFetcher, useNavigate } from "react-router";
import type { action, DeleteUserState } from "~/routes.fs/action.user.delete";
import clientLogout from "~/lib/auth/logout.client";
import { deleteUserConfirm } from "@/const";
import DelayedSpinner from "@/components/delayed-spinner";

export default function DeleteAccountDialog() {
  const fetcher = useFetcher<DeleteUserState>();
  const isLoading = fetcher.state !== "idle";
  const navigate = useNavigate();
  useEffect(() => {
    if (fetcher.data?.type === "error") {
      toast.error("Failed to delete account", {
        description: fetcher.data.message,
      });
    } else if (fetcher.data?.type === "success") {
      toast.success("Account deleted successfully, signing out...");
      clientLogout({ clientSideKeys: fetcher.data.clientSideKeys }).then(() => {
        navigate(href("/app"), { replace: true });
      });
    }
  }, [fetcher.data, navigate]);
  return (
    <DialogContent>
      <fetcher.Form
        action="/action/user/delete"
        method="post"
        className="contents"
      >
        <DialogHeader>
          <DialogTitle className="text-destructive">Delete Account</DialogTitle>
          <DialogDescription>
            Permanently delete your account and all associated data
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4">
          <Alert variant="destructive">
            <AlertTitle className="flex items-center gap-2">
              <AlertTriangle className="size-4" />
              Warning
            </AlertTitle>
            <AlertDescription>
              This action cannot be undone. <br />
              All your data will be permanently removed.
            </AlertDescription>
          </Alert>
          <FormItem>
            <FormLabel>Your email</FormLabel>
            <FormControl>
              <Input
                name="email"
                type="email"
                placeholder="Enter your email"
                className="mt-1"
              />
            </FormControl>
            <FormMessage />
          </FormItem>
          <FormItem>
            <FormLabel>
              Type <em>delete my account</em> below to confirm
            </FormLabel>
            <FormControl>
              <Input name="confirm" placeholder={deleteUserConfirm} required />
            </FormControl>
            <FormMessage />
          </FormItem>
        </div>
        <DialogFooter>
          <Button disabled={isLoading} variant="destructive" type="submit">
            {isLoading && <DelayedSpinner />}
            {isLoading ? "Deleting..." : "Delete this account"}
          </Button>
        </DialogFooter>
      </fetcher.Form>
    </DialogContent>
  );
}
