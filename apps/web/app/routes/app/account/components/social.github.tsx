import { Button } from "@/components/ui/button";
import { FaGithub } from "react-icons/fa6";
import { cn } from "@/lib/utils";
import { Skeleton } from "@/components/ui/skeleton";
import { useFetcher, useLocation } from "react-router";
import type { action as linkAction } from "~/routes.fs/action.user.link.$provider";
import { Suspense, use, useEffect } from "react";
import { toast } from "sonner";

function ConnectGitHubButton({ className }: { className?: string }) {
  const fetcher = useFetcher<typeof linkAction>();
  const { pathname, search } = useLocation();
  useEffect(() => {
    if (fetcher.data?.type === "error") {
      toast.error(fetcher.data.message);
    }
  }, [fetcher.data]);
  return (
    <fetcher.Form
      className="contents"
      method="post"
      action="/action/user/link/github"
    >
      <input type="hidden" name="next" value={pathname + search} />
      <Button type="submit" className={className}>
        Connect
      </Button>
    </fetcher.Form>
  );
}

export interface GitHubAccountProps {
  profile: Promise<{
    connected: {
      github: boolean;
    };
  }>;
}

export default function GitHubAccount(props: GitHubAccountProps) {
  return (
    <Suspense fallback={<GitHubAccountSkeleton />}>
      <GitHubAccountData {...props} />
    </Suspense>
  );
}

function GitHubAccountData({ profile }: GitHubAccountProps) {
  const {
    connected: { github: connected },
  } = use(profile);
  return (
    <div className="flex items-center justify-between p-4">
      <div className="flex items-center space-x-4">
        <FaGithub className="size-6" />
        <div>
          <h4 className="font-medium">GitHub</h4>
          <p className="text-sm text-muted-foreground">
            {connected ? "Connected" : "Not connected"}
          </p>
        </div>
      </div>
      <ConnectGitHubButton className={cn(connected && "hidden")} />
    </div>
  );
}

function GitHubAccountSkeleton() {
  return (
    <div className="flex items-center justify-between p-4">
      <div className="flex items-center space-x-4">
        <FaGithub className="size-6 text-muted-foreground/50" />
        <div>
          <h4 className="font-medium">GitHub</h4>
          <Skeleton className="h-4 w-24 mt-1" />
        </div>
      </div>
      <Skeleton className="h-9 w-24" />
    </div>
  );
}
