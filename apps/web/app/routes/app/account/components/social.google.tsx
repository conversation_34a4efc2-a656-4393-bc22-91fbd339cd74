import { But<PERSON> } from "@/components/ui/button";
import { FaGoogle, FaGoogleDrive } from "react-icons/fa6";
import { cn } from "@/lib/utils";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Skeleton } from "@/components/ui/skeleton";
import { Suspense, use, useEffect } from "react";
import type { AccessTokenStatus } from "~/lib/oauth2/token.def";
import { googleDriveFileScope } from "@/const";
import { useFetcher, useLocation } from "react-router";
import type { action as linkAction } from "~/routes.fs/action.user.link.$provider";
import type { action as oauthAction } from "~/routes.fs/action.oauth.google";
import { toast } from "sonner";
import DelayedSpinner from "@/components/delayed-spinner";

function GoogleDriveStatus({ className }: { className?: string }) {
  return (
    <Tooltip>
      <TooltipTrigger className={cn(className)}>
        <FaGoogleDrive className="size-4" />
        <span className="sr-only">Connected to Google Drive</span>
      </TooltipTrigger>
      <TooltipContent side="bottom" align="center">
        Connected to Google Drive
      </TooltipContent>
    </Tooltip>
  );
}

function RequestGoogleDriveButton({ className }: { className?: string }) {
  const fetcher = useFetcher<typeof oauthAction>();
  const { pathname, search } = useLocation();
  useEffect(() => {
    if (fetcher.data?.type === "error") {
      toast.error(fetcher.data.message);
    }
  }, [fetcher.data]);
  return (
    <fetcher.Form
      className="contents"
      method="post"
      action="/action/oauth/google"
    >
      <input type="hidden" name="next" value={pathname + search} />
      <input type="hidden" name="scope" value={googleDriveFileScope} />
      <input type="hidden" name="prompt" value="consent" />
      <Button variant="outline" type="submit" className={className}>
        <FaGoogleDrive />
        Grant Google Drive Access
      </Button>
    </fetcher.Form>
  );
}
function ConnectGoogleButton({ className }: { className?: string }) {
  const fetcher = useFetcher<typeof linkAction>();
  const { pathname, search } = useLocation();
  useEffect(() => {
    if (fetcher.data?.type === "error") {
      toast.error(fetcher.data.message);
    }
  }, [fetcher.data]);
  return (
    <fetcher.Form
      className="contents"
      method="post"
      action="/action/user/link/google"
    >
      <input type="hidden" name="next" value={pathname + search} />
      <Button type="submit" className={className}>
        {fetcher.state !== "idle" && <DelayedSpinner />}
        Connect
      </Button>
    </fetcher.Form>
  );
}

export interface GoogleAccountProps {
  profile: Promise<{
    connected: {
      google: boolean;
    };
  }>;
  googleToken: Promise<{
    tokenStatus: AccessTokenStatus;
  }>;
}

export default function GoogleAccount(props: GoogleAccountProps) {
  return (
    <Suspense fallback={<GoogleAccountSkeleton />}>
      <GoogleAccountData {...props} />
    </Suspense>
  );
}

function GoogleAccountData({ profile, googleToken }: GoogleAccountProps) {
  const {
    connected: { google: connected },
  } = use(profile);
  const { tokenStatus } = use(googleToken);
  return (
    <div className="flex items-center justify-between p-4">
      <div className="flex items-center space-x-4">
        <FaGoogle className="size-6" />
        <div>
          <div className="flex items-center gap-2">
            <h4 className="font-medium">Google</h4>
            <GoogleDriveStatus
              className={cn(tokenStatus !== "granted" && "hidden")}
            />
          </div>
          <p className="text-sm text-muted-foreground">
            {connected ? "Connected" : "Not connected"}
          </p>
        </div>
      </div>
      <RequestGoogleDriveButton
        className={cn(
          tokenStatus === "auth-required" ||
            tokenStatus === "scope-required" ||
            "hidden",
        )}
      />
      <ConnectGoogleButton className={cn(connected && "hidden")} />
    </div>
  );
}

function GoogleAccountSkeleton() {
  return (
    <div className="flex items-center justify-between p-4">
      <div className="flex items-center space-x-4">
        <FaGoogle className="size-6 text-muted-foreground/50" />
        <div>
          <div className="flex items-center gap-2">
            <h4 className="font-medium">Google</h4>
          </div>
          <Skeleton className="h-4 w-24 mt-1" />
        </div>
      </div>
      <Skeleton className="h-9 w-24" />
    </div>
  );
}
