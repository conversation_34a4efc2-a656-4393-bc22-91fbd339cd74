import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  BreadcrumbP<PERSON>,
} from "@/components/ui/breadcrumb";
import type { BreadcrumbHandle } from "../layout/route";

export const handle: BreadcrumbHandle = {
  breadcrumb: (
    <Breadcrumb>
      <BreadcrumbList>
        <BreadcrumbItem>
          <BreadcrumbPage>Account</BreadcrumbPage>
        </BreadcrumbItem>
      </BreadcrumbList>
    </Breadcrumb>
  ),
};

import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { createClient } from "~/lib/supabase/.server";
import type { Route } from "./+types/page";
import { withCookieStoreReadonly } from "~/lib/cookie-store.server";
import { href, redirect } from "react-router";
import { updateToken } from "~/lib/oauth2/token.server";
import { googleDriveFileScope } from "@/const";
import GoogleAccount from "./components/social.google";
import GitHubAccount from "./components/social.github";
import BasicInfo from "./components/basic-info";

export function loader({ request }: Route.LoaderArgs) {
  const profile = withCookieStoreReadonly(request, async (cookieStore) => {
    const supabase = await createClient({ cookieStore });
    const {
      data: { user },
      error,
    } = await supabase.auth.getUser();
    if (error) {
      throw error;
    }
    if (!user) {
      throw redirect(href("/app"));
    }
    return {
      profile: {
        avatar:
          typeof user.user_metadata.avatar_url === "string"
            ? user.user_metadata.avatar_url || undefined
            : undefined,
        name:
          typeof user.user_metadata.name === "string"
            ? user.user_metadata.name
            : user.email?.split("@")[0] || undefined,
        email: user.email,
        lastLogin: user.last_sign_in_at,
      },
      connected: {
        google:
          user.identities?.some((identity) => identity.provider === "google") ??
          false,
        github:
          user.identities?.some((identity) => identity.provider === "github") ??
          false,
      },
    };
  });
  const googleToken = withCookieStoreReadonly(request, async (cookieStore) => {
    const token = await updateToken({
      cookieStore,
      provider: "google",
      targetScopes: [googleDriveFileScope],
    });
    return {
      tokenStatus: token.status,
    };
  });

  return { profile, googleToken };
}

export default function AccountPage({
  loaderData: { profile, googleToken },
}: Route.ComponentProps) {
  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4">
      <div className="max-w-7xl mx-auto grid grid-cols-1 lg:grid-cols-12 gap-8">
        <div className="lg:col-span-7 space-y-8">
          <BasicInfo profile={profile} />
          <Card>
            <CardHeader>
              <CardTitle>Connected Accounts</CardTitle>
              <CardDescription>
                Manage your connected social accounts and permissions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col divide-y border rounded-lg">
                <GoogleAccount googleToken={googleToken} profile={profile} />
                <GitHubAccount profile={profile} />
              </div>
            </CardContent>
          </Card>
        </div>
        <div className="lg:col-span-5 space-y-8">
          <Card>
            <CardHeader>
              <CardTitle>Token Usage</CardTitle>
              <CardDescription>
                Monitor your token consumption and limits
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Monthly Usage</span>
                  <span>- / - tokens</span>
                </div>
                <Progress value={10} />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
