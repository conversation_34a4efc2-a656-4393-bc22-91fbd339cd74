import { Loader2 } from "lucide-react";
import { useLayoutEffect } from "react";
import { useNavigate, useSearchParams } from "react-router";
import { parseNextTarget } from "~/lib/next-target";

// we need to perform client-side redirect for Set-Cookie to be applied
// and then we can access the updated cookies in SSR pages
export default function Redirect() {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const next = searchParams.get("next");
  useLayoutEffect(() => {
    navigate(parseNextTarget(next));
  }, [next, navigate]);
  return <RedirectFallback />;
}

function RedirectFallback() {
  return (
    <div className="flex h-full w-full items-center justify-center gap-4">
      <Loader2 className="animate-spin" />
      <p>Redirecting...</p>
    </div>
  );
}
