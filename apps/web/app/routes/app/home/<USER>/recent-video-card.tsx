import { FileVideo } from "lucide-react";
import { cn } from "@/lib/utils";
import { Link } from "react-router";

interface RecentVideoCardProps {
  id: string;
  title: string;
  duration: string;
  thumbnail: string | null;
  source: string;
  icon: React.ComponentType<{ className?: string }>;
}

export function RecentVideoCard(video: RecentVideoCardProps) {
  return (
    <Link
      to={`/app/media?id=${video.id}`}
      className="group flex h-full w-full overflow-hidden rounded-lg border bg-card shadow-xs transition-colors hover:bg-accent/50"
    >
      <div className="w-[120px] shrink-0 relative">
        <img
          src={video.thumbnail || "/placeholder.svg"}
          alt={video.title}
          className="absolute inset-0 h-full w-full object-cover"
        />
      </div>

      <div className="flex-1 p-4 grid grid-rows-[auto_1fr] gap-2">
        <h3 className="line-clamp-2 font-medium">{video.title}</h3>

        <div className="flex items-end gap-2">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <video.icon className="size-4 shrink-0" />
            <span className="line-clamp-1">{video.source}</span>
            <span>•</span>
            <span className="whitespace-nowrap">{video.duration}</span>
          </div>
        </div>
      </div>
    </Link>
  );
}
