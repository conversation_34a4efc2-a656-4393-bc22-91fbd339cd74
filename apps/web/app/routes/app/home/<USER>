import MediaImportInitForm from "@/components/media-import/init-form/init-form";
import RecentVideosSection from "./components/recent-videos-section";
import type { BreadcrumbHandle } from "../layout/route";

export const handle: BreadcrumbHandle = {
  breadcrumb: null,
};

export default function WelcomePage() {
  return (
    <div className="container mx-auto max-w-5xl flex flex-1 flex-col gap-4 px-4 py-10 items-center">
      <section className="text-center">
        <h1 className="text-2xl md:text-4xl font-semibold mb-4">
          Ready to dive into a video?
        </h1>
      </section>

      <MediaImportInitForm
        className="w-full max-w-xl min-w-80"
        urlPlaceholder="link to YouTube video, or remote video/audio file"
      />

      <section className="text-center mb-12">
        <p className="text-muted-foreground">
          Choose video or audio from Google Drive, YouTube,
          <br />
          or import from your hard drive. Nothing leaves your device.
        </p>
      </section>

      {/* Recent/Recommended Videos */}
      <RecentVideosSection />
    </div>
  );
}
