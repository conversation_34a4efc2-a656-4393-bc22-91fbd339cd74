import { FileVideo } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";

export function RecentVideoCardSkeleton() {
  return (
    <div className="group flex h-full w-full overflow-hidden rounded-lg border bg-card shadow-xs">
      <div className="w-[120px] shrink-0 relative">
        <Skeleton className="bg-muted absolute inset-0 h-full w-full" />
      </div>

      <div className="flex-1 p-4 grid grid-rows-[auto_1fr] gap-2">
        <div className="space-y-2">
          <Skeleton className="bg-muted h-4 w-3/4" />
          <Skeleton className="bg-muted h-4 w-1/2" />
        </div>

        <div className="flex items-end gap-2">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <FileVideo className="size-4 shrink-0 text-muted-foreground/50" />
            <Skeleton className="bg-muted h-4 w-20" />
            <span className="text-muted-foreground/50">•</span>
            <Skeleton className="bg-muted h-4 w-12" />
          </div>
        </div>
      </div>
    </div>
  );
}
