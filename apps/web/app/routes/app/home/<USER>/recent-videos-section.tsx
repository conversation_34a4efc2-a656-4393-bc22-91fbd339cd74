import { <PERSON>Audio, FileVideo, History } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { RecentVideoCard } from "./recent-video-card";
import { href, Link } from "react-router";
import { assertNever } from "@std/assert/unstable-never";
import { formatDuration } from "@mx/shared/time/format";
import { RecentVideoCardSkeleton } from "./recent-video-card-skeleton";
import { useRecentMedia } from "@/data/media-list/use-recents";
import { FaBilibili, FaGoogleDrive, FaYoutube } from "react-icons/fa6";
import ClientSuspense from "@/components/client-suspense";

function RecentVideosSectionData() {
  const data = useRecentMedia(12);

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>Recent Videos</CardTitle>
          <Button variant="ghost" size="sm" asChild>
            <Link to={href("/app/vault")}>
              <History className="mr-2 h-4 w-4" />
              View All
            </Link>
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <ScrollArea className="h-[400px] pr-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {data.map((video) => {
              const source = video.sources[0];
              if (!source) return null;
              const dataSource = source.dataSource.data;
              let duration: number | null | undefined = null;
              if (
                dataSource.type === "file-handle" ||
                dataSource.type === "drive-google" ||
                dataSource.type === "file-ref" ||
                dataSource.type === "file-inline" ||
                dataSource.type === "url-file" ||
                dataSource.type === "url-bilibili" ||
                dataSource.type === "url-youtube" ||
                dataSource.type === "url-coursera" ||
                dataSource.type === "url-baidu-pan" ||
                dataSource.type === "url-vimeo"
              ) {
                duration = dataSource.metadata.media?.duration;
              } else {
                assertNever(dataSource.type);
              }
              const icon =
                dataSource.type === "url-youtube"
                  ? FaYoutube
                  : dataSource.type === "url-bilibili"
                    ? FaBilibili
                    : dataSource.type === "drive-google"
                      ? FaGoogleDrive
                      : dataSource.metadata.media?.dimension
                        ? FileVideo
                        : FileAudio;
              return (
                <RecentVideoCard
                  key={video.id}
                  id={video.id}
                  title={video.title}
                  duration={duration ? formatDuration(duration) : "?"}
                  thumbnail={null}
                  source={source.dataSource.data.type}
                  icon={icon}
                />
              );
            })}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
}

function RecentVideosSkeleton() {
  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>Recent Videos</CardTitle>
          <Button variant="ghost" size="sm" asChild>
            <Link to={href("/app/vault")}>
              <History className="mr-2 h-4 w-4" />
              View All
            </Link>
          </Button>
        </div>
        <CardDescription>Recently opened or recommended videos</CardDescription>
      </CardHeader>
      <CardContent>
        <ScrollArea className="h-[400px] pr-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {Array.from({ length: 12 }).map((_, i) => (
              <RecentVideoCardSkeleton
                key={`recent-video-skeleton-${
                  // biome-ignore lint/suspicious/noArrayIndexKey: <explanation>
                  i
                }`}
              />
            ))}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
}

export default function RecentVideosSection() {
  return (
    <ClientSuspense fallback={<RecentVideosSkeleton />}>
      <RecentVideosSectionData />
    </ClientSuspense>
  );
}
