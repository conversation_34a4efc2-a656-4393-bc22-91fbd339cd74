import { useConnectLaunch } from "@/hooks/pwa/launch";
import { Outlet } from "react-router";
import ClientProviders from "./providers";
import { useMediaLaunchHandler } from "~/lib/media-launch/use-media-launch";
import { useGlobalHotkeys } from "@/hooks/pwa/hotkeys";
import useMediaIdSync from "./app/media/hooks/use-id-sync";

function GlobalHookLoader({ children }: { children: React.ReactNode }) {
  useConnectLaunch();
  useMediaLaunchHandler();
  useGlobalHotkeys();
  useMediaIdSync();
  return children;
}

export default function Layout() {
  return (
    <ClientProviders>
      <GlobalHookLoader>
        <Outlet />
      </GlobalHookLoader>
    </ClientProviders>
  );
}
