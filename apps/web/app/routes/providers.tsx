import { Toaster } from "@/components/ui/sonner";
// import { configureClient } from "@/lib/logger/client";
import { Provider as <PERSON><PERSON><PERSON>rovider } from "jotai/react";
import { createStore } from "jotai";

import { QueryClientProvider } from "@tanstack/react-query";
import { queryClient<PERSON>tom } from "jotai-tanstack-query";
import AtomsHydrator from "@/hooks/providers/atom-hydrator";
import { queryClient } from "@/data/client";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";

export const jotaiStore = createStore();

// void configureClient();

export default function ClientProviders({
  children,
}: { children: React.ReactNode }) {
  return (
    <JotaiProvider store={jotaiStore}>
      <QueryClientProvider client={queryClient}>
        <AtomsHydrator atomValues={[[queryClientAtom, queryClient]]}>
          {children}
          <Toaster />
          {process.env.NODE_ENV === "development" && <ReactQueryDevtools />}
        </AtomsHydrator>
      </QueryClientProvider>
    </JotaiProvider>
  );
}
