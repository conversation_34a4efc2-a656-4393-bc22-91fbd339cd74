import djb2a from "@mx/shared/utils/djb2a";

export const cookiePrefix = "mx-oauth2-";

export function toCookieName(id: OAuth2CredentialId, prefix = "") {
  const idHash = djb2a([id.userId, id.provider, id.providerId].join("-"));
  return `${prefix}${cookiePrefix}${idHash.toString(36)}`;
}

export class InvalidRefreshTokenError extends Error {
  constructor(message: string) {
    super(message);
    this.name = "InvalidRefreshTokenError";
  }
}

export interface RefreshTokenStorage {
  // supabase user id
  userId: string;
  provider: "google";
  // provider user id
  providerId: string;
  refreshToken: string;
  scopes: string[];
}

export type OAuth2CredentialId = {
  userId: string;
  provider: OAuth2Provider;
  providerId: OAuth2ProviderUserId;
};

export interface TokenResponse {
  accessToken: string;
  scopes: string[];
  expiryDate: number;
  refreshToken: string | null;
}

export type OAuth2ProviderUserId = string;

export const oauth2Providers = ["google"] as const;

export function isOAuth2Provider(
  provider: string | undefined,
): provider is OAuth2Provider {
  if (!provider) {
    return false;
  }
  return oauth2Providers.includes(provider as OAuth2Provider);
}

export type OAuth2Provider = (typeof oauth2Providers)[number];
