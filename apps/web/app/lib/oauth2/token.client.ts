import { type DBSchema, openDB } from "idb";
import type { <PERSON>A<PERSON>2<PERSON>rovider } from "./def";
import type {
  AccessTokenCache,
  AccessTokenGrantedStatus,
  AccessTokenNAStatus,
  AccessTokenResponse,
} from "./token.def";
import { SingletonPromise } from "@/lib/utils/singleton-promise";
import { authTokenRoute } from "@/const";

export type { AccessTokenCache, AccessTokenResponse };
export type {
  AccessTokenNAStatus,
  AccessTokenGrantedStatus,
} from "./token.def";
export { toLocalStorageKey } from "./token.def";

const DB_NAME = "token-cache";
const STORE_NAME = "tokens";
const DB_VERSION = 1;

// In-memory cache during app lifespan
const memoryCache = new Map<string, AccessTokenCache>();

interface TokenCacheDB extends DBSchema {
  tokens: {
    value: AccessTokenCache;
    key: string;
  };
}

// IndexedDB helper functions
async function getDB() {
  return openDB<TokenCacheDB>(DB_NAME, DB_VERSION, {
    upgrade(db) {
      if (!db.objectStoreNames.contains(STORE_NAME)) {
        db.createObjectStore(STORE_NAME);
      }
    },
  });
}

async function getTokenFromCache(
  key: string,
): Promise<AccessTokenCache | null> {
  // Check memory cache first
  const cached = memoryCache.get(key);
  if (cached) {
    if (cached.expiryDate.getTime() > Date.now()) {
      return cached;
    }
    // Remove expired token from memory cache
    memoryCache.delete(key);
    return null;
  }

  // If not in memory, check IndexedDB
  const db = await getDB();
  const tx = db.transaction(STORE_NAME, "readonly");
  const store = tx.objectStore(STORE_NAME);
  const payload = await store.get(key);

  if (!payload) return null;

  if (payload.expiryDate.getTime() < Date.now()) {
    // Remove expired token from IndexedDB
    const deleteTx = db.transaction(STORE_NAME, "readwrite");
    await deleteTx.objectStore(STORE_NAME).delete(key);
    return null;
  }

  const result = {
    ...payload,
    expiryDate: new Date(payload.expiryDate),
  };

  // Cache in memory for future use
  memoryCache.set(key, result);
  return result;
}

async function setToken(key: string, payload: AccessTokenCache) {
  // Update memory cache
  memoryCache.set(key, payload);

  // Update IndexedDB
  const db = await getDB();
  const tx = db.transaction(STORE_NAME, "readwrite");
  await tx.objectStore(STORE_NAME).put(payload, key);
  await tx.done;
}

async function tokenFetch({
  clientSideKey,
  provider,
  targetScopes = [],
}: TokenFetchParams): Promise<TokenFetchResult> {
  const cached = await getTokenFromCache(clientSideKey);
  if (cached) {
    return {
      token: cached,
      key: clientSideKey,
      status: "granted",
    };
  }
  const search = new URLSearchParams();
  for (const scope of targetScopes) {
    search.append("scope", scope);
  }
  const resp = await fetch(`${authTokenRoute(provider)}?${search}`);
  if (!resp.ok) {
    throw new Error(`Failed to fetch access token: ${resp.statusText}`);
  }
  const respJson = (await resp.json()) as AccessTokenResponse;
  if (!respJson.token) {
    return {
      token: null,
      status: respJson.status,
    };
  }
  // client side session dont match server side session
  // maybe supabase session expired?
  if (clientSideKey !== respJson.key) {
    return {
      token: null,
      status: "not-login",
    };
  }
  const newToken = {
    accessToken: respJson.token.accessToken,
    scopes: respJson.token.scopes,
    expiryDate: new Date(respJson.token.expiryDate),
  };
  setToken(clientSideKey, newToken);
  return {
    token: newToken,
    key: respJson.key,
    status: "granted",
  };
}

const tokenPromiseManager = new SingletonPromise<
  OAuth2Provider,
  TokenFetchResult
>();

export function getAccessToken(params: TokenFetchParams) {
  return tokenPromiseManager.execute(params.provider, () => tokenFetch(params));
}

interface TokenFetchParams {
  provider: OAuth2Provider;
  clientSideKey: string;
  targetScopes?: string[];
}

type TokenFetchResult =
  | {
      token: AccessTokenCache;
      key: string;
      status: AccessTokenGrantedStatus;
    }
  | { token: null; status: AccessTokenNAStatus };

export async function removeTokenFromCache(clientSideKey: string) {
  // Remove from memory cache
  memoryCache.delete(clientSideKey);

  // Remove from IndexedDB
  const db = await getDB();
  const tx = db.transaction(STORE_NAME, "readwrite");
  await tx.objectStore(STORE_NAME).delete(clientSideKey);
  await tx.done;
}
