import djb2a from "@mx/shared/utils/djb2a";
import type { User } from "@supabase/supabase-js";
import type { OAuth2Provider, OAuth2CredentialId } from "./def";

export type AccessTokenNAStatus =
  // need prompt=consent to get refresh token with new scope
  | "scope-required"
  | "not-login"
  | "not-linked"
  // need prompt=consent to get refresh token
  | "auth-required";

export type AccessTokenGrantedStatus = "granted";

export type AccessTokenStatus = AccessTokenNAStatus | AccessTokenGrantedStatus;

export type AccessTokenResponse =
  | {
      token: {
        accessToken: string;
        scopes: string[];
        expiryDate: number;
      };
      /** use this key to check against local user session */
      key: string;
      status: AccessTokenGrantedStatus;
    }
  | {
      token: null;
      status: AccessTokenNAStatus;
    };

const localStoragePrefix = "mx-oauth2-";

/**
 * @returns keys for local storage cache to store oauth2 tokens
 */
export function toLocalStorageKey(
  provider: OAuth2Provider,
  { identities, id: userId }: User,
): LocalStorageKeys | null {
  const identity = identities?.find((i) => i.provider === provider);
  if (!identity) {
    return null;
  }
  const idHash = djb2a([userId, provider, identity.id].join("-"));
  return {
    key: `${localStoragePrefix}${idHash.toString(36)}`,
    id: {
      userId,
      provider,
      providerId: identity.id,
    },
  };
}

export type LocalStorageKeys = {
  key: string;
  id: OAuth2CredentialId;
};

export interface AccessTokenCache {
  accessToken: string;
  scopes: string[];
  expiryDate: Date;
}
