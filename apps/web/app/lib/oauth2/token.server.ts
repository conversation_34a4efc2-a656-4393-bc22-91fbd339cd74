import type { OAuth2Provider, TokenResponse } from "~/lib/oauth2/def";
import { toLocalStorageKey, type AccessTokenResponse } from "./token.def";
import { refreshTokenFor } from "./.server/refresh-store";
import { assertNever } from "@std/assert/unstable-never";
import { ResponseBodyError } from "oauth4webapi";
import { createClient } from "~/lib/supabase/.server";
import type { CookieStore } from "~/lib/cookie-store.server";

export async function updateToken({
  provider,
  targetScopes = [],
  cookieStore,
}: {
  provider: OAuth2Provider;
  targetScopes?: string[];
  cookieStore: CookieStore;
}): Promise<AccessTokenResponse> {
  const supabase = await createClient({ cookieStore });
  const {
    data: { user },
  } = await supabase.auth.getUser();
  if (!user) {
    return {
      token: null,
      status: "not-login",
    };
  }
  const keys = toLocalStorageKey(provider, user);
  if (!keys) {
    return {
      token: null,
      status: "not-linked",
    };
  }
  const refreshToken = refreshTokenFor(keys.id, cookieStore);

  const tokenPayload = await refreshToken.get();
  if (!tokenPayload) {
    return { token: null, status: "auth-required" };
  }
  if (targetScopes.some((s) => !tokenPayload.scopes.includes(s))) {
    return { token: null, status: "scope-required" };
  }
  try {
    let credentials: TokenResponse;
    if (provider === "google") {
      const { default: refreshAccessToken } = await import(
        "~/lib/oauth2/.server/api/token/google"
      );
      credentials = await refreshAccessToken(tokenPayload.refreshToken);
    } else {
      assertNever(provider);
    }

    // if refresh token rotation is supported, update the refresh token
    if (credentials.refreshToken) {
      refreshToken.set({
        refreshToken: credentials.refreshToken,
        scopes: credentials.scopes,
      });
    }
    return {
      token: {
        accessToken: credentials.accessToken,
        scopes: credentials.scopes,
        expiryDate: credentials.expiryDate,
        // use key to check against local id
      },
      key: keys.key,
      status: "granted",
    };
  } catch (e) {
    // handle refresh token expiry
    if (e instanceof ResponseBodyError) {
      if (e.status === 400) {
        return { token: null, status: "auth-required" };
      }
    }
    throw e;
  }
}
