import type { CookieStore } from "~/lib/cookie-store.server";

const COOKIE_PREFIX = "mx-pkce-verifier-";

export interface PkceCookieOptions {
  sessionId: string;
  codeVerifier: string;
  redirectUriPath: string;
}

export async function setPkceCookie(
  { sessionId, codeVerifier, redirectUriPath }: PkceCookieOptions,
  cookieStore: CookieStore,
) {
  cookieStore.set(`${COOKIE_PREFIX}${sessionId}`, codeVerifier, {
    secure: true,
    httpOnly: true,
    // Allow cookie to be sent during redirects from OAuth provider
    // "lax" is safe for OAuth flows while preventing CSRF attacks
    // "strict" would block the cookie during the OAuth redirect
    sameSite: "lax",
    path: redirectUriPath,
    maxAge: 300, // 5 minutes, matching state JWT expiration
  });
}

export async function getPkceCookie(
  sessionId: string,
  cookieStore: CookieStore,
) {
  const cookie = cookieStore.get(`${COOKIE_PREFIX}${sessionId}`);
  if (!cookie) {
    return null;
  }
  return { codeVerifier: cookie.value };
}

export function deletePkceCookie(sessionId: string, cookieStore: CookieStore) {
  cookieStore.delete(`${COOKIE_PREFIX}${sessionId}`);
}
