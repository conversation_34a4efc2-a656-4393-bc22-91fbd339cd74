import { parseNextTarget } from "~/lib/next-target";

export async function getPKCERedirectTarget(
  formData: FormData,
): Promise<string> {
  const redirectPath = process.env.SUPABASE_AUTH_LOCAL_REDIRECT_PATH;
  if (!redirectPath) {
    throw new Error("SUPABASE_AUTH_LOCAL_REDIRECT_PATH is not set");
  }
  const searchParams = new URLSearchParams();

  const next = parseNextTarget(formData.get("next"));
  if (next) {
    searchParams.set("next", next);
  }

  return `${redirectPath}?${searchParams}`;
}
