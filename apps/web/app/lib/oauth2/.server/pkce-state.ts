import { type JWTPayload, jwtVerify, SignJWT } from "jose";

interface PKCEStatePayload extends JWTPayload {
  providerId: string;
  sessionId: string;
  userId: string;
  next: string | null;
}

export async function signState(payload: PKCEStatePayload) {
  if (!process.env.IRON_SESSION_PASSWORD) {
    throw new Error("IRON_SESSION_PASSWORD is not set");
  }
  const secret = new TextEncoder().encode(process.env.IRON_SESSION_PASSWORD);

  return await new SignJWT(payload)
    .setProtectedHeader({ alg: "HS256" })
    .setIssuedAt()
    .setExpirationTime("5m") // Short expiration for CSRF protection
    .sign(secret);
}

export async function verifyState(token: string) {
  if (!process.env.IRON_SESSION_PASSWORD) {
    throw new Error("IRON_SESSION_PASSWORD is not set");
  }
  const secret = new TextEncoder().encode(process.env.IRON_SESSION_PASSWORD);
  const { payload } = await jwtVerify<PKCEStatePayload>(token, secret);
  return payload;
}
