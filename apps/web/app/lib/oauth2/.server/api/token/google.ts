import {
  type Client,
  refreshTokenGrantRequest,
  processRefreshTokenResponse,
  ClientSecretPost,
  discoveryRequest,
  processDiscoveryResponse,
} from "oauth4webapi";
import type { TokenResponse } from "~/lib/oauth2/def";

export default async function refreshAccessTokenGoogle(
  refreshToken: string,
): Promise<TokenResponse> {
  const as = await discoveryRequest(
    new URL("https://accounts.google.com/"),
  ).then((res) =>
    processDiscoveryResponse(new URL("https://accounts.google.com/"), res),
  );
  const client: Client = {
    client_id: process.env.SUPABASE_AUTH_EXTERNAL_GOOGLE_CLIENT_ID,
  };
  const clientAuth = ClientSecretPost(
    process.env.SUPABASE_AUTH_EXTERNAL_GOOGLE_SECRET,
  );
  const response = await refreshTokenGrantRequest(
    as,
    client,
    clientAuth,
    refreshToken,
  );
  const credentials = await processRefreshTokenResponse(as, client, response);

  const scopes = credentials.scope!.split(" ");
  const expiryDate = Date.now() + credentials.expires_in! * 1000;

  return {
    accessToken: credentials.access_token,
    scopes,
    expiryDate,
    // google doesn't support refresh token rotation
    refreshToken: null,
  };
}
