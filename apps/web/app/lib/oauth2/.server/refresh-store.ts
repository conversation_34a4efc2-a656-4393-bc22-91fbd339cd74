import type { CookieS<PERSON> } from "~/lib/cookie-store.server";
import {
  cookiePrefix,
  toCookieName,
  type OAuth2CredentialId,
  type RefreshTokenStorage,
} from "~/lib/oauth2/def";
import { getIronSession, type IronSession } from "iron-session";

export function refreshTokenFor(
  id: OAuth2CredentialId,
  cookieStore: CookieStore,
) {
  const cookieName = toCookieName(
    id,
    // Cookies with names starting with __Host- are sent only to the host subdomain or domain that set them,
    // and not to any other host.
    // in local dev with http, setting __Host- prefixed cookies will not work
    process.env.NODE_ENV === "production" ? "__Host-" : "",
  );

  if (!process.env.IRON_SESSION_PASSWORD) {
    throw new Error("IRON_SESSION_PASSWORD is not set");
  }

  let session:
    | Promise<IronSession<{ payload: RefreshTokenStorage }>>
    | undefined;

  const init = async () => {
    session ??= getIronSession<{ payload: RefreshTokenStorage }>(cookieStore, {
      password: process.env.IRON_SESSION_PASSWORD,
      cookieName,
      ttl: 0,
      // https://www.ietf.org/archive/id/draft-ietf-oauth-browser-based-apps-22.html#name-cookie-security
      cookieOptions: {
        secure: true,
        httpOnly: true,
        sameSite: "strict",
        path: "/",
      },
    });
    return await session;
  };

  return {
    async get() {
      const session = await init();
      return session.payload || null;
    },
    async set(payload: Omit<RefreshTokenStorage, keyof OAuth2CredentialId>) {
      const session = await init();
      const storageValue: RefreshTokenStorage = {
        ...id,
        ...payload,
      };
      session.payload = storageValue;
      await session.save();
    },
    async delete() {
      cookieStore.delete(cookieName);
    },
  };
}

export function clearAllRefreshTokens(cookieStore: CookieStore) {
  for (const v of cookieStore.getAll()) {
    if (
      v.name.startsWith(`__Host-${cookiePrefix}`) ||
      v.name.startsWith(cookiePrefix)
    ) {
      cookieStore.delete(v.name);
    }
  }
}
