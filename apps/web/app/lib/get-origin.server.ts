import { localDevDefaultOrigin } from "@/const";

export async function getOrigin(headers: Headers) {
  if (
    process.env.VERCEL_PROJECT_PRODUCTION_URL &&
    process.env.VERCEL_ENV === "production"
  ) {
    return `https://${process.env.VERCEL_PROJECT_PRODUCTION_URL}`;
  }
  if (process.env.VERCEL_URL) {
    return `https://${process.env.VERCEL_URL}`;
  }
  if (process.env.SITE_ORIGIN) {
    return process.env.SITE_ORIGIN;
  }

  // Handle proxy headers if present
  const forwardedHost = headers.get("x-forwarded-host");
  const forwardedProto = headers.get("x-forwarded-proto");

  if (forwardedHost) {
    const protocol = forwardedProto || "https";
    return `${protocol}://${forwardedHost}`;
  }

  const host = headers.get("host");
  if (!host) {
    console.warn("No host header found, fallback to localhost");
    return localDevDefaultOrigin;
  }
  // if loopback address (include ipv4 and ipv6)
  if (
    host.startsWith("localhost") ||
    host.startsWith("127.0.0.1") ||
    host.startsWith("::1")
  ) {
    return `http://${host}`;
  }
  return `https://${host}`;
}
