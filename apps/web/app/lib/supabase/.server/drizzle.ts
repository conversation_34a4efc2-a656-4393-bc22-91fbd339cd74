import { sql } from "drizzle-orm";
import type { PgDatabase } from "drizzle-orm/pg-core";

type SupabaseToken = {
  iss?: string;
  sub?: string;
  aud?: string[] | string;
  exp?: number;
  nbf?: number;
  iat?: number;
  jti?: string;
  role?: string;
};

export default function createDrizzle<
  Database extends PgDatabase<any, any, any>,
  <PERSON><PERSON> extends SupabaseToken = SupabaseToken,
>(accessToken: string, { client }: { client: Database }) {
  const token = decode<Token>(accessToken);
  return {
    rls: (async (transaction, ...rest) => {
      return await client.transaction(
        async (tx) => {
          // Supabase exposes auth.uid() and auth.jwt()
          // https://supabase.com/docs/guides/database/postgres/row-level-security#helper-functions
          try {
            await tx.execute(sql`
          -- auth.jwt()
          select set_config('request.jwt.claims', '${sql.raw(
            JSON.stringify(token),
          )}', TRUE);
          -- auth.uid()
          select set_config('request.jwt.claim.sub', '${sql.raw(
            token.sub ?? "",
          )}', TRUE);												
          -- set local role
          set local role ${sql.raw(token.role ?? "anon")};
          `);
            return await transaction(tx);
          } finally {
            await tx.execute(sql`
            -- reset
            select set_config('request.jwt.claims', NULL, TRUE);
            select set_config('request.jwt.claim.sub', NULL, TRUE);
            reset role;
            `);
          }
        },
        ...rest,
      );
    }) as Database["transaction"],
  };
}

import { decodeJwt, type JWTPayload } from "jose";

function decode<T extends JWTPayload & { role?: string }>(accessToken: string) {
  try {
    return decodeJwt<T>(accessToken);
  } catch (error) {
    return { role: "anon" } as T;
  }
}
