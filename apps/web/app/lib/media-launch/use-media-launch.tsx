import { existingMediaFromLaunchFile<PERSON>tom } from "@/hooks/pwa/file-query";
import { useLaunchListener } from "@/hooks/pwa/launch";
import { useCallback, useEffect, useRef } from "react";
import { useNavigate } from "react-router";
import { useStore } from "jotai";
import { mediaIdAtom } from "@/data/media-ref/query/base";
import { sleep } from "@/lib/utils/sleep";
import { useMediaRemoteRef } from "@mx/shared/hooks/use-player";
import { parseHashProps } from "@mx/shared/utils/hash-prop";
import { isTimestamp } from "@mx/shared/time/temporal-frag";

function useInitLock() {
  const initLockRef = useRef(true);
  useEffect(() => {
    setTimeout(() => {
      initLockRef.current = false;
    }, 500);
  }, []);
  return initLockRef;
}

function useHashChange(onHashChange: (evt: HashChangeEvent) => void) {
  useEffect(() => {
    if (typeof document === "undefined") return;
    window.addEventListener("hashchange", onHashChange);
    return () => {
      window.removeEventListener("hashchange", onHashChange);
    };
  }, [onHashChange]);
}

export function useMediaLaunchHandler() {
  const navigate = useNavigate();
  const store = useStore();
  const initLockRef = useInitLock();
  const playerRef = useMediaRemoteRef();

  // set to false to disable hash change handling
  const hashChangeSwitchRef = useRef(true);

  useHashChange(
    useCallback(() => {
      if (hashChangeSwitchRef.current === false) return;
      // handle regular hash changes
      const hashParams = parseHashProps(window.location.hash);
      const player = playerRef.current;
      if (isTimestamp(hashParams.tempFragment)) {
        player.play();
        player.seek(hashParams.tempFragment.start);
      }
    }, [playerRef]),
  );

  useLaunchListener(
    useCallback(
      async (get, _set, now, _prev) => {
        const player = playerRef.current;

        const handleFileLaunch = async () => {
          // don't handle initial launch for global handler,
          // because it could be initiated during focus-existing by, for example, reloads
          // and we don't want to handle it here.
          // the valid case will be handled in /launch/local-file
          // where file is opened with when no existing app instance is opened.
          if (initLockRef.current) return false;
          const nowFile = now?.files[0];
          if (!nowFile) return false;
          const [existingMediaRef] = await get(existingMediaFromLaunchFileAtom);
          if (existingMediaRef) {
            navigate(`/app/media?id=${existingMediaRef.id}`);
          } else {
            navigate("/app/launch/local-file");
          }
          return true;
        };

        const handleSchemeLaunch = async () => {
          if (initLockRef.current) return false;
          if (!now?.schemeURL) return false;
          const { pathname, hash } = now.schemeURL;
          if (pathname.startsWith("/media/")) {
            const id = pathname.split("/")[2];
            const currentId = store.get(mediaIdAtom);
            if (currentId !== id) {
              navigate(`/app/media?id=${id}${hash}`);
            } else {
              hashChangeSwitchRef.current = false;
              window.location.hash = hash;
              // sleep(0) ensures the hashchange event from our programmatic hash update
              // is processed in the current event loop tick, before we reset disableHashRef.
              // This prevents our hashchange handler from double-processing the same hash update.
              await sleep(0);
              hashChangeSwitchRef.current = true;
              const hashParams = parseHashProps(hash);
              if (isTimestamp(hashParams.tempFragment)) {
                player.play();
                player.seek(hashParams.tempFragment.start);
              }
            }
            return true;
          }
          return false;
        };
        (await handleFileLaunch()) || (await handleSchemeLaunch());
      },
      [initLockRef, playerRef, store, navigate],
    ),
  );
}
