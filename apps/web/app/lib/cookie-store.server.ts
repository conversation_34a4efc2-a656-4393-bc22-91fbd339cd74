import type { SerializeOptions as CookieSerializeOptions } from "cookie";

/**
 * {@link https://wicg.github.io/cookie-store/#dictdef-cookielistitem CookieListItem}
 * as specified by W3C.
 */
interface CookieListItem
  extends Pick<
    CookieSerializeOptions,
    "domain" | "path" | "sameSite" | "secure"
  > {
  /** A string with the name of a cookie. */
  name: string;
  /** A string containing the value of the cookie. */
  value: string;
  /** A number of milliseconds or Date interface containing the expires of the cookie. */
  expires?: CookieSerializeOptions["expires"] | number;
}
/**
 * Superset of {@link CookieListItem} extending it with
 * the `httpOnly`, `maxAge` and `priority` properties.
 */
type ResponseCookie = CookieListItem &
  Pick<CookieSerializeOptions, "httpOnly" | "maxAge" | "priority">;

import { parse, serialize } from "cookie";

export class CookieStore {
  #readCookies = new Map<string, ResponseCookie>();
  #pendingCookies = new Map<string, ResponseCookie>();

  constructor(requestHeaders: Headers) {
    const cookieHeader = requestHeaders.get("Cookie");
    if (!cookieHeader) return;
    const parsedCookies = parse(cookieHeader);
    for (const [name, value] of Object.entries(parsedCookies)) {
      if (!value) continue;
      this.#readCookies.set(name, { name, value });
    }
  }

  get(name: string): { name: string; value: string } | undefined {
    const readCookie = this.#readCookies.get(name);
    if (readCookie) {
      return { name: readCookie.name, value: readCookie.value };
    }
    return undefined;
  }

  has(name: string): boolean {
    return this.#readCookies.has(name);
  }

  /**
   * The amount of cookies received from the client
   */
  get size(): number {
    return this.#readCookies.size;
  }

  set(name: string, value: string, cookie?: Partial<ResponseCookie>): void;
  set(options: ResponseCookie): void;
  set(
    nameOrOptions: string | ResponseCookie,
    value?: string,
    options?: Partial<ResponseCookie>,
  ): void {
    if (typeof nameOrOptions === "string") {
      const name = nameOrOptions;
      if (value === undefined) {
        throw new Error("Cookie value must be provided");
      }

      this.#pendingCookies.set(name, {
        name,
        value,
        ...(options || {}),
      });
    } else {
      const { name, value, ...cookieOptions } = nameOrOptions;
      this.#pendingCookies.set(name, { name, value, ...cookieOptions });
    }
  }

  #delete(name: string): boolean {
    // Set the cookie with an expiration date in the past to delete it
    this.set(name, "", {
      expires: new Date(0), // Set to epoch time (1970-01-01)
      maxAge: 0, // Also set maxAge to 0 for immediate expiration
    });
    return this.#readCookies.has(name);
  }

  delete(names: string): boolean;
  delete(names: string[]): boolean[];
  delete(_names: string | string[]): boolean | boolean[] {
    if (typeof _names === "string") {
      return this.#delete(_names);
    }
    return _names.map(this.#delete);
  }

  /**
   * Delete all the cookies in the cookies in the request.
   */
  clear(): this {
    for (const cookie of this.#readCookies.keys()) {
      this.#delete(cookie);
    }
    this.#readCookies.clear();
    return this;
  }

  apply(headers: Headers): void {
    // Only apply pending cookies to headers
    for (const cookie of this.#pendingCookies.values()) {
      const { name, value, expires, ...options } = cookie;
      const serialized = serialize(name, value, {
        ...options,
        expires: expires ? new Date(expires) : undefined,
      });
      headers.append("Set-Cookie", serialized);
    }
  }

  getAll(): ResponseCookie[] {
    return Array.from(this.#readCookies.values());
  }

  // Make the CookieStore iterable - combines both read and pending cookies
  // with pending cookies taking precedence
  [Symbol.iterator](): IterableIterator<
    [string, { name: string; value: string }]
  > {
    return this.#readCookies.entries();
  }
}

import type { UNSAFE_DataWithResponseInit } from "react-router";

type ResponseOrData =
  | Data<any>
  | Response
  | undefined
  | Promise<Response | Data<any> | undefined>;

export type Data<D> = UNSAFE_DataWithResponseInit<D>;

export async function withCookieStore<R extends ResponseOrData>(
  request: Request,
  handler: (cookieStore: CookieStore) => R,
): Promise<Awaited<R>> {
  const cookieStore = new CookieStore(request.headers);
  const response = ((await handler(cookieStore)) ??
    new Response()) as Awaited<ResponseOrData>;
  if (response instanceof Response) {
    cookieStore.apply(response.headers);
  } else if (response) {
    const headers = new Headers(response.init?.headers);
    cookieStore.apply(headers);
    if (response.init?.headers) {
      response.init.headers = headers;
    } else {
      response.init = { headers };
    }
  }
  return response as any;
}

export async function withCookieStoreReadonly<D>(
  request: Request,
  handler: (cookieStore: CookieStore) => Promise<D>,
): Promise<D> {
  const cookieStore = new CookieStore(request.headers);
  return handler(cookieStore);
}
