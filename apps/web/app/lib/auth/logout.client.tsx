import { createClient } from "~/lib/supabase/.client";
import { removeTokenFromCache } from "~/lib/oauth2/token.client";
import { toLocalStorageKey } from "~/lib/oauth2/token.def";

export default async function clientLogout({
  clientSideKeys,
}: { clientSideKeys?: string[] } = {}) {
  const supabase = createClient();
  if (clientSideKeys) {
    for (const key of clientSideKeys) {
      await removeTokenFromCache(key);
    }
  } else {
    const {
      data: { session },
    } = await supabase.auth.getSession();
    if (session) {
      const clientSideKey = toLocalStorageKey("google", session.user);
      if (clientSideKey) {
        await removeTokenFromCache(clientSideKey.key);
      }
      await supabase.auth.signOut().catch();
    }
  }
}
