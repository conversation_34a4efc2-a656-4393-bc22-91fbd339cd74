---
description:
globs:
alwaysApply: false
---
# Data Models and Type Definitions

The plugin uses several key data structures defined in the `src/def` directory:

## Media Information
- [src/def/media-info.ts](mdc:src/def/media-info.ts): Core media information interfaces
- [src/def/media-type.ts](mdc:src/def/media-type.ts): Media type definitions and supported formats
- [src/def/media-id.ts](mdc:src/def/media-id.ts): Media identification structures

## Track Information
- [src/def/track-info.ts](mdc:src/def/track-info.ts): Track data structures
- [src/def/file-info.ts](mdc:src/def/file-info.ts): File information interfaces

## View Types
- [src/def/view-type.ts](mdc:src/def/view-type.ts): View type constants and interfaces

## URL and File Parsing
- [src/def/url-parse.ts](mdc:src/def/url-parse.ts): URL parsing utilities
- [src/def/file-parse.ts](mdc:src/def/file-parse.ts): File parsing utilities
