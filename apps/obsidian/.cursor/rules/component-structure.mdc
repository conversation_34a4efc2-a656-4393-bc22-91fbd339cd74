---
description:
globs:
alwaysApply: false
---
# React Components Structure

The Media Extended plugin uses React for its UI components:

## Player Components
- [src/components/player.tsx](mdc:src/components/player.tsx): Main media player component
- [src/components/player-remote.tsx](mdc:src/components/player-remote.tsx): Remote player component
- [src/components/transcript.tsx](mdc:src/components/transcript.tsx): Transcript display component

## Shadow DOM Integration
- [src/components/shadow-dom.tsx](mdc:src/components/shadow-dom.tsx): Handles Shadow DOM integration for isolated styling

## CSS and Styling
- [src/components/player.css](mdc:src/components/player.css): Base styles for player components
