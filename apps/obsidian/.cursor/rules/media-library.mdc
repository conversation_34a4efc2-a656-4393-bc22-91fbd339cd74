---
description:
globs:
alwaysApply: false
---
# Media Library System

The media library system manages media files and their metadata:

## Media Library Indexer
- [src/media-lib/indexer.ts](mdc:src/media-lib/indexer.ts): Indexes media files in the vault

## Media Library Note Integration
- [src/media-lib/insert.ts](mdc:src/media-lib/insert.ts): Functions for inserting media into notes

## Transcript Support
- [src/transcript/loader.ts](mdc:src/transcript/loader.ts): Loads and processes transcripts for media files

## Audio Recording
- [src/audio-rec](mdc:src/audio-rec/index.ts): Audio recording functionality
