---
description:
globs:
alwaysApply: false
---
# Media Extended Obsidian Plugin

The Media Extended plugin is a media player extension for Obsidian that enhances media playback capabilities.

## Main Entry Point
- [src/mx-main.ts](mdc:src/mx-main.ts): The main plugin class that initializes the plugin, registers views, and sets up commands.

## Core Modules
- **Media Views**: Provides different views for media playback ([src/media-view](mdc:src/media-view/file-view.ts)).
- **Track Views**: Views for displaying and managing media tracks ([src/track-view](mdc:src/track-view/file-view.ts)).
- **Media Library**: Indexes and manages media files ([src/media-lib](mdc:src/media-lib/indexer.ts)).
- **Components**: React components for the UI ([src/components](mdc:src/components/player.tsx)).
- **Definitions**: Type definitions and interfaces ([src/def](mdc:src/def/media-info.ts)).
- **Settings**: Plugin settings management ([src/settings](mdc:src/settings/registry.ts)).
- **Commands**: User commands for media control ([src/command](mdc:src/command/media.ts)).
- **Link Management**: Handling media links ([src/link](mdc:src/link/index.ts)).
