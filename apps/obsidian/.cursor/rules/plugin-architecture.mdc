---
description:
globs:
alwaysApply: false
---
# Plugin Architecture

The Media Extended plugin integrates with Obsidian through the standard plugin architecture:

## Plugin Initialization
- [src/mx-main.ts](mdc:src/mx-main.ts): Main plugin class that extends Obsidian's Plugin
- [manifest.json](mdc:manifest.json): Plugin manifest with metadata
- [manifest-beta.json](mdc:manifest-beta.json): Beta version manifest

## Settings Integration
- [src/settings/tab.ts](mdc:src/settings/tab.ts): Settings tab implementation
- [src/settings/registry.ts](mdc:src/settings/registry.ts): Settings registry

## View Integration
- Media Views: Custom views for media playback
  - [src/media-view/file-view.ts](mdc:src/media-view/file-view.ts): File-based media view
  - [src/media-view/url-view.ts](mdc:src/media-view/url-view.ts): URL-based media view
  - [src/media-view/remote-view.ts](mdc:src/media-view/remote-view.ts): Remote media playback

## Command Integration
- [src/command/media.ts](mdc:src/command/media.ts): Media control commands
- [src/command/note.ts](mdc:src/command/note.ts): Note-related commands
- [src/command/global.ts](mdc:src/command/global.ts): Global plugin commands

## Patches and Customizations
- [src/patch](mdc:src/patch/view.ts): Patches for Obsidian's default behavior
