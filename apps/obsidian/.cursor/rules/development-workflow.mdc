---
description:
globs:
alwaysApply: false
---
# Development Workflow

## Setting Up Development Environment
1. Clone the repository
2. Install dependencies: `npm install`
3. Start development server: `npm run dev`

## Creating New Features
When adding new features:
1. Add type definitions in [src/def](mdc:src/def) if needed
2. Implement React components in [src/components](mdc:src/components) if UI is required
3. Add feature implementation in the appropriate module directory
4. Register views, commands or patches in [src/mx-main.ts](mdc:src/mx-main.ts)
5. Update settings if configuration options are needed

## Testing
- Test the plugin within Obsidian by symlink or copying to the Obsidian plugins folder
- Ensure compatibility with various media formats

## Building for Distribution
- Run `npm run build` to create the production build
- Update version in [manifest.json](mdc:manifest.json) and [manifest-beta.json](mdc:manifest-beta.json)
- Update [versions.json](mdc:versions.json) with the new version information
