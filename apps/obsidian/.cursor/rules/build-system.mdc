---
description:
globs:
alwaysApply: false
---
# Build System Configuration

The Media Extended plugin uses esbuild for bundling:

## Build Configuration
- [esbuild.config.js](mdc:esbuild.config.js): esbuild configuration file
- [tsconfig.json](mdc:tsconfig.json): TypeScript configuration

## Scripts
- Build: `npm run build` - Builds the production version
- Development: `npm run dev` - Starts development mode with hot reloading
- TypeCheck: `npm run typecheck` - Runs TypeScript type checking

## Dependencies
The plugin uses several key dependencies:
- React and React DOM for UI components
- Vidstack for media playback
- Jotai for state management
- Various utility libraries for specific functionality

See [package.json](mdc:package.json) for the complete list of dependencies.
