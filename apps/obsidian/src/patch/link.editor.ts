import { around } from "monkey-around";
import type { App, Component, MarkdownEditView } from "obsidian";

import type { LinkEvent } from "./event";
import { normNewPanelType } from "./mod-evt";
import { getInstancePrototype, getRunningViewInstance } from "./utils";

declare module "obsidian" {
  interface MarkdownEditView {
    triggerClickableToken(
      token: { type: string; text: string; start: number; end: number },
      newLeaf: boolean | PaneType,
    ): void;
  }
  interface MarkdownView {
    // for safe access
    editMode?: MarkdownEditView;
  }
}

export default async function patchEditorClick<
  P extends Component & { app: App },
>(
  plugin: P,
  { onExternalLinkClick, onInternalLinkClick }: Partial<LinkEvent<P>>,
) {
  // eslint-disable-next-line @typescript-eslint/no-this-alias
  const view = await getRunningViewInstance("markdown", plugin);
  if (!view.editMode) {
    console.error(
      "MarkdownView.editMode is not available, cannot patch editor click",
    );
    return;
  }
  plugin.register(
    around(getInstancePrototype(view.editMode), {
      triggerClickableToken: (next_1) =>
        async function (this: MarkdownEditView, token, newLeaf, ...args) {
          const fallback = () => next_1.call(this, token, newLeaf, ...args);
          if (onInternalLinkClick && "internal-link" === token.type) {
            try {
              await onInternalLinkClick.call(
                plugin,
                token.text,
                this.file.path,
                normNewPanelType(newLeaf),
                fallback,
              );
            } catch (e) {
              console.error(
                "onInternalLinkClick error in editor, fallback to default",
                e,
              );
              fallback();
            }
          } else if (onExternalLinkClick && "external-link" === token.type) {
            try {
              await onExternalLinkClick.call(
                plugin,
                token.text,
                normNewPanelType(newLeaf),
                fallback,
              );
            } catch (e) {
              console.error(
                "onExternalLinkClick error in editor, fallback to default",
                e,
              );
              fallback();
            }
          } else fallback();
        },
    }),
  );
  console.debug("editor click patched");
}
