import { around } from "monkey-around";
import type { Plugin } from "obsidian";
import type MediaExtended from "@/mx-main";
import "./embed-patch.global.css";

import defineStatefulDecoration from "./state";
import type { Extension } from "@codemirror/state";
import type { LatestPluginSettings } from "@/settings/registry/atom";

export default function setupEmbedWidget(plugin: MediaExtended) {
  const config: Extension[] = [];
  plugin.registerEditorExtension(config);
  const updateConfig = (settings: LatestPluginSettings) => {
    config.length = 0;
    config.push(defineStatefulDecoration({ plugin, settings }));
  };
  plugin.settings.loaded.then(updateConfig);

  using stack = new DisposableStack();
  stack.use(
    plugin.settings.subscribe("link.handle-direct-url", (_v, _p, settings) => {
      updateConfig(settings);
    }),
  );
  stack.use(
    plugin.settings.subscribe("link.handle-hosted", (_v, _p, settings) => {
      updateConfig(settings);
    }),
  );
  const disposables = stack.move();
  plugin.register(() => disposables.dispose());

  imgErrorDataLabel(plugin);
}

const cmClasses = ["cm-line", "cm-content"];
const errorLabel = "mxError";
function isParentCm(el: HTMLElement) {
  if (!el.parentElement) return false;
  const parent = el.parentElement;
  return cmClasses.some((cls) => parent.classList.contains(cls));
}
function onError(this: HTMLImageElement) {
  if (!isParentCm(this)) return;
  this.dataset[errorLabel] = "";
  this.removeEventListener("load", onLoad);
}
function onLoad(this: HTMLImageElement) {
  if (!isParentCm(this)) return;
  delete this.dataset[errorLabel];
  this.removeEventListener("error", onError);
}
function imgErrorDataLabel(plugin: Plugin) {
  plugin.register(
    around(window, {
      // @ts-ignore
      createEl: (next) =>
        function () {
          // @ts-ignore
          // biome-ignore lint/style/noArguments: <explanation>
          const el = next.apply(this, arguments);

          if (el instanceof HTMLImageElement) {
            el.addEventListener("error", onError, { once: true });
            el.addEventListener("load", onLoad, { once: true });
          }
          return el;
        },
    }),
  );
}
