/* eslint-disable @typescript-eslint/naming-convention */
import type { EditorView } from "@codemirror/view";
import { WidgetType } from "@codemirror/view";
import { Platform } from "obsidian";
import { parseSizeFromLinkTitle, setSize } from "@/lib/size-syntax";
import { MediaRenderChild } from "@/media-view/url-embed";
import type MediaExtended from "@/mx-main";
import {
  isMediaInfoEqual,
  type MediaInfoSrc,
  type UrlMediaInfo,
} from "@/def/media-info";

class UrlMediaRenderChild extends MediaRenderChild {
  constructor(
    containerEl: HTMLElement,
    plugin: MediaExtended,
    onEditClick?: () => void,
  ) {
    super(containerEl, plugin, onEditClick);
    containerEl.addClasses(["mx-external-media-embed"]);
  }
}

type ElementWithInfo = HTMLElement & {
  playerInfo:
    | {
        title: string;
        start: number;
        end: number;
        child: UrlMediaRenderChild;
        url: MediaInfoSrc<UrlMediaInfo>;
      }
    | undefined;
};

export class InvalidNoticeWidget extends WidgetType {
  constructor(
    public message: string,
    public start: number,
    public end: number,
  ) {
    super();
  }
  toDOM(view: EditorView): HTMLElement {
    const dom = document.createElement("div");
    this.hookClickHandler(view, dom);
    dom.className = "external-embed mx-external-media-embed mx-invalid-notice";
    dom.createEl("p", { text: this.message });
    return dom;
  }
  eq(widget: WidgetType): boolean {
    return (
      widget instanceof InvalidNoticeWidget && widget.message === this.message
    );
  }
  updateDOM(dom: HTMLElement): boolean {
    if (dom.textContent !== this.message) {
      dom.empty();
      dom.createEl("p", { text: this.message });
      return true;
    }
    return false;
  }
  hookClickHandler(view: EditorView, el: HTMLElement) {
    el.addEventListener("click", (evt) => {
      if (!evt.defaultPrevented) {
        this.selectElement(view, el);
        evt.preventDefault();
      }
    });
  }
  selectElement(view: EditorView, el: HTMLElement) {
    const info = (el as ElementWithInfo).playerInfo;
    const { start } = info ?? this;
    let { end } = info ?? this;
    try {
      if (start < 0 || end < 0) {
        const pos = view.posAtDOM(el);
        view.dispatch({ selection: { head: pos, anchor: pos } });
        view.focus();
      } else {
        if (Platform.isMobile) end = start;
        view.dispatch({ selection: { head: start, anchor: end } });
        view.focus();
      }
    } catch (e) {
      // ignore
    }
  }
}

export class UrlPlayerWidget extends WidgetType {
  setPos(dom: HTMLElement) {
    const info = (dom as ElementWithInfo).playerInfo;
    if (info) {
      info.start = this.start;
      info.end = this.end;
    }
  }
  selectElement(view: EditorView, el: HTMLElement) {
    const info = (el as ElementWithInfo).playerInfo;
    const { start } = info ?? this;
    let { end } = info ?? this;
    try {
      if (start < 0 || end < 0) {
        const pos = view.posAtDOM(el);
        view.dispatch({ selection: { head: pos, anchor: pos } });
        view.focus();
      } else {
        if (Platform.isMobile) end = start;
        view.dispatch({ selection: { head: start, anchor: end } });
        view.focus();
      }
    } catch (e) {
      // ignore
    }
  }
  resizeWidget(view: EditorView, el: HTMLElement) {
    window.ResizeObserver &&
      new window.ResizeObserver(() => {
        return view.requestMeasure();
      }).observe(el, { box: "border-box" });
  }

  constructor(
    public plugin: MediaExtended,
    public media: MediaInfoSrc<UrlMediaInfo>,
    public title: string,
    public start: number,
    public end: number,
  ) {
    super();
  }

  setInfo(dom: HTMLElement, child: UrlMediaRenderChild) {
    (dom as ElementWithInfo).playerInfo = {
      title: this.title,
      start: this.start,
      end: this.end,
      child,
      url: this.media,
    };
  }

  updateDOM(domToUpdate: HTMLElement): boolean {
    const info = (domToUpdate as ElementWithInfo).playerInfo;
    if (!info) return false;
    const { title } = info;
    if (isMediaInfoEqual(info.url.info, this.media.info)) {
      if (this.title !== title) {
        info.title = this.title;
        this.applyTitle(domToUpdate);
        this.setPos(domToUpdate);
      }
    } else {
      info.child.setSource(this.media);
    }
    return true;
  }
  destroy(domToDestroy: HTMLElement): void {
    const info = (domToDestroy as ElementWithInfo).playerInfo;
    if (info) {
      (domToDestroy as ElementWithInfo).playerInfo = undefined;
      info.child.unload();
    }
  }
  eq(other: UrlPlayerWidget): boolean {
    return (
      isMediaInfoEqual(this.media.info, other.media.info) &&
      this.title === other.title
    );
  }

  setDOM(view: EditorView, container: HTMLDivElement) {
    container.tabIndex = -1;
    // container.setAttr("src", this.linktext);
    this.applyTitle(container);
    // container.addEventListener(
    //   "mousedown",
    //   (evt) => 0 === evt.button && view.hasFocus && evt.preventDefault(),
    // );
    const child = new UrlMediaRenderChild(container, this.plugin, () => {
      this.selectElement(view, container);
    });
    child.setSource(this.media);
    child.load();
    this.setInfo(container, child);
    this.resizeWidget(view, container);
  }
  private applyTitle(dom: HTMLElement) {
    setSize(dom, parseSizeFromLinkTitle(this.title));
  }
  toDOM(view: EditorView): HTMLDivElement {
    const container = createDiv();
    container.style.display = "none";
    container.setAttr("src", this.media.info.url.toString());
    container.addClasses([
      "external-embed",
      "cm-embed-block",
      "mx-media-embed",
    ]);
    container.style.removeProperty("display");
    this.setDOM(view, container);

    return container;
  }
}
Object.defineProperty(UrlPlayerWidget.prototype, "estimatedHeight", {
  get: () => 100,
  enumerable: false,
  configurable: true,
});
