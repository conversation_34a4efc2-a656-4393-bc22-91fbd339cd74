import { syntaxTree, tokenClassNodeProp } from "@codemirror/language";
import type { EditorState } from "@codemirror/state";
import type { WidgetType } from "@codemirror/view";
import { Decoration } from "@codemirror/view";
// import { editorInfo<PERSON>ield } from "obsidian";
import type MediaExtended from "@/mx-main";

import { isMdFavorInternalLink } from "./utils";
import { InvalidNoticeWidget, UrlPlayerWidget } from "./widget";
import { parseUrl } from "@/def/url-parse";
import { validateFileUri } from "@/link/utils/file-uri";
import type { LatestPluginSettings } from "@/settings/registry/atom";

const getPlayerDecos = (
  ctx: { plugin: MediaExtended; settings: LatestPluginSettings },
  state: EditorState,
  decos: ReturnType<Decoration["range"]>[],
  from?: number,
  to?: number,
) => {
  // const mdView = state.field(editorInfoField),
  // sourcePath = mdView.file?.path ?? "";

  // if (!sourcePath) console.warn("missing sourcePath", mdView);

  const doc = state.doc;
  let imgInfo: { alt?: string; url?: string; imgMarkLoc: number } | null = null;

  syntaxTree(state).iterate({
    from,
    to,
    enter: ({ type, from, to }) => {
      const nodeTypes = new Set(
        (type.prop(tokenClassNodeProp) as string | undefined)?.split(" "),
      );
      if (nodeTypes.size === 0) return;
      if (nodeTypes.has("image-marker")) {
        imgInfo = { imgMarkLoc: from };
        return;
      }

      if (!imgInfo) return;

      if (nodeTypes.has("image-alt-text") && !nodeTypes.has("formatting")) {
        imgInfo.alt = doc.sliceString(from, to);
        return;
      }

      if (nodeTypes.has("url") && !nodeTypes.has("formatting")) {
        imgInfo.url = doc.sliceString(from, to);
        return;
      }

      if (!(nodeTypes.has("formatting") && imgInfo.url)) return;

      const { imgMarkLoc, alt, url } = imgInfo;
      imgInfo = null;
      if (isMdFavorInternalLink(url)) return;
      const urlInfo = parseUrl(url);

      if (!urlInfo) return;
      if (
        urlInfo.info.type === "url:direct" &&
        urlInfo.info.url.protocol === "file:"
      ) {
        const result = validateFileUri(urlInfo.info.url, ctx.plugin.app);
        if (result.type !== "success") {
          addDeco(
            new InvalidNoticeWidget(
              "Please use internal embed instead of file url embed",
              imgMarkLoc,
              to,
            ),
            imgMarkLoc,
            to,
          );
        }
      } else if (ctx.plugin.link.shouldHandleLinkLogic(urlInfo, ctx.settings)) {
        const widget = new UrlPlayerWidget(
          ctx.plugin,
          urlInfo,
          alt ?? "",
          imgMarkLoc,
          to,
        );
        addDeco(widget, imgMarkLoc, to);
      }
    },
  });
  function addDeco(widget: WidgetType, from: number, to: number) {
    const side = -1; // place the player widget after default live preview widget
    const { from: lineFrom, text: lineText } = doc.lineAt(from);
    const isWholeLine =
      lineText.substring(0, from - lineFrom).trim() === "" &&
      lineText.substring(to - lineFrom).trim() === "";
    if (isWholeLine) {
      decos.push(
        Decoration.widget({ widget, block: true, side }).range(lineFrom),
      );
    } else {
      decos.push(Decoration.widget({ widget, side }).range(from));
    }
  }
};

export default getPlayerDecos;
