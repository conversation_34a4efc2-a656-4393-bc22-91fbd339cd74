export const getUrlFromMarkdown = (text: string): string => {
  const trimmedText = text.trim();
  if (trimmedText.startsWith("<")) {
    const trailing = trimmedText.indexOf(">");
    if (-1 !== trailing) return trimmedText.substring(1, trailing);
  }
  const matches = trimmedText.match(/\s/);
  return matches ? trimmedText.substring(0, matches.index) : trimmedText;
};

export const isMdFavorInternalLink = (e: string) => {
  return (
    !(!e.startsWith("./") && !e.startsWith("../")) || -1 === e.indexOf(":")
  );
};

export const parseLinktextAlias = (linktext: string) => {
  let title = "";
  const pipeLoc = linktext.indexOf("|");
  const hasAlias = pipeLoc > 0;

  let processedLink = linktext;

  if (hasAlias) {
    title = processedLink.substring(pipeLoc + 1).trim();
    processedLink = processedLink.substring(0, pipeLoc).trim();
  } else {
    processedLink = processedLink.trim();
    title = getAltTextForInternalLink(processedLink);
  }

  if (processedLink.endsWith("\\")) {
    processedLink = processedLink.substring(0, processedLink.length - 1);
  }

  return {
    href: normalizeSpace(processedLink).trim(),
    title: title,
    isAlias: hasAlias,
  };
};

const getAltTextForInternalLink = (text: string) => {
  return text
    .split("#")
    .filter((e) => !!e)
    .join(" > ")
    .trim();
};

const NO_BREAK_SPACE = /\u00A0/g;
export const normalizeSpace = (text: string) => {
  return text.replaceAll(NO_BREAK_SPACE, " ");
};
