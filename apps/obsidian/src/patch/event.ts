import type { Component, PaneType, TFile } from "obsidian";

export interface LinkEvent<P extends Component> {
  onExternalLinkClick(
    this: P,
    url: string,
    newLeaf: false | PaneType,
    fallback: () => void,
  ): any;
  onInternalLinkClick(
    this: P,
    linktext: string,
    sourcePath: string,
    newLeaf: false | PaneType | undefined,
    fallback: () => void,
  ): any;
}

export type MediaEmbedSource = "popover" | "view" | "embed";
export type MediaEmbedRenderHandler = (
  type: "video" | "audio",
  el: HTMLElement,
  file: TFile,
  source: MediaEmbedSource,
  fallback: () => Promise<void>,
) => Promise<void>;
