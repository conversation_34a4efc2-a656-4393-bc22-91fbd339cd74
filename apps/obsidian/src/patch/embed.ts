import type { EmbedCreator, Plugin } from "obsidian";
import type { Size } from "@/lib/size-syntax";
import { parseSizeFromLinkTitle, setSize } from "@/lib/size-syntax";
import { MediaRenderChild } from "@/media-view/url-embed";
import { FileMediaEmbed } from "@/media-view/file-embed";

import type MxPlugin from "@/mx-main";
import setupEmbedWidget from "./embed-widget";
import { reloadMarkdownPreview } from "./utils";
import { mediaExtensions } from "@/def/media-type";
import {
  extractFirstMarkdownLink,
  parseMarkdown,
} from "@/lib/parse/utils/markdown";
import { parseUrl } from "@/def/url-parse";
import type { MediaInfoSrc, UrlMediaInfo } from "@/def/media-info";

export default function injectMediaEmbed(plugin: MxPlugin) {
  injectFileMediaEmbed(
    plugin,
    (info, file, subpath) => new FileMediaEmbed(info, file, subpath, plugin),
  );
  injectUrlMediaEmbed(plugin);
  setupEmbedWidget(plugin);
}

function injectFileMediaEmbed(plugin: Plugin, embedCreator: EmbedCreator) {
  const { app } = plugin;
  const revertBackup = unregisterExistingEmbed(mediaExtensions);
  const unregister = registerEmbed(mediaExtensions, embedCreator);
  plugin.register(() => {
    unregister();
    revertBackup();
  });
  // reload to apply embed changes
  reloadMarkdownPreview(app.workspace);
  plugin.register(() => {
    // reload to revert embed changes
    reloadMarkdownPreview(app.workspace);
  });

  function registerEmbed(exts: string[], newCreator: EmbedCreator) {
    app.embedRegistry.registerExtensions(exts, newCreator);
    return () => {
      app.embedRegistry.unregisterExtensions(exts);
    };
  }
  function unregisterExistingEmbed(exts: string[]) {
    const creatorBackup: (EmbedCreator | undefined)[] = exts.map(
      (ext) => app.embedRegistry.embedByExtension[ext],
    );
    app.embedRegistry.unregisterExtensions(exts);
    return () => {
      exts.forEach((ext, i) => {
        const creator = creatorBackup[i];
        creator && app.embedRegistry.registerExtension(ext, creator);
      });
    };
  }
}

class UrlEmbedMarkdownRenderChild extends MediaRenderChild {
  constructor(
    public info: MediaInfoSrc<UrlMediaInfo>,
    public containerEl: HTMLElement,
    public plugin: MxPlugin,
  ) {
    super(containerEl, plugin);
    containerEl.addClasses(["mx-external-media-embed"]);
  }
  onload() {
    this.setSource(this.info);
    super.onload();
  }
}

function injectUrlMediaEmbed(plugin: MxPlugin) {
  plugin.registerMarkdownPostProcessor((el, ctx) => {
    for (const img of el.querySelectorAll("img")) {
      const info = extractSourceFromImg(img);
      if (!info) continue;
      replace(info, img);
    }
    for (const iframe of el.querySelectorAll<HTMLIFrameElement>(
      'iframe.external-embed[src*="youtube.com/embed/"]',
    )) {
      const sourceText = ctx.getSectionInfo(iframe)?.text;
      const info =
        extractSourceFromMarkdown(sourceText) ??
        extractSourceFromIframe(iframe);
      if (!info) continue;
      const src = parseUrl(info.url);
      if (!src) continue;
      replace(info, iframe);
    }

    function replace(embed: EmbedSource, target: HTMLElement) {
      const src = parseUrl(embed.url);
      if (
        !src ||
        (src.info.url.protocol !== "http:" &&
          src.info.url.protocol !== "https:") ||
        !plugin.link.shouldHandleLink(src)
      )
        return;
      const newWarpper = createDiv({
        cls: ["media-embed", "external-embed", "is-loaded"],
        attr: { src: src.info.url.href },
      });
      setSize(newWarpper, embed);
      target.replaceWith(newWarpper);
      const child = new UrlEmbedMarkdownRenderChild(src, newWarpper, plugin);
      ctx.addChild(child);
    }
  });
}

interface EmbedSource {
  url: string;
  title: string;
  size: Size | null;
}

function extractSourceFromImg(img: HTMLImageElement): EmbedSource | null {
  const linkTitle = img.alt;
  const srcText = img.src;

  if (!srcText) return null;

  return { url: srcText, ...parseSizeFromLinkTitle(linkTitle) };
}

function extractSourceFromMarkdown(
  md: string | null | undefined,
): EmbedSource | null {
  if (!md) return null;
  const parsed = parseMarkdown(md);
  const link = extractFirstMarkdownLink(md, parsed);
  if (!link) return null;
  const { url, display } = link;
  return { url, ...parseSizeFromLinkTitle(display) };
}

function extractSourceFromIframe(
  iframe: HTMLIFrameElement,
): EmbedSource | null {
  console.warn("cannot get source text of iframe, use src instead");
  const srcText = iframe.src;
  if (!srcText) return null;
  return { url: srcText, title: "", size: null };
}
