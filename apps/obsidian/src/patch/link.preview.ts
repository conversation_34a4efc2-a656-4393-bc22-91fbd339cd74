import { around } from "monkey-around";
import type { Component, Plugin, PreviewEventHanlder } from "obsidian";
import { MarkdownPreviewRenderer } from "obsidian";
import type { LinkEvent } from "./event";
import { isModEvent } from "./mod-evt";
import { getInstancePrototype } from "./utils";

export default function patchPreviewClick<P extends Component>(
  plugin: P,
  events: Partial<LinkEvent<P>>,
) {
  const unloadPatchHook = around(
    MarkdownPreviewRenderer as MDPreviewRendererCtor,
    {
      registerDomEvents: (next) =>
        function (this: MarkdownPreviewRenderer, _el, helper, ...args) {
          patchPreviewEventHanlder(helper, events, plugin);
          unloadPatchHook();
          console.debug("preview click patched");
          return next.call(this, _el, helper, ...args);
        },
    },
  );
  plugin.register(unloadPatchHook);
}

function patchPreviewEventHanlder<P extends Component>(
  handler: PreviewEventHanlder,
  { onExternalLinkClick, onInternalLinkClick }: Partial<LinkEvent<P>>,
  plugin: P,
) {
  plugin.register(
    around(getInstancePrototype(handler), {
      ...(onExternalLinkClick
        ? {
            onExternalLinkClick: (next) =>
              async function (
                this: PreviewEventHanlder,
                evt,
                target,
                link,
                ...args
              ) {
                const fallback = () =>
                  next.call(this, evt, target, link, ...args);
                evt.preventDefault();
                try {
                  await onExternalLinkClick.call(
                    plugin,
                    link,
                    isModEvent(evt),
                    fallback,
                  );
                } catch (e) {
                  console.error(
                    "onExternalLinkClick error in preview, fallback to default",
                    e,
                  );
                  fallback();
                }
              },
          }
        : {}),
      ...(onInternalLinkClick
        ? {
            onInternalLinkClick: (next) =>
              async function (
                this: PreviewEventHanlder,
                evt,
                target,
                link,
                ...args
              ) {
                const fallback = () =>
                  next.call(this, evt, target, link, ...args);
                evt.preventDefault();
                try {
                  await onInternalLinkClick.call(
                    plugin,
                    link,
                    this.info?.file?.path ?? "",
                    isModEvent(evt),
                    fallback,
                  );
                } catch (e) {
                  console.error(
                    "onInternalLinkClick error in preview, fallback to default",
                    e,
                  );
                  fallback();
                }
              },
          }
        : {}),
    }),
  );
}

declare module "obsidian" {
  class PreviewEventHanlder {
    app: App;
    onInternalLinkDrag(
      evt: MouseEvent,
      delegateTarget: HTMLElement,
      linktext: string,
    ): void;
    onInternalLinkClick(
      evt: MouseEvent,
      delegateTarget: HTMLElement,
      linktext: string,
    ): void;
    onInternalLinkRightClick(
      evt: MouseEvent,
      delegateTarget: HTMLElement,
      linktext: string,
    ): void;
    onExternalLinkClick(
      evt: MouseEvent,
      delegateTarget: HTMLElement,
      href: string,
    ): void;
    onInternalLinkMouseover(
      evt: MouseEvent,
      delegateTarget: HTMLElement,
      href: string,
    ): void;
    onTagClick(evt: MouseEvent, delegateTarget: HTMLElement, tag: string): void;
    info?: MarkdownView | MarkdownFileInfo;
  }
}

type MDPreviewRendererCtor = typeof MarkdownPreviewRenderer & {
  registerDomEvents(
    el: HTMLElement,
    helper: PreviewEventHanlder,
    isBelongTo: (el: HTMLElement) => boolean,
  ): void;
  belongsToMe(
    target: HTMLElement,
    el: HTMLElement,
    isBelongTo: (el: HTMLElement) => boolean,
  ): boolean;
};
