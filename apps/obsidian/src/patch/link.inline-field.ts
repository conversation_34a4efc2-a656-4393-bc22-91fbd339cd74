import { around } from "monkey-around";
import {
  MarkdownView,
  type PaneType,
  type Component,
  type App,
} from "obsidian";
import { isModEvent } from "./mod-evt";
import { toURL } from "@mx/shared/utils/to-url";

export default function patchInlineUrl<P extends Component & { app: App }>(
  plugin: P,
  {
    onExternalLinkClick,
  }: {
    onExternalLinkClick: (
      this: P,
      evt: MouseEvent,
      url: string,
      newLeaf: false | PaneType,
    ) => void;
  },
) {
  const clickHandler = (e: MouseEvent) => {
    const target = e.target as HTMLElement;
    if (!target.instanceOf(HTMLElement)) return;
    if (
      !target.matches(
        ".metadata-property .metadata-property-value .external-link",
      )
    )
      return;
    const main = target.closest<HTMLElement>(".metadata-property");
    if (!main) return;
    const key = main.dataset.propertyKey;
    if (key !== "video" && key !== "audio" && key !== "media") return;
    if (!target.textContent) return;
    if (toURL(target.textContent)) {
      onExternalLinkClick.call(plugin, e, target.textContent, isModEvent(e));
      return;
    }
  };
  const unload = around(MarkdownView.prototype, {
    onload: (next) =>
      function (this: MarkdownView) {
        this.registerDomEvent(this.containerEl, "click", clickHandler, {
          capture: true,
        });
        return next.call(this);
      },
  });
  plugin.register(() => {
    unload();
    for (const leaf of plugin.app.workspace.getLeavesOfType("markdown")) {
      leaf.view.containerEl.removeEventListener("click", clickHandler);
    }
  });
}
