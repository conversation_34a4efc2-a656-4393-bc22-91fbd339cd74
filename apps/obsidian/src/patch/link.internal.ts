import { around } from "monkey-around";
import { type Component, Workspace } from "obsidian";
import type { LinkEvent } from "./event";
import { normNewPanelType } from "./mod-evt";

export default function patchLinktextOpen<P extends Component>(
  plugin: P,
  { onInternalLinkClick }: Pick<LinkEvent<P>, "onInternalLinkClick">,
) {
  // eslint-disable-next-line @typescript-eslint/no-this-alias

  plugin.register(
    around(Workspace.prototype, {
      openLinkText: (next) =>
        async function (
          this: Workspace,
          linktext,
          sourcePath,
          newLeaf,
          openViewState,
          ...args
        ) {
          const fallback = () =>
            next.call(
              this,
              linktext,
              sourcePath,
              newLeaf,
              openViewState,
              ...args,
            );
          try {
            await onInternalLinkClick.call(
              plugin,
              linktext,
              sourcePath,
              normNewPanelType(newLeaf),
              fallback,
            );
          } catch (e) {
            console.error(
              "onInternalLinkClick error in openLinktext, fallback to default",
              e,
            );
            fallback();
          }
        },
    }),
  );
}
