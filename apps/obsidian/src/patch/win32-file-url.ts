/**
 * Fixes incorrectly formatted Windows file:// URLs obsidian generates
 * by properly encoding Windows paths according to the URL specification.
 *
 * @see {@link https://forum.obsidian.md/t/incorrect-file-urls-generated-by-drop-on-windows/22429} Original issue report
 *
 * @example
 * // Input:  file:///C:/path%5Cto%5Cfile.txt
 * // Output: file:///C|/path/to/file.txt
 *
 * @param url - The URL object to patch
 * @returns A new URL object with properly formatted Windows file path
 */
export function normalizeWin32FileUri(url: URL): URL {
  if (url.protocol !== "file:") {
    return url;
  }

  // Check if this is a Windows path (starts with /C: or /D: etc)
  const windowsDriveMatch = url.pathname.match(
    /^\/(?<drive>[A-Z]+):(?<path>.+)/i,
  );
  if (!windowsDriveMatch) {
    return url;
  }

  const { drive, path } = windowsDriveMatch.groups! as Record<
    "drive" | "path",
    string
  >;

  if (!path.includes("%5C")) {
    return url;
  }

  // 1. Decode the URL to get actual backslashes
  const decodedPath = decodeURIComponent(path);

  // 2. Replace backslashes with forward slashes
  const normalizedPath = decodedPath.replace(/\\+/g, "/");

  return new URL(`file:///${drive}:${normalizedPath}`);
}
