import type { Plugin } from "obsidian";

export default function fixLinkLabel(plugin: Plugin) {
  plugin.registerMarkdownPostProcessor((el) => {
    for (const a of el.querySelectorAll("a")) {
      const label = a.getAttr("aria-label");
      if (!label) continue;
      try {
        // decode uri to make uri more readable
        const newLabel = decodeURI(label);
        if (label !== newLabel) {
          a.setAttr("aria-label", newLabel);
        }
      } catch {
        // if decode failed, do nothing
      }
    }
  });
}
