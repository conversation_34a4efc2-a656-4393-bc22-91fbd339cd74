import type { TextTrackInfo } from "@/def/track-info";
import { assertNever } from "@std/assert/unstable-never";
import type { PaneType, SplitDirection, Workspace } from "obsidian";
import { trackLinkToUrl, URL_TRACK_VIEW_TYPE } from "./url-view";

export async function openTrack(
  track: TextTrackInfo,
  ctx: {
    workspace: Workspace;
    newLeaf?: PaneType | false;
    direction?: SplitDirection;
  },
) {
  if (track.type === "internal.resolved") {
    await ctx.workspace.openLinkText(track.src.path, "", ctx.newLeaf);
  } else if (track.type === "url" || track.type === "file") {
    const state = trackLinkToUrl(track);
    if (!state) return;
    const leaf =
      ctx.newLeaf === "split"
        ? ctx.workspace.getLeaf(ctx.newLeaf, ctx.direction)
        : ctx.workspace.getLeaf(ctx.newLeaf);
    await leaf.setViewState({
      type: URL_TRACK_VIEW_TYPE,
      state,
      active: true,
    });
  } else {
    assertNever(track);
  }
}
