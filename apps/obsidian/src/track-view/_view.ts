import type { Editor } from "prosekit/core";
import type { WebVTTEditorExtension } from "@mx/ui";
import { Notice, type ItemView } from "obsidian";
import type { createStore } from "jotai";
import type MxPlugin from "@/mx-main";
import { trackInfoAtom } from "@/def/atom/track";
import { findLinkedMediaLeaf } from "@/link/leaf/finder";

export interface ITrackView extends ItemView {
  editor: Editor<WebVTTEditorExtension> | null;
  toggleSearch: (enabled?: boolean) => void;
  store: ReturnType<typeof createStore>;
  plugin: MxPlugin;
}

export function registerTrackViewScopes(view: ITrackView) {
  view.scope?.register(null, "Escape", () => {
    view.toggleSearch(false);
  });
  view.scope?.register(null, "F3", () => {
    view.editor?.commands.findNext();
  });
  view.scope?.register(["Shift"], "F3", () => {
    view.editor?.commands.findPrev();
  });
}

export function addOpenLinkedMediaAction(view: ITrackView): void {
  view.addAction("square-play", "Open linked media", async () => {
    const trackInfo = view.store.get(trackInfoAtom);
    if (!trackInfo) {
      new Notice("No track opened");
      return;
    }
    const media = (
      await view.plugin.transcriptLoader.getLinkedMedia(trackInfo)
    ).at(0);
    if (!media) {
      new Notice("No linked media found");
      return;
    }
    const existing = await findLinkedMediaLeaf(trackInfo, view.plugin);
    if (existing) {
      await view.plugin.app.workspace.revealLeaf(existing.leaf);
    } else {
      await view.plugin.link.openMedia(
        { info: media },
        { fromUser: false, newLeaf: "split" },
      );
    }
  });
}
