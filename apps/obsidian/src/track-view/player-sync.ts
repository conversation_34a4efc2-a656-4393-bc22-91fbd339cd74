import "./style.css";

import { pluginAtom } from "@/def/atom/media";
import { trackInfoAtom } from "@/def/atom/track";
import { getTrackInfoID } from "@/def/track-info";
import { findLinkedMediaLeaf } from "@/link/leaf/finder";
import { player<PERSON>tom } from "@mx/shared/hooks/use-player";
import { atom } from "jotai";
import { observe } from "jotai-effect";
import { type EventRef, debounce } from "obsidian";
import type { ITrackView } from "./_view";

/**
 * Toggle to enable/disable player connection
 */
const playerSyncToggleAtom = atom(true);
const playerConnectedAtom = atom((get) => {
  if (!get(playerSyncToggleAtom)) return null;
  return get(playerAtom) !== null;
});

function registerPlayerSyncObserver(view: ITrackView): Disposable {
  using stack = new DisposableStack();
  stack.defer(
    observe((get, set) => {
      const plugin = get(pluginAtom);

      const trackInfo = get(trackInfoAtom);
      if (!trackInfo) {
        set(playerAtom, null);
        return;
      }
      const trackId = getTrackInfoID(trackInfo);
      if (!get(playerSyncToggleAtom)) {
        set(playerAtom, null);
        return;
      }

      using stack = new DisposableStack();
      const register = (ref: EventRef) => {
        stack.defer(() => plugin.app.workspace.offref(ref));
      };

      const updatePlayer = debounce(
        async () => {
          const player = (
            await findLinkedMediaLeaf(trackInfo, plugin)
          )?.view?.getPlayer();
          if (player) {
            set(playerAtom, player);
            player.textTracks.getById(trackId)?.setMode("showing");
          } else {
            set(playerAtom, null);
          }
        },
        200,
        true,
      );

      updatePlayer();
      register(plugin.app.workspace.on("active-leaf-change", updatePlayer));
      register(plugin.app.workspace.on("layout-change", updatePlayer));

      const disposable = stack.move();
      return () => {
        disposable.dispose();
      };
    }, view.store),
  );
  return stack.move();
}

function addDisconnectAction(view: ITrackView): Disposable {
  using stack = new DisposableStack();
  const actionEl = view.addAction(
    "lucide-refresh-cw-off",
    "Stop syncing with player",
    () => {
      view.store.set(playerSyncToggleAtom, false);
      // new Notice(
      //   "Track no longer sync with player progress, to reconnect, go to more options menu",
      // );
    },
  );
  actionEl.addClass("mx-connect-action");
  stack.defer(
    observe((get) => {
      if (get(playerSyncToggleAtom)) {
        actionEl.dataset.active = "";
      } else {
        delete actionEl.dataset.active;
      }
      if (get(playerConnectedAtom)) {
        actionEl.dataset.connected = "";
      } else {
        delete actionEl.dataset.connected;
      }
    }, view.store),
  );
  return stack.move();
}

export function registerPlayerSync(view: ITrackView): void {
  using stack = new DisposableStack();
  stack.use(registerPlayerSyncObserver(view));
  stack.use(addDisconnectAction(view));
  const disposable = stack.move();
  view.register(() => disposable.dispose());
}

export const handlePlayerSyncMenu: ITrackView["onPaneMenu"] =
  function handleConnectMenuOption(this: ITrackView, menu, _source) {
    if (!this.store.get(playerSyncToggleAtom)) {
      menu.addItem((item) =>
        item
          .setTitle("Sync with active player")
          .setSection("action")
          .setIcon("lucide-refresh-ccw")
          .onClick(() => this.store.set(playerSyncToggleAtom, true)),
      );
    } else {
      menu.addItem((item) =>
        item
          .setTitle("Stop syncing with player")
          .setSection("view")
          .setIcon("lucide-refresh-cw-off")
          .onClick(() => this.store.set(playerSyncToggleAtom, false)),
      );
    }
  };
