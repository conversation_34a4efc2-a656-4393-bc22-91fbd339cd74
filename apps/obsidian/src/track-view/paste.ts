import { parseTextTrackInfo } from "@/def/track-info";
import type MxPlugin from "@/mx-main";
import { htmlToMarkdown, Notice } from "obsidian";
import { WebVttTimestamp } from "@mx/ui";
import { timestampLinkFactory } from "@/link/insert/template";

export function handleTrackPaste(plugin: MxPlugin) {
  plugin.registerEvent(
    plugin.app.workspace.on("editor-paste", (evt, editor, info) => {
      const html = evt.clipboardData?.getData("text/html");
      if (!html) return;
      if (!html.includes('name="mx:track-src"')) return;
      const frag = new DOMParser().parseFromString(html, "text/html");
      const trackSrc = frag.querySelector('meta[name="mx:track-src"][content]');
      if (!trackSrc) return;
      const content = trackSrc.getAttribute("content");
      if (!content) return;
      const trackinfo = parseTextTrackInfo(content, {
        metadataCache: plugin.app.metadataCache,
        sourcePath: "",
      });
      // get metadata including kind from context
      if (!trackinfo) return;
      evt.preventDefault();
      const markdown = htmlToMarkdown(frag).trim().replace(/\n+/g, " ");
      (async () => {
        const [linedMedia] =
          await plugin.transcriptLoader.getLinkedMedia(trackinfo);
        if (!linedMedia) {
          throw new Error("No linked media found for track");
        }

        const firstCue = frag.querySelector('[data-part="cue"][data-start]');
        const firstCueStartText = firstCue?.getAttribute("data-start")!;
        const firstTimestampText = firstCue
          ?.querySelector('[data-part="timestamp"][data-time]')
          ?.getAttribute("data-time")!;
        const timestampText = firstTimestampText || firstCueStartText;
        if (!timestampText) {
          throw new Error("No timestamp found for track");
        }
        const timestamp = WebVttTimestamp.parse(timestampText);
        const timestampLink = timestampLinkFactory(
          {
            currentTime: timestamp.getTimestamp(),
            src: linedMedia,
          },
          {
            fileManager: plugin.app.fileManager,
            timestampOffset: 0,
          },
        )(info.file?.path ?? "");
        editor.replaceSelection(`${timestampLink} ${markdown}`);
      })().catch((err) => {
        console.error("Error pasting track", err);
        editor.replaceSelection(markdown);
      });
    }),
  );
}
