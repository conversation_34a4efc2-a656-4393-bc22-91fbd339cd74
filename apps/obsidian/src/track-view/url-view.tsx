import type { Workspace<PERSON>ea<PERSON>, <PERSON>u, ViewStateResult } from "obsidian";
import { ItemView, Platform, Scope } from "obsidian";
import type MediaExtended from "@/mx-main";
import { createStore } from "jotai";
import type { PaneMenuSource } from "@/lib/menu";
import { pluginAtom } from "@/def/atom/media";
import { renderView } from "@/media-view/_render";
import {
  showSearchAtom,
  trackEditorAtom,
  trackInfoAtom,
} from "@/def/atom/track";
import { Transcript } from "@/components/transcript";
import { handlePlayerSyncMenu, registerPlayerSync } from "./player-sync";
import { deserialize, serialize } from "./_state";
import { requireUrl } from "@/lib/node";
import { assertNever } from "@std/assert/unstable-never";
import {
  isTrackInfoEqual,
  urlToTrackLink,
  type LocalFileTrack,
  type RemoteTrack,
} from "@/def/track-info";
import {
  addOpenLinkedMediaAction,
  registerTrackViewScopes,
  type ITrackView,
} from "./_view";

export const URL_TRACK_VIEW_TYPE = "mx-url-track";

export type UrlTrackViewType = typeof URL_TRACK_VIEW_TYPE;

export type UrlTrackViewState = {
  src: URL;
};

export default class UrlTrackView extends ItemView implements ITrackView {
  // no need to manage scope manually,
  // as it's implicitly called and handled by the WorkspaceLeaf
  scope: Scope;
  navigation = true;
  store;

  getViewType(): UrlTrackViewType {
    return URL_TRACK_VIEW_TYPE;
  }
  getIcon(): string {
    return "notepad-text";
  }
  getDisplayText(): string {
    return "Track";
  }

  constructor(
    leaf: WorkspaceLeaf,
    public plugin: MediaExtended,
  ) {
    super(leaf);
    this.scope = new Scope(this.app.scope);
    this.store = createStore();
    this.store.set(pluginAtom, this.plugin);
    this.contentEl.addClasses(["mx", "custom"]);

    registerTrackViewScopes(this);
    registerPlayerSync(this);
    addOpenLinkedMediaAction(this);
  }

  render(): Disposable {
    return renderView({
      ctx: this,
      target: this.contentEl,
      children: <Transcript />,
    });
  }

  get editor() {
    return this.store.get(trackEditorAtom);
  }
  toggleSearch(enabled?: boolean) {
    if (enabled === undefined) {
      this.store.set(showSearchAtom, (curr) => !curr);
    } else {
      this.store.set(showSearchAtom, enabled);
    }
  }

  onPaneMenu(menu: Menu, menuSource: PaneMenuSource): void {
    super.onPaneMenu(menu, menuSource);
    handlePlayerSyncMenu.call(this, menu, menuSource);
  }

  getState(): Record<string, unknown> {
    const internal = super.getState();
    const src = this.store.get(trackInfoAtom);
    if (!src || src.type === "internal.resolved") return internal;
    const state = trackLinkToUrl(src);
    if (!state) return internal;
    return {
      ...internal,
      ...state,
    };
  }

  async setState(
    state: Record<string, unknown>,
    result: ViewStateResult,
  ): Promise<void> {
    await super.setState(state, result);
    const viewState = deserialize<UrlTrackViewState>(state);
    this.store.set(trackInfoAtom, (curr) => {
      const next = viewState?.src
        ? urlToTrackLink(viewState.src, { kind: "subtitles" })
        : null;
      if (isTrackInfoEqual(curr, next)) return curr;
      return next;
    });
  }

  protected async onOpen(): Promise<void> {
    await super.onOpen();
    this.#reset();
    this.#renderDisposables = this.render();
  }

  #renderDisposables: Disposable | null = null;
  #reset() {
    this.#renderDisposables?.[Symbol.dispose]();
    this.#renderDisposables = null;
  }
}

export function trackLinkToUrl(track: RemoteTrack | LocalFileTrack) {
  if (track.type === "url") {
    return serialize<UrlTrackViewState>({
      src: track.src,
    });
  }
  if (track.type === "file") {
    if (Platform.isDesktopApp) return null;
    return serialize<UrlTrackViewState>({
      src: requireUrl().pathToFileURL(track.src.path),
    });
  }
  assertNever(track);
}
