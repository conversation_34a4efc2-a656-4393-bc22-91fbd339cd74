import type { PaneMenuSource } from "@/lib/menu";
import type MediaExtended from "@/mx-main";
import { createStore } from "jotai";
import {
  EditableFileView,
  Scope,
  type WorkspaceLeaf,
  type TFile,
  type Menu,
} from "obsidian";
import { isTrackFile } from "@/def/media-type";
import { renderView } from "@/media-view/_render";
import {
  showSearch<PERSON>tom,
  trackEditorAtom,
  trackInfoAtom,
} from "@/def/atom/track";
import { Transcript } from "@/components/transcript";
import { fileToTrack } from "@/def/track-info";
import { pluginAtom } from "@/def/atom/media";
import { handlePlayerSyncMenu, registerPlayerSync } from "./player-sync";
import {
  addOpenLinkedMediaAction,
  registerTrackViewScopes,
  type ITrackView,
} from "./_view";

export const VAULT_TRACK_VIEW_TYPE = "mx-vault-track";

export type VaultTrackViewType = typeof VAULT_TRACK_VIEW_TYPE;

export default class VaultTrackFileView
  extends EditableFileView
  implements ITrackView
{
  allowNoFile = false;
  // inherit from EditableFileView, no need to set explicitly
  // navigation = true
  // no need to manage scope manually,
  // as it's implicitly called and handled by the WorkspaceLeaf
  scope: Scope;
  store;
  constructor(
    leaf: WorkspaceLeaf,
    public plugin: MediaExtended,
  ) {
    super(leaf);
    this.scope = new Scope(this.app.scope);
    const store = createStore();
    store.set(pluginAtom, this.plugin);
    this.store = store;

    this.contentEl.addClasses(["mx", "custom"]);

    registerTrackViewScopes(this);
    registerPlayerSync(this);
    addOpenLinkedMediaAction(this);
  }

  #reset() {
    this.#renderDisposables?.[Symbol.dispose]();
    this.#renderDisposables = null;
  }

  protected async onOpen(): Promise<void> {
    await super.onOpen();
    this.#reset();
    this.#renderDisposables = this.render();
  }
  async onClose() {
    this.#reset();
    await super.onClose();
  }

  get editor() {
    return this.store.get(trackEditorAtom);
  }
  toggleSearch(enabled?: boolean) {
    if (enabled === undefined) {
      this.store.set(showSearchAtom, (curr) => !curr);
    } else {
      this.store.set(showSearchAtom, enabled);
    }
  }

  async onLoadFile(file: TFile): Promise<void> {
    const track = fileToTrack(file, { kind: "subtitles" });
    if (!track) {
      throw new Error("File is not a track file");
    }
    this.store.set(trackInfoAtom, track);
  }
  onPaneMenu(menu: Menu, menuSource: PaneMenuSource): void {
    super.onPaneMenu(menu, menuSource);
    handlePlayerSyncMenu.call(this, menu, menuSource);
  }

  #renderDisposables: Disposable | null = null;
  render(): Disposable {
    return renderView({
      ctx: this,
      target: this.contentEl,
      shadowRootClassName: "shadow-root",
      children: <Transcript />,
    });
  }

  getIcon(): string {
    return "notepad-text";
  }
  getViewType() {
    return VAULT_TRACK_VIEW_TYPE;
  }
  canAcceptExtension(extension: string): boolean {
    return isTrackFile({ extension });
  }
}
