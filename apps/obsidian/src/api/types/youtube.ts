/**
 * Simplified YouTube metadata types for Obsidian plugin UI
 * Based on the API response from /apps/api/src/routes/youtube.ts
 */

export interface YouTubeThumbnail {
  url: string;
  size?: {
    width: number;
    height: number;
  };
}

export interface YouTubeChapter {
  title: string;
  /** Start time in seconds */
  start_time: number;
  /** End time in seconds */
  end_time: number;
}

/**
 * Simplified YouTube metadata containing only fields needed for UI
 */
export interface YouTubeMetadata {
  /** Video title */
  title: string;
  /** Video description */
  description: string;
  /** Duration in seconds */
  duration: number;
  /** Publication date in ISO 8601 format */
  published_at: string;
  /** Channel/uploader name */
  uploader_name: string;
  /** View count, null if unavailable */
  view_count: number | null;
  /** Like count, null if unavailable */
  like_count: number | null;
  /** Aspect ratio as string (e.g., "16:9") */
  aspect_ratio: string;
  /** Available thumbnails by quality */
  thumbnails: Partial<Record<
    "default" | "high" | "maxres" | "medium" | "standard",
    YouTubeThumbnail
  >>;
  /** Video tags */
  tags: string[];
  /** Video chapters, if available */
  chapters?: YouTubeChapter[];
}

/**
 * Custom error types for YouTube API operations
 */
export class YouTubeAPIError extends Error {
  constructor(
    message: string,
    public readonly statusCode?: number,
    public readonly originalError?: Error
  ) {
    super(message);
    this.name = "YouTubeAPIError";
  }
}

export class YouTubeNetworkError extends YouTubeAPIError {
  constructor(message: string, originalError?: Error) {
    super(message, undefined, originalError);
    this.name = "YouTubeNetworkError";
  }
}

export class YouTubeAuthError extends YouTubeAPIError {
  constructor(message: string, statusCode?: number) {
    super(message, statusCode);
    this.name = "YouTubeAuthError";
  }
}

export class YouTubeNotFoundError extends YouTubeAPIError {
  constructor(videoId: string) {
    super(`YouTube video not found: ${videoId}`, 404);
    this.name = "YouTubeNotFoundError";
  }
}

export class YouTubeProcessingError extends YouTubeAPIError {
  constructor(message: string) {
    super(message, 202);
    this.name = "YouTubeProcessingError";
  }
}
