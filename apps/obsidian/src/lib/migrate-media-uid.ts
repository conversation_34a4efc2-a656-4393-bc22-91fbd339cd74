import { MEDIA_ID_FIELD } from "@/def/media-id";
import iterateFiles from "@/lib/iterate-files";
import { getMediaInfoFromMeta } from "@/media-lib/extract";
import { createId } from "@paralleldrive/cuid2";
import type { FileManager, MetadataCache, TFile, Vault } from "obsidian";

export async function migrateMediaUid({
  fileManager,
  metadataCache,
  vault,
}: {
  fileManager: FileManager;
  metadataCache: MetadataCache;
  vault: Vault;
}) {
  const queue: Promise<TFile>[] = [];
  for (const file of iterateFiles(vault.getRoot())) {
    const meta = metadataCache.getFileCache(file);
    if (!meta) continue;
    if (meta.frontmatter?.[MEDIA_ID_FIELD]) continue;
    const media = getMediaInfoFromMeta(meta, {
      metadataCache,
      sourcePath: file.path,
    });
    if (!media) continue;
    queue.push(addUid(file));
  }
  return await Promise.all(queue);

  async function addUid(file: TFile) {
    await fileManager.processFrontMatter(file, (fm) => {
      if (MEDIA_ID_FIELD in fm) return;
      fm[MEDIA_ID_FIELD] = createId();
    });
    return file;
  }
}
