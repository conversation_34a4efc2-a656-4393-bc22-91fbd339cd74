import { type App, normalizePath, Notice, TFolder } from "obsidian";

export async function getAttachmentFolder(options: {
  folderPath?: string;
  app: App;
  sourcePath: string;
}): Promise<TFolder> {
  const { vault } = options.app;

  if (!options.folderPath) {
    const folderPath = await getDefaultAttachmentFolder(options);
    const folder = vault.getFolderByPath(folderPath);
    if (!folder) {
      throw new Error(`Attachment folder not found: ${folderPath}`);
    }
    return folder;
  }

  try {
    const parentPath = normalizePath(options.folderPath);
    const folder = await mkdirParent(options.app, parentPath);
    return folder;
  } catch (err) {
    new Notice(
      `Failed to create attachment folder ${options.folderPath}: ${
        err instanceof Error ? err.message : err
      }`,
    );
    throw err;
  }
}

async function getDefaultAttachmentFolder(ctx: {
  app: App;
  sourcePath: string;
}) {
  const random = Math.random().toString(36).substring(2);
  const newAttachmentPath =
    await ctx.app.fileManager.getAvailablePathForAttachment(
      random,
      ctx.sourcePath,
    );
  const folderPath = newAttachmentPath.replace(random, "");
  return normalizePath(folderPath) || "/";
}

async function mkdirParent(app: App, path: string): Promise<TFolder> {
  const existing = app.vault.getAbstractFileByPath(path);
  if (existing) {
    if (existing instanceof TFolder) return existing;
    throw new Error(`Path ${path} occupied by file`);
  }
  const parts = path.split("/").filter(Boolean);
  if (parts.length === 0) {
    return app.vault.getRoot();
  }
  const currentPath = parts[0]!;
  const ab = app.vault.getAbstractFileByPath(currentPath);
  let folder: TFolder;
  if (!ab) {
    folder = await app.vault.createFolder(currentPath);
  } else if (ab instanceof TFolder) {
    folder = ab;
  } else {
    throw new Error(`Path ${currentPath} exists but is not a folder`);
  }
  const remainingPath = parts.slice(1).join("/");
  if (!remainingPath) return folder;
  return mkdirParent(app, `${currentPath}/${remainingPath}`);
}
