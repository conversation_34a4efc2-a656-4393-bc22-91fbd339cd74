import { Platform } from "obsidian";
import { requireDialog, toFileURL } from "./node";
import { getMediaExts, mediaExtensions } from "@/def/media-type";

/**
 * @returns absolute path of the picked file, or null if canceled
 */
export async function pickMediaFile(defaultPath?: string): Promise<URL | null> {
  if (!Platform.isDesktopApp) {
    throw new Error("Not supported in web");
  }
  const result = await requireDialog().showOpenDialog({
    title: "Pick a media file",
    message: "Pick a media file to open",
    buttonLabel: "Pick",
    properties: ["openFile"],
    filters: [{ name: "Media files", extensions: mediaExtensions }],
    defaultPath,
  });
  if (result.canceled) return null;
  const filePath = result.filePaths[0] ?? null;
  if (!filePath) return null;
  return toFileURL(filePath);
}
