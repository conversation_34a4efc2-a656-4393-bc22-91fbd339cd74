import {
  Platform,
  type WorkspaceMobileDrawer,
  type WorkspaceTabs,
  type View,
} from "obsidian";
import type { PlayerComponent } from "@/def/player-comp";
import { playerAtom } from "@mx/shared/hooks/use-player";

export interface LastState {
  currentTime: number;
  playbackRate: number;
  paused: boolean;
}

export function handlePaneMigration<T extends PlayerComponent & View>(
  playerComp: T,
  onMigrated: () => void,
) {
  const store = playerComp.store;
  let playerDisposables: DisposableStack | null = null;

  const lastStateStore = new WeakMap<
    WorkspaceTabs | WorkspaceMobileDrawer,
    LastState
  >();
  let prevParent: WorkspaceTabs | WorkspaceMobileDrawer | undefined;

  using stack = new DisposableStack();
  stack.defer(
    store.sub(playerAtom, () => {
      const player = store.get(playerAtom);
      if (!player) {
        playerDisposables?.dispose();
        playerDisposables = null;
        return;
      }
      using stack = new DisposableStack();
      stack.defer(
        player.subscribe(({ currentTime, paused, playbackRate }) => {
          if (currentTime === 0) return;
          if (!player.el) return;
          lastStateStore.set(playerComp.leaf.parent, {
            currentTime,
            paused,
            playbackRate,
          });
        }),
      );
      stack.defer(
        player.listen("can-play", () => {
          if (!prevParent) return;
          const lastState = lastStateStore.get(prevParent);
          if (!lastState) {
            prevParent = undefined;
            return;
          }
          const { currentTime, paused, playbackRate } = lastState;
          if (!paused) {
            player
              .play(new Event("recover-state"))
              .then(() => {
                player.currentTime = currentTime;
                player.playbackRate = playbackRate;
                if (prevParent) {
                  lastStateStore.delete(prevParent);
                  prevParent = undefined;
                }
              })
              .catch((e) => console.error("recov err play", e));
          } else {
            try {
              player.currentTime = currentTime;
              player.playbackRate = playbackRate;
              if (prevParent) {
                lastStateStore.delete(prevParent);
                prevParent = undefined;
              }
            } catch (e) {
              console.error("recov err paused", e);
            }
          }
        }),
      );
      playerDisposables = stack.move();
    }),
  );
  stack.defer(
    onParentMigrated(playerComp, (_, prev) => {
      onMigrated();
      if (lastStateStore.has(prev)) {
        prevParent = prev;
      } else {
        prevParent = undefined;
      }
    }),
  );
  stack.defer(() => {
    prevParent = undefined;
  });
  stack.defer(() => {
    playerDisposables?.dispose();
    playerDisposables = null;
  });
  const disposables = stack.move();
  playerComp.register(() => disposables.dispose());
}

export function handleWindowMigration<
  T extends PlayerComponent & {
    containerEl: HTMLElement;
  },
>(playerComp: T, onMigrated: () => void) {
  if (Platform.isMobile) return;
  const store = playerComp.store;
  let playerDisposables: DisposableStack | null = null;

  const lastStateStore = new WeakMap<Window, LastState>();
  let prevWin: Window | undefined;

  using stack = new DisposableStack();

  stack.defer(
    store.sub(playerAtom, () => {
      const player = store.get(playerAtom);
      if (!player) {
        playerDisposables?.dispose();
        playerDisposables = null;
        return;
      }
      using stack = new DisposableStack();
      stack.defer(
        player.subscribe(({ currentTime, paused, playbackRate }) => {
          if (currentTime === 0) return;
          if (!player.el) return;
          lastStateStore.set(player.el.win, {
            currentTime,
            paused,
            playbackRate,
          });
        }),
      );
      stack.defer(
        player.listen("can-play", () => {
          if (!prevWin) return;
          const lastState = lastStateStore.get(prevWin);
          if (!lastState) {
            prevWin = undefined;
            return;
          }
          const { currentTime, paused, playbackRate } = lastState;
          if (!paused) {
            player
              .play(new Event("recover-state"))
              .then(() => {
                player.currentTime = currentTime;
                player.playbackRate = playbackRate;
                if (prevWin) {
                  lastStateStore.delete(prevWin);
                  prevWin = undefined;
                }
              })
              .catch((e) => console.error("recov err play", e));
          } else {
            try {
              player.currentTime = currentTime;
              player.playbackRate = playbackRate;
              if (prevWin) {
                lastStateStore.delete(prevWin);
                prevWin = undefined;
              }
            } catch (e) {
              console.error("recov err paused", e);
            }
          }
        }),
      );
      playerDisposables = stack.move();
    }),
  );
  stack.defer(
    onWindowMigrated(playerComp.containerEl, (_, prev) => {
      onMigrated();
      if (lastStateStore.has(prev)) {
        prevWin = prev;
      } else {
        prevWin = undefined;
      }
    }),
  );
  stack.defer(() => {
    prevWin = undefined;
  });
  const disposables = stack.move();
  playerComp.register(() => disposables.dispose());
}

function onWindowMigrated(
  target: HTMLElement,
  handler: (win: Window, prevWin: Window) => void,
) {
  let prevWin = target.win;
  return target.onNodeInserted(() => {
    const currWin = target.win;
    if (currWin === prevWin) return;
    handler(currWin, prevWin);
    prevWin = currWin;
  });
}
function onParentMigrated(
  view: View,
  handler: (
    curr: WorkspaceTabs | WorkspaceMobileDrawer,
    prev: WorkspaceTabs | WorkspaceMobileDrawer,
  ) => void,
) {
  let prevWin = view.containerEl.win;
  let prevTab = view.leaf.parent;
  return view.containerEl.onNodeInserted(() => {
    const currWin = view.containerEl.win;
    const currLeaf = view.leaf.parent;
    if (currWin === prevWin) {
      if (currLeaf !== prevTab) {
        handler(currLeaf, prevTab);
        prevTab = currLeaf;
      }
    } else {
      prevWin = currWin;
    }
  });
}
