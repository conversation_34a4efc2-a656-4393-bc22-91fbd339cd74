export function requireFs() {
  return require("node:fs/promises") as typeof import("node:fs/promises");
}
export function requirePath() {
  return require("node:path") as typeof import("node:path");
}
export function requireUrl() {
  return require("node:url") as typeof import("node:url");
}

export function requireElectronRemote() {
  return require("@electron/remote") as typeof import("@electron/remote");
}

export function requireElectronSafeStorage() {
  return (requireElectronRemote() as any).safeStorage as Electron.SafeStorage;
}

export function requireDialog() {
  const remote = requireElectronRemote();
  return remote.dialog;
}

export function requireCrypto() {
  return require("node:crypto") as typeof import("node:crypto");
}

export function requireElectron() {
  return require("electron") as typeof import("electron");
}

export function toFileURL(path: string) {
  const { pathToFileURL } = requireUrl();
  try {
    const url = pathToFileURL(path) as globalThis.URL;
    return url;
  } catch (e) {
    console.error(`Failed to convert path ${path} to URL: `, e);
    return null;
  }
}
