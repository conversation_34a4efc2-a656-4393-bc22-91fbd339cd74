// check if matching iso-639-1 two-letter language code, with optional -extlang-script-region-variant-extension-privateuse
export const isoLangPattern = /^(?<lang>[a-z]{2})(?:-([A-Za-z]+))*$/;

export function isIso6391(language: string) {
  return isoLangPattern.test(language);
}

export function parseIso6391(language: string | undefined | null): {
  lang: string;
  extlang: string[];
} | null {
  if (!language) return null;
  const match = language.trim().match(isoLangPattern);
  if (!match) return null;
  const [, lang, ...extlang] = match;
  return { lang: lang!, extlang };
}

const countryMap = {
  "zh-Hans": ["CN", "SG", "MY"],
  "zh-Hant": ["TW", "HK", "MO"],
} as Record<string, string[]>;

export function detectChs(extLang: string) {
  if (
    extLang.toLowerCase() === "hans" ||
    countryMap["zh-Hans"]?.includes(extLang.toUpperCase())
  )
    return "zh-Hans";
  if (
    extLang.toLowerCase() === "hant" ||
    countryMap["zh-Hant"]?.includes(extLang.toUpperCase())
  )
    return "zh-Hant";
  return "zh";
}
