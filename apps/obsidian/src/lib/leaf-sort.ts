import type { Media<PERSON>eafSnapshot } from "@/def/view-type";
import { MEDIA_REMOTE_VIEW_TYPE } from "@/media-view/remote-view";

/**
 * Sort by active time, recent first
 */
export function recentlyActiveFirst(
  a: { activeTime: number },
  b: { activeTime: number },
) {
  return b.activeTime - a.activeTime;
}

/**
 * Prioritize non-deferred views
 */
export function nonDeferredFirst(a: MediaLeafSnapshot, b: MediaLeafSnapshot) {
  return a.view === undefined ? 1 : b.view === undefined ? -1 : 0;
}

/**
 * Prioritize remote views
 */
export function remoteMediaViewFirst(
  a: MediaLeafSnapshot,
  b: MediaLeafSnapshot,
) {
  return a.type === MEDIA_REMOTE_VIEW_TYPE
    ? -1
    : b.type === MEDIA_REMOTE_VIEW_TYPE
      ? 1
      : 0;
}

/**
 * Helper method to build composite sort functions by chaining multiple sort criteria.
 * The sort functions are applied in order - if the first returns 0 (equal),
 * the next sort function is used, and so on.
 */
export function by<T>(
  ...sortFns: Array<(a: T, b: T) => number>
): (a: T, b: T) => number {
  return (a: T, b: T) => {
    for (const sortFn of sortFns) {
      const result = sortFn(a, b);
      if (result !== 0) {
        return result;
      }
    }
    return 0;
  };
}
