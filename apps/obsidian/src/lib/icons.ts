import type { HostedVid } from "@/def/media-info";
import { addIcon } from "obsidian";

const icons: Record<HostedVid["host"], string | null> = {
  bilibili: `<path fill-rule="evenodd" clip-rule="evenodd" d="M 20.736 14.88 C 18.513 12.735 18.513 9.173 20.736 7.028 C 22.849 4.99 26.197 4.99 28.311 7.028 L 40.096 18.397 C 40.43 18.72 40.715 19.075 40.949 19.453 L 58.772 19.453 C 59.006 19.075 59.291 18.72 59.625 18.397 L 71.41 7.028 C 73.523 4.99 76.871 4.99 78.984 7.028 C 81.208 9.173 81.208 12.735 78.984 14.88 L 74.244 19.453 L 77.778 19.453 C 90.051 19.453 100 29.402 100 41.675 L 100 72.262 C 100 84.534 90.051 94.484 77.778 94.484 L 22.222 94.484 C 9.949 94.484 0 84.534 0 72.262 L 0 41.675 C 0 29.402 9.949 19.453 22.222 19.453 L 25.477 19.453 L 20.736 14.88 Z M 22.222 30.172 C 16.086 30.172 11.111 35.146 11.111 41.283 L 11.111 72.654 C 11.111 78.79 16.086 83.765 22.222 83.765 L 77.778 83.765 C 83.914 83.765 88.889 78.79 88.889 72.654 L 88.889 41.283 C 88.889 35.146 83.914 30.172 77.778 30.172 L 22.222 30.172 Z M 27.778 51.805 C 27.778 48.737 30.265 46.25 33.333 46.25 C 36.402 46.25 38.889 48.737 38.889 51.805 L 38.889 56.772 C 38.889 59.84 36.402 62.328 33.333 62.328 C 30.265 62.328 27.778 59.84 27.778 56.772 L 27.778 51.805 Z M 66.667 46.25 C 63.598 46.25 61.111 48.737 61.111 51.805 L 61.111 56.772 C 61.111 59.84 63.598 62.328 66.667 62.328 C 69.735 62.328 72.222 59.84 72.222 56.772 L 72.222 51.805 C 72.222 48.737 69.735 46.25 66.667 46.25 Z" fill="currentColor"/>`,
  vimeo: `<path d="M 99.952 26.773 C 99.508 36.508 92.705 49.844 79.555 66.768 C 65.962 84.445 54.458 93.287 45.044 93.287 C 39.218 93.287 34.286 87.905 30.254 77.134 C 27.566 67.263 24.872 57.4 22.185 47.53 C 19.194 36.765 15.986 31.377 12.552 31.377 C 11.806 31.377 9.182 32.952 4.701 36.09 L 0 30.027 C 4.932 25.692 9.799 21.352 14.59 17.005 C 21.175 11.321 26.113 8.324 29.412 8.022 C 37.193 7.276 41.983 12.6 43.783 23.988 C 45.725 36.283 47.069 43.929 47.822 46.919 C 50.066 57.117 52.535 62.21 55.229 62.21 C 57.325 62.21 60.47 58.898 64.663 52.287 C 68.849 45.671 71.093 40.636 71.395 37.183 C 71.993 31.473 69.749 28.612 64.663 28.612 C 62.264 28.612 59.795 29.158 57.261 30.252 C 62.174 14.15 71.562 6.324 85.426 6.768 C 95.701 7.07 100.544 13.739 99.952 26.773" fill="currentColor"/>`,
  coursera: `<path d="M 2.8 50.013 C 2.8 22.278 25.472 0.001 53.82 0.001 C 71.135 -0.12 87.347 8.489 96.943 22.903 L 75.688 35.232 C 70.696 28.099 62.527 23.863 53.82 23.893 C 39.244 23.893 27.298 36.043 27.298 50.013 C 27.298 63.983 39.244 76.133 53.82 76.133 C 62.954 76.192 71.463 71.505 76.294 63.754 L 97.339 76.306 C 87.854 91.149 71.422 100.093 53.808 99.999 C 25.472 100.024 2.8 77.321 2.8 50.013 Z" fill="currentColor" />`,
  youtube: null,
  // https://github.com/log-z/logos/tree/main/website-logos
  "baidu-pan": `<path d="M88.6,46.1c-4.6-4.6-10.8-6.7-16.8-6.3c0.4-6-1.7-12.2-6.3-16.8c-8.5-8.5-22.4-8.5-30.9,0   c-4.6,4.6-6.7,10.8-6.3,16.8c-6-0.4-12.2,1.7-16.8,6.3c-8.5,8.5-8.5,22.4,0,30.9s22.4,8.5,30.9,0l14.3-14.3l1.1-1.1l7.7-7.7   c4.3-4.3,11.2-4.3,15.4,0s4.3,11.2,0,15.4c-4.3,4.3-11.2,4.3-15.4,0c-2.1-2.1-5.6-2.1-7.7,0s-2.1,5.6,0,7.7   c8.5,8.5,22.4,8.5,30.9,0C97.1,68.5,97.1,54.7,88.6,46.1z M19.1,69.3c-4.3-4.3-4.3-11.2,0-15.4c4.3-4.3,11.2-4.3,15.4,0   s4.3,11.2,0,15.4C30.3,73.6,23.4,73.6,19.1,69.3z M42.3,46.1C38,41.9,38,35,42.3,30.7c4.3-4.3,11.2-4.3,15.4,0s4.3,11.2,0,15.4   C53.5,50.4,46.5,50.4,42.3,46.1z"/>`,
};

export function registerIcons() {
  for (const [name, svg] of Object.entries(icons)) {
    if (!svg) continue;
    addIcon(name, svg);
  }
}
