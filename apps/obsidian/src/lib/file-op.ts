import { Platform } from "obsidian";

export function showItemInFolder(fullpath: string) {
  const shell = getElectronShell();
  if (!shell) return;
  shell.showItemInFolder(fullpath);
}
export async function openPath(fullpath: string) {
  const shell = getElectronShell();
  if (!shell) return;
  const err = await shell.openPath(fullpath);
  if (err) throw new Error(err);
}

function getElectronShell() {
  if (!Platform.isDesktopApp) return null;
  const electron = (window as any).electron;
  if (!electron) return;
  return (
    Platform.isMacOS ? electron.remote.shell : electron.shell
  ) as typeof Electron.shell;
}
