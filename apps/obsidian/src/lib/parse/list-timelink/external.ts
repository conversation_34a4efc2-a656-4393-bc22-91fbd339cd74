import type { ListItemWithChildren } from "../chapters/types";
import { isMediaInfoEqual, type MediaInfo } from "@/def/media-info";
import type { LinkParser, LinkParseResult } from "./def";
import { parseExternalLink } from "./utils";

/**
 * Parser that extracts timestamps from markdown links in the format:
 * - [link](#t=00:00:00) point being...
 */
export class ExternalLinkParser implements LinkParser {
  info;
  constructor(info: MediaInfo) {
    this.info = info;
  }

  parseLink(li: ListItemWithChildren, docMd: string): LinkParseResult | null {
    return parseExternalLink(li, docMd, (url) =>
      isMediaInfoEqual(url, this.info),
    );
  }
}
