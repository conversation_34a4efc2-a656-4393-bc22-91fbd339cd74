import { visit, EXIT } from "unist-util-visit";
import { parseHashProps } from "@mx/shared/utils/hash-prop";
import { isTimestamp } from "@mx/shared/time/temporal-frag";
import type { ListItemWithChildren } from "../chapters/types";
import {
  isMediaInfoEqual,
  type FileMediaInfo,
  type MediaInfo,
} from "@/def/media-info";
import type { LinkParser, LinkParseResult } from "./def";
import { parseLinktext, type LinkCache } from "obsidian";
import { extractChildrenText, parseMarkdown } from "../utils/markdown";
import { parseExternalLink } from "./utils";

/**
 * Parser that extracts timestamps from ob internal links in the format:
 * - [[link#t=00:00:00]] point being...
 * And also handles markdown links when no internal links are found:
 * - [00:22](#t=2) point being...
 */
export class InternalLinkParser implements LinkParser {
  info;
  links;
  resolveLink;
  constructor(
    info: MediaInfo,
    links: LinkCache[],
    resolveLink: (path: string) => FileMediaInfo | null,
  ) {
    this.info = info;
    this.links = links;
    this.resolveLink = resolveLink;
  }

  parseLink(li: ListItemWithChildren, docMd: string): LinkParseResult | null {
    // First try to parse as internal Obsidian link
    const result = this.parseInternalLink(li, docMd);
    if (result) return result;

    // If no internal link found, try to parse as markdown link
    // we don't allow any external links with actual urls
    return parseExternalLink(li, docMd, () => false);
  }

  private parseInternalLink(
    li: ListItemWithChildren,
    docMd: string,
  ): LinkParseResult | null {
    const links = this.links.filter(
      (l) =>
        l.position.start.offset >= li.position.start.offset &&
        l.position.end.offset <= li.position.end.offset,
    );
    links.sort((a, b) => a.position.start.offset - b.position.start.offset);
    const firstLink = links.at(0);
    if (!firstLink) return null;
    const { path, subpath } = parseLinktext(firstLink.link);
    const t = parseHashProps(subpath).tempFragment;
    if (!t || !isTimestamp(t)) return null;

    if (path) {
      const info = this.resolveLink(path);
      if (!isMediaInfoEqual(info, this.info)) return null;
    }

    const linkDisplayText = firstLink.displayText;

    const liMd =
      docMd.slice(li.position.start.offset, firstLink.position.start.offset) +
      docMd.slice(firstLink.position.end.offset, li.position.end.offset);
    const tree = parseMarkdown(liMd);

    let output: LinkParseResult | null = null;
    visit(tree, "listItem", (liNode) => {
      const mainText = extractChildrenText(liNode, liMd);
      output = {
        timestamp: t.start,
        title: (mainText || linkDisplayText || "").trim(),
      };
      return EXIT;
    });

    return output;
  }
}
