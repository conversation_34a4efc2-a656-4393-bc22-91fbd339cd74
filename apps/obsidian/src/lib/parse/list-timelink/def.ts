import type { ListItemWithChildren } from "../chapters/types";

export interface LinkParseResult {
  timestamp: number;
  title: string;
}

/**
 * Interface for different link parsing strategies
 * Implementations can handle different formats of links
 * to extract timestamps and titles
 */
export interface LinkParser {
  /**
   * Parse a markdown tree to extract timestamp and title
   *
   * @param tree - The markdown AST
   * @param text - The original markdown text
   * @returns LinkParseResult or null if no valid link was found
   */
  parseLink(li: ListItemWithChildren, docMd: string): LinkParseResult | null;
}
