import { visit, EXIT } from "unist-util-visit";
import { parseHashProps } from "@mx/shared/utils/hash-prop";
import { isTimestamp, type TempFragment } from "@mx/shared/time/temporal-frag";
import type { ListItemWithChildren } from "../chapters/types";
import {
  isMediaInfoEqual,
  type MediaInfo,
  type UrlMediaInfo,
} from "@/def/media-info";
import type { LinkParseResult } from "./def";
import { extractChildrenText, parseMarkdown } from "../utils/markdown";
import { toURL } from "@mx/shared/utils/to-url";
import { parseUrl } from "@/def/url-parse";

/**
 * Extract timestamp and title from a markdown link
 * Handles links like [00:22](#t=2)
 */
export function parseExternalLink(
  li: ListItemWithChildren,
  docMd: string,
  allowLink: (url: UrlMediaInfo) => boolean,
): LinkParseResult | null {
  const liMd = docMd.slice(li.position.start.offset, li.position.end.offset);
  const tree = parseMarkdown(liMd);

  let output: LinkParseResult | null = null;

  visit(tree, "listItem", (liNode) => {
    // the first child must be a link
    const content = liNode.children.at(0);
    if (!content || content.type !== "paragraph") return EXIT;
    const link = content.children.at(0);
    if (!link || link.type !== "link") return EXIT;

    let t: TempFragment | null;

    // allow short form without full url
    if (link.url.startsWith("#")) {
      t = parseHashProps(link.url).tempFragment;
    } else {
      const url = toURL(link.url);
      if (!url) return EXIT;
      const parsedUrl = parseUrl(url);
      if (!parsedUrl?.info || !allowLink(parsedUrl.info)) return EXIT;
      t = parseHashProps(url.hash).tempFragment;
    }
    if (!t || !isTimestamp(t)) return EXIT;

    // skip the link
    const mainText = extractChildrenText(content, liMd, {
      startIdx: 1,
    });
    const linkDisplayText = extractChildrenText(link, liMd);

    output = {
      timestamp: t.start,
      title: (mainText || link.title || linkDisplayText).trim(),
    };

    return EXIT;
  });

  return output;
}
