import type { ListItemCache } from "obsidian";
import type { ListItemWithChildren } from "../chapters/types";

/**
 * Builds a map of list items with their children
 * @param listItems - The list items from Obsidian
 * @returns A map of list items by their root start line
 */
export function buildListItemsMap(
  listItems: ListItemCache[],
): Map<number, ListItemWithChildren[]> {
  const listsMap = new Map<number, ListItemWithChildren[]>();
  const itemsWithChildren: ListItemWithChildren[] = listItems.map((item) => ({
    ...item,
    children: [],
  }));

  // Create an index of items by their line number for O(1) parent lookup
  const itemsByLine = new Map<number, ListItemWithChildren>();
  for (const item of itemsWithChildren) {
    itemsByLine.set(item.position.start.line, item);
  }

  // Group items by their root list or parent
  for (const item of itemsWithChildren) {
    if (item.parent < 0) {
      // Root level item - add to the appropriate list in the map
      const rootStartLine = Math.abs(item.parent);
      if (!listsMap.has(rootStartLine)) {
        listsMap.set(rootStartLine, []);
      }
      listsMap.get(rootStartLine)?.push(item);
    } else {
      // Add as child to parent item
      const parentItem = itemsByLine.get(item.parent);
      if (parentItem) {
        parentItem.children.push(item);
      }
    }
  }

  return listsMap;
}

/**
 * Extracts content from list items and their children
 * @param markdownText - The original markdown text
 * @param rootItem - The root list item
 * @returns An array of extracted content with level and parent info
 */
export function extractListItemsContent(
  markdownText: string,
  rootItem: ListItemWithChildren,
): { content: string; level: number; parent: number }[] {
  const result: { content: string; level: number; parent: number }[] = [];

  function processItem(
    item: ListItemWithChildren,
    level: number,
    parentIndex: number,
  ): void {
    const content = markdownText.slice(
      item.position.start.offset,
      item.position.end.offset,
    );

    const currentIndex = result.length;
    result.push({ content, level, parent: parentIndex });

    for (const child of item.children) {
      processItem(child, level + 1, currentIndex);
    }
  }

  for (const child of rootItem.children) {
    processItem(child, 0, -1);
  }

  return result;
}
