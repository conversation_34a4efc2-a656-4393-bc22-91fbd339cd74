import type { Link, <PERSON>I<PERSON>, Parent, Root } from "mdast";
import remarkGfm from "remark-gfm";
import remarkParse from "remark-parse";
import { unified } from "unified";
import { EXIT } from "unist-util-visit";
import { visit } from "unist-util-visit";

/**
 * Parses markdown text into an AST
 * @param text - The markdown text to parse
 * @returns The parsed markdown AST
 */
export function parseMarkdown(text: string): Root {
  return unified().use(remarkParse).use(remarkGfm).parse(text);
}

export function extractChildrenText(
  node: Parent,
  text: string,
  opts: { startIdx?: number; endIdx?: number } = {},
) {
  const start = node.children.at(opts.startIdx ?? 0)?.position?.start.offset;
  const end = node.children.at(opts.endIdx ?? -1)?.position?.end.offset;
  return start && end ? text.slice(start, end).trim() : "";
}

export function extractFirstMarkdownLink(text: string, tree: Root) {
  let link: Link | undefined;
  visit(tree, "link", (node: Link) => {
    link = node;
    return EXIT;
  });
  if (!link) return null;
  const displayTextStart = link.children.first()?.position?.start.offset;
  const displayTextEnd = link.children.last()?.position?.end.offset;
  const displayText =
    displayTextStart && displayTextEnd
      ? text.slice(displayTextStart, displayTextEnd).trim()
      : "";
  return {
    display: displayText,
    url: link.url,
    title: link.title,
  };
}

/**
 * @returns raw markdown text
 */
export function extractListItemMainText(text: string, tree: Root) {
  let mainText = "";
  visit(tree, "listItem", (node: ListItem) => {
    const start = node.children.first()?.position?.start.offset;
    const end = node.children.last()?.position?.end.offset;
    if (start && end) {
      mainText = text.slice(start, end);
    }
    return EXIT;
  });
  return mainText;
}
