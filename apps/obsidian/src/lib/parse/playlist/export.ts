import type { Vault } from "obsidian";
import { FileSystemAdapter, Notice } from "obsidian";

import type { Playlist } from "./def";
import { getMediaInfoID, isUrlMediaInfo } from "@/def/media-info";
import { requireUrl } from "@/lib/node";

export function generateM3U8File(playlist: Playlist, vault: Vault): Blob {
  // Start of the M3U8 file
  let m3u8Content = "#EXTM3U\n";

  // Iterate over the playlist items and add them to the file content
  const skippedItems: string[] = [];
  let fileNotSupported = false;
  for (const item of playlist.list) {
    if (item.media && isUrlMediaInfo(item.media.info)) {
      // Ensure there's a media URL
      m3u8Content += `#EXTINF:-1,${item.title}\n${item.media.info.url}\n`;
    } else if (item.media?.info.type === "file") {
      if (vault.adapter instanceof FileSystemAdapter) {
        const { pathToFileURL } = requireUrl();
        const fileFullPath = vault.adapter.getFullPath(
          item.media.info.file.path,
        );
        try {
          const fileUrl = pathToFileURL(fileFullPath).href;
          m3u8Content += `#EXTINF:-1,${item.title}\n${fileUrl}\n`;
        } catch (e) {
          new Notice(`Failed to convert file path to URL: ${e}`);
          skippedItems.push(item.title || getMediaInfoID(item.media.info));
        }
      } else {
        fileNotSupported = true;
        skippedItems.push(item.title || getMediaInfoID(item.media.info));
      }
    }
  }
  if (skippedItems.length > 0) {
    if (fileNotSupported) {
      new Notice(
        createFragment((f) => {
          f.createDiv({
            text: "File URI is not supported in this environment. ",
          });
          f.createDiv({ text: `Skipped items: ${skippedItems.join(", ")}` });
        }),
      );
    } else {
      new Notice(`Skipped items: ${skippedItems.join(", ")}`);
    }
  }

  return new Blob([m3u8Content], {
    type: "application/vnd.apple.mpegurl",
  });
}
