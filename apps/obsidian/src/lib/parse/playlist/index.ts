import type MxPlugin from "@/mx-main";
import type { CachedMetadata, TFile } from "obsidian";
import type { Playlist } from "./def";
import parse from "./parse";

export default async function parsePlaylist(
  file: TFile,
  plugin: MxPlugin,
): Promise<Playlist | null> {
  const meta = plugin.app.metadataCache.getFileCache(file);
  if (!meta) return null;
  const list = await parse(file, plugin.app);
  if (!list) return null;
  return {
    // by default autoplay is true
    autoplay: parsePlaylistAutoplay(meta),
    title: getFileTitle(meta, file),
    list,
    file,
  };
}

function parsePlaylistAutoplay(meta: CachedMetadata) {
  if (!meta.frontmatter || !("autoplay" in meta.frontmatter)) return true;
  const value = meta.frontmatter.autoplay;
  return Boolean(value);
}

function getFileTitle(meta: CachedMetadata, file: TFile): string {
  return (
    meta.frontmatter?.title?.trim() ||
    meta.headings?.find((h) => h.level === 1)?.heading?.trim() ||
    file.basename.trim()
  );
}
