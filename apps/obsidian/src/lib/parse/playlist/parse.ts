/* eslint-disable @typescript-eslint/naming-convention */
import { parseLinktext } from "obsidian";
import type {
  Pos,
  LinkCache,
  ListItemCache,
  TFile,
  Vault,
  MetadataCache,
} from "obsidian";
import { isMediaTaskSymbol, taskSymbolMediaTypeMap } from "./def";
import type { MediaTaskSymbol, PlaylistItem } from "./def";
import {
  extractFirstMarkdownLink,
  extractListItemMainText,
  parseMarkdown,
} from "../utils/markdown";
import type { FileMediaInfo, MediaInfoSrc } from "@/def/media-info";
import { parseFile } from "@/def/file-parse";
import { parseHashProps } from "@mx/shared/utils/hash-prop";
import { parseUrl } from "@/def/url-parse";

export default async function parsePlaylist(
  source: TFile,
  ctx: {
    metadataCache: MetadataCache;
    vault: Vault;
  },
): Promise<PlaylistItem[] | null> {
  const { metadataCache, vault } = ctx;
  const meta = metadataCache.getFileCache(source);
  if (!meta) return null;
  const { frontmatter } = meta;
  if (frontmatter?.playlist !== true || !meta.sections || !meta.listItems)
    return null;

  // only consider the first list section
  const listSection = meta.sections.find((s) => s.type === "list");
  if (!listSection) return [];
  const withinListSection = (item: { position: Pos }) =>
    within(item, listSection);
  const listItems = toPlaylistItems(meta.listItems.filter(withinListSection));

  const links = meta.links?.filter(withinListSection) ?? [];
  const fileContent = await vault.cachedRead(source);
  const playlist = listItems.map((listItem, _i, arr): PlaylistItem => {
    const internalLinkIdx = links.findIndex((link) => within(link, listItem));
    const { parent: _parent, task } = listItem;
    const type = task && taskSymbolMediaTypeMap[task];

    // convert start_line-based indexing to array_index-based indexing
    const parent =
      _parent >= 0
        ? arr.findIndex((li) => li.position.start.line === _parent)
        : -1;
    if (internalLinkIdx !== -1) {
      // remove all links within the list item
      const internalLink = links[internalLinkIdx]!;
      const last = links.findLastIndex((link) => within(link, listItem));
      links.splice(internalLinkIdx, last - internalLinkIdx + 1);
      const media = mediaInfoFromInternalLink(internalLink);
      return {
        media,
        type: type ?? "generic",
        parent,
        title: internalLink.displayText ?? "",
      };
    }
    // handle external links
    const text = extractText(fileContent, listItem.position);
    const syntax = parseMarkdown(text);
    const externalLink = extractFirstMarkdownLink(text, syntax);
    if (externalLink) {
      const { url, display } = externalLink;
      const media = parseUrl(url);
      return { media, title: display, type: type || "generic", parent };
    }
    return {
      media: null,
      title: extractListItemMainText(text, syntax) || "Item",
      type: type || "chapter",
      parent,
    };
  });
  return playlist;

  function mediaInfoFromInternalLink({
    link,
  }: LinkCache): MediaInfoSrc<FileMediaInfo> | null {
    const { path, subpath } = parseLinktext(link);
    const file = metadataCache.getFirstLinkpathDest(path, source.path);
    if (!file) return null;
    const info = parseFile(file);
    if (!info) return null;
    return { info, hash: parseHashProps(subpath) };
  }
}

function toPlaylistItems(
  list: ListItemCache[],
): (ListItemCache & { task?: MediaTaskSymbol })[] {
  return list.map(({ task, ...i }) =>
    isMediaTaskSymbol(task) ? { task, ...i } : i,
  );
}

function within(item: { position: Pos }, parent: { position: Pos }) {
  return (
    item.position.start.offset >= parent.position.start.offset &&
    item.position.end.offset <= parent.position.end.offset
  );
}

function extractText(text: string, range: Pos) {
  return text.slice(range.start.offset, range.end.offset);
}
