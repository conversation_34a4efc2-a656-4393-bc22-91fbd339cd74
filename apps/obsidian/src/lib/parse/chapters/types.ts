import type { SectionCache, ListItemCache } from "obsidian";

export interface Chapter {
  title: string;
  startTime: number;
  endTime: number | null; // null for the last chapter
  points: KeyPoint[];
}

export interface ChapterStructure {
  title: string;
  startTime: number;
  endTime: number | null; // null for the last chapter
  pointList: SectionCache | null;
}

export interface KeyPoint {
  timestamp: number;
  title: string;
  description: {
    content: string;
    level: number;
    parent: number;
  }[];
}

export interface ParsedMarkdownData {
  chapters: Chapter[];
}

export interface ListItemWithChildren extends ListItemCache {
  children: ListItemWithChildren[];
}
