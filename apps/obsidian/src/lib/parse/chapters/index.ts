import type {
  App,
  CachedMetadata,
  MetadataCache,
  Pos,
  TFile,
  Vault,
} from "obsidian";
import { parseTimestampedBlockId } from "../utils/block";
import { buildListItemsMap, extractListItemsContent } from "../utils/list";
import type {
  Chapter,
  ChapterStructure,
  KeyPoint,
  ParsedMarkdownData,
} from "./types";
import { ExternalLinkParser } from "../list-timelink/external";
import { getMediaMeta } from "@/media-lib/extract";
import { InternalLinkParser } from "../list-timelink/internal";
import { parseFile } from "@/def/file-parse";

/**
 * Parses markdown content and document structure to extract chapters with time ranges
 * and their key points with timestamps, titles, and descriptions.
 *
 * @param markdown - The raw markdown text content
 * @param doc - JSON with document structure from Obsidian
 * @param linkParser - Optional custom link parser (defaults to TimestampLinkParser)
 * @returns Structured data with chapters and their key points
 */
export default async function parseChapters(
  file: TFile,
  ctx: {
    vault: Vault;
    metadataCache: MetadataCache;
  },
): Promise<ParsedMarkdownData> {
  const docMeta = ctx.metadataCache.getFileCache(file);
  const media = getMediaMeta(file, ctx)?.src;
  if (!media) return { chapters: [] };
  if (
    !docMeta ||
    !media ||
    !docMeta.headings ||
    !docMeta.sections ||
    !docMeta.blocks
  )
    return { chapters: [] };

  const docMd = await ctx.vault.cachedRead(file);
  const linkParser =
    media.type === "file"
      ? new InternalLinkParser(media, docMeta.links ?? [], (path) => {
          const mediaFile = ctx.metadataCache.getFirstLinkpathDest(
            path,
            file.path,
          );
          return parseFile(mediaFile);
        })
      : new ExternalLinkParser(media);

  const validHeadings = extractValidHeadings(docMeta);
  const chapterStructure = createChapterStructure(validHeadings, docMeta);
  const listItemsMap = buildListItemsMap(docMeta.listItems ?? []);

  const chapters = chapterStructure.map<Chapter>(
    ({ pointList, ...chapter }) => {
      if (!pointList) return { ...chapter, points: [] };
      const listItems = listItemsMap.get(pointList.position.start.line);
      if (!listItems) return { ...chapter, points: [] };

      const points = listItems
        ?.map<KeyPoint | null>((li) => {
          const point = linkParser.parseLink(li, docMd);
          if (!point) return null;
          return {
            ...point,
            description: extractListItemsContent(docMd, li),
          };
        })
        .filter((v) => v !== null);

      points.sort((a, b) => a.timestamp - b.timestamp);
      return { ...chapter, points };
    },
  );
  return { chapters };
}

interface ValidHeading {
  id: string;
  idx: number;
  timestamp: number;
  position: Pos;
  heading: string;
}

/**
 * Extracts valid headings from the document
 */
function extractValidHeadings(doc: CachedMetadata): ValidHeading[] {
  if (!doc.sections) return [];
  const validHeadings = doc.sections
    .map((section, idx) => {
      if (section.type !== "heading" || !section.id) return null;
      const timestamp = parseTimestampedBlockId(section.id);
      if (!timestamp) return null;
      const h = doc.headings?.find(
        (heading) =>
          heading.position.start.offset === section.position.start.offset &&
          heading.position.end.offset === section.position.end.offset,
      );
      if (!h || h.level !== 2) return null;
      return {
        id: section.id,
        idx, // index of the section in the document
        timestamp,
        position: section.position,
        heading: h.heading
          .replace(new RegExp(`${RegExp.escape(`^${section.id}`)}$`), "")
          .trim(),
      };
    })
    .filter((t) => t !== null);

  validHeadings.sort((a, b) => a.timestamp - b.timestamp);
  return validHeadings;
}

/**
 * Creates chapter structure from valid headings
 */
function createChapterStructure(
  validHeadings: ValidHeading[],
  doc: CachedMetadata,
): ChapterStructure[] {
  const sections = doc.sections;
  const doneSections = new WeakSet<any>();
  if (!sections) return [];

  // Create chapters with time ranges
  return validHeadings.map<ChapterStructure>((currHeading, idx, headings) => {
    const nextHeading = headings[idx + 1] || null;
    const chapter: ChapterStructure = {
      title: currHeading.heading,
      startTime: currHeading.timestamp,
      endTime: nextHeading ? nextHeading.timestamp : null,
      pointList: null,
    };

    if (!doc.listItems || doc.listItems.length === 0) return chapter;

    for (let si = currHeading.idx + 1; si < sections.length; si++) {
      const section = sections[si]!;
      if (doneSections.has(section)) continue;
      // find first list, break if found heading
      if (section.type === "heading") return chapter;
      if (section.type === "list") {
        doneSections.add(section);
        return { ...chapter, pointList: section };
      }
    }
    return chapter;
  });
}
