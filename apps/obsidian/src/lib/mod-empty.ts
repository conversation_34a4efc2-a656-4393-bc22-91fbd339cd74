import { createEventEmitter } from "@mx/shared/utils/event";
import { around } from "monkey-around";
import type { App, View } from "obsidian";
import { WorkspaceLeaf } from "obsidian";

async function getEmptyViewCtor(app: App) {
  const leaf = new (WorkspaceLeaf as any)(app) as WorkspaceLeaf;
  await leaf.setViewState({ type: "empty" });
  return leaf.view.constructor as typeof View;
}

const patchedSymbol = Symbol("mx-empty-patched");

interface EmptyView {
  actionListEl: HTMLElement;
  onOpen(): Promise<void>;
  onClose(): Promise<void>;
  [patchedSymbol]: boolean;
}

export async function addEmptyViewActions(
  app: App,
  actions: { callback: (evt: MouseEvent) => void; title: string }[],
): Promise<Disposable> {
  using stack = new DisposableStack();
  const EmptyView = await getEmptyViewCtor(app);
  if (!EmptyView) {
    console.error("Failed to get EmptyView");
    return stack.move();
  }
  const reversed = [...actions].reverse();

  const ac = new AbortController();

  const inject = (view: EmptyView) => {
    const inserted = reversed.map((cmd) =>
      createDiv(
        { cls: "empty-state-action tappable", text: cmd.title },
        (el) => {
          el.addEventListener("click", cmd.callback);
        },
      ),
    );
    for (const el of inserted) {
      view.actionListEl.lastElementChild?.insertAdjacentElement(
        "beforebegin",
        el,
      );
    }
    ac.signal.addEventListener("abort", () => {
      for (const el of inserted) {
        el.remove();
      }
      view[patchedSymbol] = false;
    });
    view[patchedSymbol] = true;
  };

  for (const leaf of app.workspace.getLeavesOfType("empty")) {
    inject(leaf.view as unknown as EmptyView);
  }
  stack.defer(
    around(EmptyView.prototype as unknown as EmptyView, {
      onOpen(next) {
        return async function (this: EmptyView) {
          const returns = await next.apply(this);
          inject(this);
          return returns;
        };
      },
    }),
  );
  stack.defer(() => ac.abort());
  const disposables = stack.move();
  return disposables;
}
