import type { MediaInfo } from "@/def/media-info";
import type { TranscriptLoader } from "./loader";
import { TextTrack } from "@vidstack/react";
import type { CaptionsFileFormat } from "media-captions";
import type MxSettings from "@/settings/registry";
import { assertNever } from "@std/assert/unstable-never";
import { getTrackInfoID, type TextTrackInfo } from "@/def/track-info";
import { detectChs, parseIso6391 } from "@/lib/lang-iso";

export async function loadTracks(
  media: MediaInfo,
  ctx: { loader: TranscriptLoader; settings: MxSettings },
): Promise<{ tracks: TextTrack[]; trackInfo: TextTrackInfo[] }> {
  const [settings, tracks] = await Promise.all([
    ctx.settings.loaded,
    ctx.loader.getTracks(media),
  ]);

  tracks.sort((a, b) => compareTrack(a.meta, b.meta));
  const defaultTrackIdx = (() => {
    if (!settings["playback.track.default-enabled"]) return -1;
    const specified = tracks.findIndex((t) => t.meta.isDefault);
    if (specified !== -1) return specified;
    const defaultLanguages = settings["playback.track.default-languages"];
    const groupedByLang = Map.groupBy(
      tracks.map((t, i) => ({ data: t, idx: i })),
      (t) => parseIso6391(t.data.meta.language)?.lang,
    );
    for (const defaultLang of defaultLanguages) {
      const directMatch = tracks.findIndex(
        (t) => t.meta.language?.trim() === defaultLang,
      );
      if (directMatch !== -1) return directMatch;

      const defaultLangIso = parseIso6391(defaultLang);
      // special handling for Chinese dialects
      if (defaultLangIso?.lang === "zh" && defaultLangIso.extlang[0]) {
        const chs = detectChs(defaultLangIso.extlang[0]);
        const chsMatch = tracks.findIndex((t) => {
          const currLangIso = parseIso6391(t.meta.language);
          return (
            currLangIso &&
            currLangIso.lang === "zh" &&
            currLangIso.extlang[0] &&
            detectChs(currLangIso.extlang[0]) === chs
          );
        });
        if (chsMatch !== -1) return chsMatch;
      }

      if (defaultLangIso && groupedByLang.has(defaultLangIso.lang)) {
        return groupedByLang.get(defaultLangIso.lang)!.at(0)!.idx;
      }
    }
    return 0;
  })();

  return {
    tracks: tracks.map((track, i) => {
      const { meta, src, type } = track;
      return new TextTrack({
        kind: meta.kind,
        type: meta.format,
        src:
          type === "internal.resolved"
            ? `<VAULT_FILE:${src.path}>`
            : type === "file"
              ? `<LOCAL_FILE:${src.path}>`
              : `<REMOTE:${src}>`,
        default: defaultTrackIdx === i,
        label: formatLabel(meta, i),
        language: meta.language?.trim(),
        id: getTrackInfoID(track),
        fetch: async () =>
          createResponse(meta.format, () => ctx.loader.loadTrack(track)),
      });
    }),
    trackInfo: tracks,
  };
}

function createResponse(
  format: CaptionsFileFormat,
  getContent: () => Promise<string>,
) {
  return new Response(
    new ReadableStream<string>({
      async start(controller) {
        try {
          const text = await getContent();
          controller.enqueue(text);
          controller.close();
        } catch (error) {
          controller.error(error);
        }
      },
    }).pipeThrough(new TextEncoderStream()),
    {
      headers: {
        "Content-Type": captionMimeMap[format],
      },
    },
  );
}

export function formatLabel(
  track: {
    label: string | null | undefined;
    language: string | null | undefined;
    kind: "subtitles" | "captions";
  },
  idx: number,
) {
  if (track.label) return track.label;
  if (track.language) {
    if (track.kind === "subtitles") {
      return track.language;
    }
    if (track.kind === "captions") {
      return `${track.language} (CC)`;
    }
    assertNever(track.kind);
  }
  return `${track.kind} ${idx + 1}`;
}

const captionMimeMap = {
  vtt: "text/vtt",
  srt: "application/x-subrip",
  ssa: "application/x-ssa",
  ass: "application/x-ass",
} satisfies Record<CaptionsFileFormat, string>;

interface SortableTrack {
  language: string | null | undefined;
  label: string | null | undefined;
}

const compareTrack = createChainableComparer<SortableTrack>()
  // First handle null objects (they'll go to the end)
  // Then compare by language (null languages go after non-null languages)
  .compareBy((item) => item.language, { nullsFirst: false })
  // Finally compare by label
  .compareBy((item) => item.label)
  .build();

/**
 * Creates a chainable comparison function for sorting objects with potentially nullable string fields
 */
function createChainableComparer<T>() {
  type Comparer = (a: T | null, b: T | null) => number;
  const comparers: Comparer[] = [];

  const chain = {
    /**
     * Compare by a property that might be null/undefined
     * @param getter Function to extract the comparable string value
     * @param options Optional comparison options
     */
    compareBy: (
      getter: (item: T) => string | null | undefined,
      options: { nullsFirst?: boolean; ignoreCase?: boolean } = {},
    ) => {
      comparers.push((a, b) => {
        // Handle null objects first
        if (a === null && b === null) return 0;
        if (a === null) return 1; // Null objects go to the end
        if (b === null) return -1;

        const aValue = getter(a);
        const bValue = getter(b);

        // If both values are null/undefined, move to next comparer
        if (!aValue && !bValue) return 0;

        // Handle the case where only one value is null/undefined
        if (aValue && !bValue) return options.nullsFirst ? 1 : -1;
        if (!aValue && bValue) return options.nullsFirst ? -1 : 1;

        // Both values exist, do locale compare
        return aValue!.localeCompare(
          bValue!,
          undefined,
          options.ignoreCase ? { sensitivity: "base" } : undefined,
        );
      });

      return chain;
    },

    /**
     * Build the final comparison function
     */
    build: () => {
      return (a: T | null, b: T | null): number => {
        for (const compare of comparers) {
          const result = compare(a, b);
          if (result !== 0) return result;
        }
        return 0;
      };
    },
  };

  return chain;
}
