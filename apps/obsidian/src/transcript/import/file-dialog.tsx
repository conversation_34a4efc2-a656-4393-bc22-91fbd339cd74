import {
  TrackFileImportForm,
  type TrackFileForm,
  HTMLInputFilePicker,
  TrackFilePickerDisplay,
} from "@mx/ui";
import { type App, Modal, Platform, moment } from "obsidian";
import ReactDOM from "react-dom/client";
import { ShadowRoot } from "@/components/shadow-dom";

import "./dialog.css";
import { ElectronFilePicker } from "./file-picker";
import { inferTrackInfoFromPath } from "../resolve/infer-info";

export class TextTrackImportDialog extends Modal {
  #disposables: Disposable | null = null;
  #resolvers;
  private constructor(app: App) {
    super(app);
    this.modalEl.addClass("mx", "track-import-dialog", "track-file-import");
    this.titleEl.setText("Import Text Track File");
    this.#resolvers = Promise.withResolvers<TrackFileForm | null>();
    this.#resolvers.promise.finally(() => {
      this.close();
    });
  }
  onOpen(): void {
    super.onOpen();
    using stack = new DisposableStack();
    const root = ReactDOM.createRoot(this.contentEl);
    stack.defer(() => {
      root.unmount();
    });
    stack.defer(() => {
      this.#resolvers.resolve(null);
    });
    root.render(
      <ShadowRoot>
        <TrackFileImportForm
          onSubmit={(data) => {
            this.#resolvers.resolve(data);
          }}
          inferDefaults={(file) => {
            const meta = inferTrackInfoFromPath({ name: file.name });
            if (!meta) return {};
            return {
              label: meta.basename,
              language: meta.language || undefined,
              "track-kind":
                meta.language && !meta.language.startsWith(moment.locale())
                  ? "subtitles" // choose subtitles if subtitle language is not the same as the current locale
                  : "captions",
            };
          }}
          renderFilePicker={({ onChange, value, disabled }) =>
            !Platform.isDesktopApp ? (
              <HTMLInputFilePicker
                className="contents"
                disabled={disabled}
                onChange={onChange}
                onCancel={() => onChange(null)}
              >
                <TrackFilePickerDisplay value={value} />
              </HTMLInputFilePicker>
            ) : (
              <ElectronFilePicker
                className="contents"
                disabled={disabled}
                onChange={onChange}
                onCancel={() => onChange(null)}
              >
                <TrackFilePickerDisplay value={value} />
              </ElectronFilePicker>
            )
          }
        />
      </ShadowRoot>,
    );
    this.#disposables = stack.move();
  }

  onClose(): void {
    this.#disposables?.[Symbol.dispose]();
    super.onClose();
  }

  static async open(app: App) {
    const modal = new TextTrackImportDialog(app);
    modal.open();
    return await modal.#resolvers.promise;
  }
}
