import { TrackUrlImportForm, type TrackUrlForm } from "@mx/ui";
import { type App, Modal } from "obsidian";
import ReactDOM from "react-dom/client";
import { ShadowRoot } from "@/components/shadow-dom";

import "./dialog.css";

export class TextTrackUrlImportDialog extends Modal {
  #disposables: Disposable | null = null;
  #resolvers;
  private constructor(app: App) {
    super(app);
    this.modalEl.addClass("mx", "track-import-dialog", "track-url-import");
    this.titleEl.setText("Import Text Track from URL");
    this.#resolvers = Promise.withResolvers<TrackUrlForm | null>();
    this.#resolvers.promise.finally(() => {
      this.close();
    });
  }
  onOpen(): void {
    super.onOpen();
    using stack = new DisposableStack();
    const root = ReactDOM.createRoot(this.contentEl);
    stack.defer(() => {
      root.unmount();
    });
    stack.defer(() => {
      this.#resolvers.resolve(null);
    });
    root.render(
      <ShadowRoot>
        <TrackUrlImportForm
          onSubmit={(data) => {
            this.#resolvers.resolve(data);
          }}
        />
      </ShadowRoot>,
    );
    this.#disposables = stack.move();
  }

  onClose(): void {
    this.#disposables?.[Symbol.dispose]();
    super.onClose();
  }

  static async open(app: App): Promise<TrackUrlForm | null> {
    const modal = new TextTrackUrlImportDialog(app);
    modal.open();
    return await modal.#resolvers.promise;
  }
}
