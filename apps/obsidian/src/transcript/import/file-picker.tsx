import { trackExtensions } from "@/def/media-type";
import { requireElectronRemote, requirePath } from "@/lib/node";
import type { FilePath } from "@mx/ui";
import { Platform } from "obsidian";

/**
 * Electron file picker supports reading symlink's file name without resolving the symlink.
 * This is useful for showing the correct file name in the file picker.
 */
export const ElectronFilePicker = ({
  onChange,
  onCancel,
  ...props
}: {
  onChange: (file: FilePath) => void;
  onCancel?: () => void;
} & Omit<React.ComponentProps<"button">, "onChange" | "type" | "onClick">) => {
  if (!Platform.isDesktopApp) throw new Error("Not supported on this platform");
  return (
    <button
      {...props}
      type="button"
      onClick={async () => {
        const { dialog } = requireElectronRemote();
        const { basename } = requirePath();
        try {
          const resp = await dialog.showOpenDialog({
            properties: ["openFile", "noResolveAliases"],
            message: "Select a subtitle file to import",
            filters: [
              {
                name: "Subtitle files",
                extensions: trackExtensions,
              },
            ],
          });
          const selected = resp.filePaths[0];
          if (resp.canceled || !selected) {
            onCancel?.();
          } else {
            onChange({ path: selected, name: basename(selected) });
          }
        } catch (e) {
          console.error("Failed to open electron file picker", e);
          onCancel?.();
        }
      }}
      {...props}
    />
  );
};
