import { loadedMediaTracksAtom, mediaTracksAtom } from "@/def/atom/track";
import { player<PERSON>tom } from "@mx/shared/hooks/use-player";
import { abortable } from "@std/async";
import { atomEffect } from "jotai-effect";

export const playerTrackLoader = atomEffect((get, set) => {
  const player = get(playerAtom);
  if (!player) return;
  const tracksPromise = get(mediaTracksAtom);
  const ac = new AbortController();
  abortable(tracksPromise, ac.signal).then((tracks) => {
    for (const t of tracks?.tracks ?? []) {
      player.textTracks.add(t);
    }
    set(loadedMediaTracksAtom, tracks);
  });
  return () => {
    ac.abort();
    set(loadedMediaTracksAtom, null);
    player.textTracks.clear();
    // @ts-expect-error may report to vidstack/react as a bug?
    player.textTracks._defaults = {};
  };
});
