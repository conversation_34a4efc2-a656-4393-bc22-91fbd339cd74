// handle url/file/website -> transcript; transcript -> url/file/website
// handle transcript resolution, fetching, and caching

import type { FileInfo } from "@/def/file-info";
import type { MediaInfo } from "@/def/media-info";
import { isTrackInfoEqual, type TextTrackInfo } from "@/def/track-info";
import type MxPlugin from "@/mx-main";
import type { ParsedCaptionsResult } from "media-captions";
import type { App } from "obsidian";
import { TFile, requestUrl, Platform } from "obsidian";
import { parseTrack } from "./parse";
import { requireFs } from "../lib/node";
import { resolveLocalFileTracks } from "./resolve/track.local";
import { resolveVaultTracks } from "./resolve/track.vault";
import { resolveVaultMediaForTrack } from "./resolve/media.vault";
import { resolveLocalMediaForTrack } from "./resolve/media.local";
import { isTrackFile } from "@/def/media-type";

export class TranscriptLoader {
  constructor(public plugin: MxPlugin) {
    this.app = plugin.app;
  }
  app: App;

  // #region caption file loading
  async loadAndParseTrackFile(
    file: FileInfo,
  ): Promise<ParsedCaptionsResult | null> {
    if (!isTrackFile(file)) return null;
    const content = await this.#readFile(file);
    return parseTrack(content, { type: file.extension });
  }

  async #readFile(file: FileInfo) {
    if (file instanceof TFile) {
      return await this.app.vault.cachedRead(file);
    }
    if (!Platform.isDesktopApp) {
      throw new Error("Cannot read file system on mobile");
    }
    const { readFile } = requireFs();
    return await readFile(file.path, "utf-8");
  }
  async #loadFromURL(url: string | URL) {
    const resp = await requestUrl({ url: url.toString(), method: "GET" });
    return resp.text;
  }

  async loadAndParseTrack(track: TextTrackInfo): Promise<ParsedCaptionsResult> {
    const content: string =
      track.src instanceof URL
        ? await this.#loadFromURL(track.src)
        : await this.#readFile(track.src);
    return await parseTrack(content, { type: track.meta.format });
  }
  async loadTrack(track: TextTrackInfo): Promise<string> {
    const content: string =
      track.src instanceof URL
        ? await this.#loadFromURL(track.src)
        : await this.#readFile(track.src);
    return content;
  }
  // #endregion

  async getLocalTracks(media: MediaInfo): Promise<TextTrackInfo[]> {
    if (media.type === "url:direct")
      return await resolveLocalFileTracks(media).catch((e) => {
        console.error("Failed to get tracks from file system for", media, e);
        return [];
      });
    if (media.type === "file") return resolveVaultTracks(media);
    return [];
  }

  async getTracks(media: MediaInfo): Promise<TextTrackInfo[]> {
    const linkedTracks = this.plugin.mediaLib.findTracksByMedia(media);
    const localTracks = await this.getLocalTracks(media);
    // console.debug("track for media", { media, localTracks, linkedTracks });
    return [
      ...localTracks.filter(
        (local) =>
          // prefer explicitly defined in the media lib over auto detected
          !linkedTracks.some((linked) => isTrackInfoEqual(local, linked)),
      ),
      ...linkedTracks,
    ];
  }

  async getLinkedMedia(track: TextTrackInfo): Promise<MediaInfo[]> {
    const linkedMedia = this.plugin.mediaLib
      .findMediaByTrack(track)
      .map((t) => t.src);
    if (track.type === "internal.resolved") {
      const vaultMedia = resolveVaultMediaForTrack(track);
      if (vaultMedia) linkedMedia.push(vaultMedia);
    } else if (track.type === "file") {
      const fileMedia = await resolveLocalMediaForTrack(track).catch((e) => {
        console.error("Failed to find related media from fs for", track, e);
        return null;
      });
      if (fileMedia) linkedMedia.push(fileMedia);
    }
    return linkedMedia;
  }
}
