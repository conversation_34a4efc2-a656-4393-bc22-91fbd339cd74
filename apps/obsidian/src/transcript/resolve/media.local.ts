import type { DirectUrlInfo } from "@/def/media-info";
import { isMediaFile, mediaExtensions } from "@/def/media-type";
import type { LocalFileTrack } from "@/def/track-info";
import { Platform } from "obsidian";
import { requirePath, requireUrl } from "../../lib/node";
import { iterSiblings } from "./utils/iter";
import type { FileInfo } from "@/def/file-info";

export async function resolveLocalMediaForTrack(
  track: LocalFileTrack,
): Promise<DirectUrlInfo | null> {
  if (!Platform.isDesktopApp) {
    return null;
  }

  const path = requirePath();

  const mediaFiles: FileInfo[] = [];
  for await (const file of iterSiblings(path.dirname(track.src.path), [
    track.src.basename,
  ])) {
    if (file.basename !== track.basename || !isMediaFile(file)) {
      continue;
    }
    mediaFiles.push(file);
  }

  const { pathToFileURL } = requireUrl();

  for (const ext of mediaExtensions) {
    const media = mediaFiles.find((f) => f.extension === ext);
    if (!media) continue;
    return {
      type: "url:direct",
      url: pathToFileURL(media.path),
    };
  }
  return null;
}
