import type { FileMediaInfo } from "@/def/media-info";
import { isMediaFile, mediaExtensions } from "@/def/media-type";
import type { VaultFileTrack } from "@/def/track-info";
import { TFile } from "obsidian";

export function resolveVaultMediaForTrack(
  track: VaultFileTrack,
): FileMediaInfo | null {
  const parentDir = track.src.parent;
  if (!parentDir) return null;

  const mediaFiles = parentDir.children
    .map((f) =>
      f instanceof TFile &&
      f.name !== track.src.name &&
      f.basename === track.basename &&
      isMediaFile(f)
        ? f
        : null,
    )
    .filter((f) => !!f);

  for (const ext of mediaExtensions) {
    const media = mediaFiles.find((f) => f.extension === ext);
    if (!media) continue;
    return {
      type: "file",
      file: media,
    };
  }
  return null;
}
