import { fileNameFrom, type FileLike, type FileName } from "@/def/file-info";
import { isTrackFile } from "@/def/media-type";
import { isIso6391 } from "@/lib/lang-iso";
import type { CaptionsFileFormat } from "media-captions";

function parseLanguage(file: FileName): {
  basename: string;
  language: string;
} | null {
  const segments = file.basename.split(".");
  if (segments.length <= 1) {
    return null;
  }
  const language = segments.pop()!;
  if (!isIso6391(language)) {
    return null;
  }

  const newBasename = segments.join(".");
  return {
    basename: newBasename,
    language,
  };
}

export function inferTrackInfoFromPath(
  input: FileLike,
  { sep = "/" }: { sep?: string } = {},
): {
  basename: string;
  language: string | null;
  format: CaptionsFileFormat;
} | null {
  const file = fileNameFrom(input, sep);
  if (!file || !isTrackFile(file)) {
    return null;
  }
  const lang = parseLanguage(file);
  return {
    basename: lang?.basename ?? file.basename,
    language: lang?.language ?? null,
    format: file.extension,
  };
}
