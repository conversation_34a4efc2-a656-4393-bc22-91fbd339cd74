import type { FileMediaInfo } from "@/def/media-info";
import { fileToTrack, type VaultFileTrack } from "@/def/track-info";
import { TFile } from "obsidian";
import { dedupeTracks } from "./utils/dedupe";

export function resolveVaultTracks({ file }: FileMediaInfo): VaultFileTrack[] {
  if (!file.parent) return [];

  const tracks = file.parent.children
    .map((f) => {
      if (!(f instanceof TFile)) return null;
      const trackinfo = fileToTrack(f, { kind: "subtitles" });
      if (!trackinfo || trackinfo.basename !== file.basename) return null;
      return trackinfo;
    })
    .filter((t) => !!t);

  return dedupeTracks(tracks);
}
