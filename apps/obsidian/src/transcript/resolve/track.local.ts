import { toFileInfo } from "@/def/file-info";
import type { DirectUrlInfo, UrlMediaInfo } from "@/def/media-info";
import type { LocalFileTrack } from "@/def/track-info";
import { Platform } from "obsidian";
import { inferTrackInfoFromPath } from "./infer-info";
import { dedupeTracks } from "./utils/dedupe";
import { requirePath, requireUrl } from "../../lib/node";
import { iterSiblings } from "./utils/iter";

export async function resolveLocalFileTracks(
  media: DirectUrlInfo,
): Promise<LocalFileTrack[]> {
  if (!Platform.isDesktopApp || media.url.protocol !== "file:") {
    return [];
  }
  const path = requirePath();
  const { fileURLToPath } = requireUrl();

  const filepath = fileURLToPath(media.url);
  const mediaFile = toFileInfo(filepath, path.sep);

  const tracks: LocalFileTrack[] = [];
  for await (const file of iterSiblings(mediaFile.parent, [mediaFile.name])) {
    const trackinfo = inferTrackInfoFromPath(file, { sep: path.sep });
    if (!trackinfo || trackinfo.basename !== mediaFile.basename) continue;
    tracks.push({
      type: "file",
      src: file,
      basename: file.basename,
      meta: {
        format: trackinfo.format,
        kind: "subtitles",
        language: trackinfo.language,
        label: null,
        id: null,
        isDefault: false,
        wid: null,
      },
    });
  }

  return dedupeTracks(tracks);
}
