import type { LocalFileTrack, VaultFileTrack } from "@/def/track-info";
import type { CaptionsFileFormat } from "media-captions";

const preferredFormats = [
  "vtt",
  "srt",
  "ssa",
  "ass",
] satisfies CaptionsFileFormat[];

/**
 * Deduplicates caption tracks by selecting one track per language based on preferred format priority.
 * When multiple tracks exist for the same language, it keeps only the track with the highest priority format.
 *
 * @param {LocalFileTrack[]} tracks - Array of caption tracks to deduplicate
 * @returns {LocalFileTrack[]} - Deduplicated tracks, one per language with preferred format
 */
export function dedupeTracks<T extends LocalFileTrack | VaultFileTrack>(
  tracks: T[],
): T[] {
  const langGrouped = Map.groupBy(tracks, (track) => track.meta.language);

  return [...langGrouped.values()]
    .map((tracksInLang) => {
      for (const format of preferredFormats) {
        const track = tracksInLang.find((t) => t.meta.format === format);
        if (track) return track;
      }
      return tracksInLang[0]; // Fallback to first track if no preferred format found
    })
    .filter((v) => v !== undefined);
}
