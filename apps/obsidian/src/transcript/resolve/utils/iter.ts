import { type FileInfo, toFileInfo } from "@/def/file-info";
import { requireFs, requirePath } from "../../../lib/node";

/**
 * @param exclude names of files under the directory to exclude
 */
export async function* iterSiblings(
  dirPath: string,
  exclude: string[] = [],
): AsyncGenerator<FileInfo> {
  const { opendir } = requireFs();
  const path = requirePath();

  const excludeSet = new Set(exclude);
  const dir = await opendir(dirPath, { encoding: "utf-8" });

  for await (const f of dir) {
    if (!(f.isFile() || f.isSymbolicLink()) || excludeSet.has(f.name)) continue;
    yield toFileInfo(path.join(dirPath, f.name), path.sep);
  }
}
