import {
  type FileManager,
  Platform,
  type TFile,
  type CachedMetadata,
  type FrontmatterLinkCache,
  type MetadataCache,
  parseFrontMatterStringArray,
} from "obsidian";
import { toURL } from "@mx/shared/utils/to-url";

import {
  CAPTION_FIELD,
  SUBTITLE_FIELD,
  linkToTrackLink,
  urlToTrackLink,
  type MetaTextTrackInfo,
  type TextTrackInfo,
  type TrackMeta,
} from "@/def/track-info";
import { requireUrl } from "@/lib/node";
import { assertNever } from "@std/assert/unstable-never";

const supportedProtocols = new Set(["file:", "http:", "https:"]);

export function parseTextTrackFields(
  meta: CachedMetadata,
): MetaTextTrackInfo[] {
  const fm = meta.frontmatter ?? {};
  const subtitleUrls = urlFromField(SUBTITLE_FIELD, fm);
  const captionUrls = urlFromField(CAPTION_FIELD, fm);

  const fmLinks = Map.groupBy(meta.frontmatterLinks ?? [], (f) =>
    f.key.split(".").at(0),
  );
  const subtitleLinks = linkFromField(SUBTITLE_FIELD, fmLinks);
  const captionLinks = linkFromField(CAPTION_FIELD, fmLinks);

  return [
    ...captionLinks.map((l) => linkToTrackLink(l, "captions")),
    ...captionUrls.map((u) => urlToTrackLink(u, { kind: "captions" })),
    ...subtitleLinks.map((l) => linkToTrackLink(l, "subtitles")),
    ...subtitleUrls.map((u) => urlToTrackLink(u, { kind: "subtitles" })),
  ].filter((v) => v !== null);
}

function linkFromField(
  field: string,
  fmLinks: Map<string | undefined, FrontmatterLinkCache[]>,
): FrontmatterLinkCache[] {
  if (!fmLinks.has(field)) return [];
  return fmLinks.get(field)!;
}

function urlFromField(field: string, fm: Record<string, unknown>): URL[] {
  return (parseFrontMatterStringArray(fm, field, true) ?? [])
    .map((s) => toURL(s))
    .filter((v): v is URL => v !== null && supportedProtocols.has(v.protocol));
}

/**
 * Encodes a single TextTrackInfo to a string representation
 */
export function encodeTextTrackInfo(
  track: TextTrackInfo,
  ctx: { sourcePath: string; metadataCache: MetadataCache },
): string {
  switch (track.type) {
    case "url":
      return track.src.toString() + encodeTrackMeta(track.meta);
    case "file": {
      if (!Platform.isDesktopApp)
        throw new Error("File tracks are only supported on desktop");
      const { pathToFileURL } = requireUrl();
      const fileUrl = pathToFileURL(track.src.path);
      return (
        fileUrl.toString() +
        encodeTrackMeta(track.meta, { includeFormat: false })
      );
    }
    case "internal.resolved": {
      const linktext = ctx.metadataCache.fileToLinktext(
        track.src,
        ctx.sourcePath,
      );
      const metaHash = encodeTrackMeta(track.meta, { includeFormat: false });
      return `[[${linktext}${metaHash}]]`;
    }
    default:
      assertNever(track);
  }
}

/**
 * Encodes TrackMeta to URL hash/query parameters
 */
export function encodeTrackMeta(
  meta: TrackMeta,
  {
    includeKind = false,
    includeFormat = true,
  }: { includeKind?: boolean; includeFormat?: boolean } = {},
): string {
  const params = new URLSearchParams();

  if (meta.id) params.set("id", meta.id);
  if (includeFormat && meta.format) params.set("format", meta.format);
  if (meta.language) params.set("lang", meta.language);
  if (meta.label) params.set("label", meta.label);
  if (meta.wid) params.set("wid", meta.wid);
  if (meta.isDefault) params.set("default", "true");
  if (includeKind && meta.kind) params.set("kind", meta.kind);

  const query = params.toString();
  return query ? `#${query}` : "";
}

/**
 * Updates existing frontmatter by adding or replacing track fields
 */
export async function addTracksToFrontmatter(
  tracks: TextTrackInfo[],
  ctx: { fileManager: FileManager; note: TFile; metadataCache: MetadataCache },
): Promise<void> {
  await ctx.fileManager.processFrontMatter(ctx.note, (fm) => {
    const captions = tracks
      .filter((t) => t.meta.kind === "captions")
      .map((t) =>
        encodeTextTrackInfo(t, {
          metadataCache: ctx.metadataCache,
          sourcePath: ctx.note.path,
        }),
      );
    if (captions.length > 0) {
      if (typeof fm[CAPTION_FIELD] === "string") {
        fm[CAPTION_FIELD] = [fm[CAPTION_FIELD], ...captions];
      } else if (Array.isArray(fm[CAPTION_FIELD])) {
        fm[CAPTION_FIELD] = [...fm[CAPTION_FIELD], ...captions];
      } else {
        console.warn(
          `Invalid caption field: ${CAPTION_FIELD}, overwrite with new value`,
          fm[CAPTION_FIELD],
        );
        fm[CAPTION_FIELD] = captions;
      }
    }
    const subtitles = tracks
      .filter((t) => t.meta.kind === "subtitles")
      .map((t) =>
        encodeTextTrackInfo(t, {
          metadataCache: ctx.metadataCache,
          sourcePath: ctx.note.path,
        }),
      );
    if (subtitles.length > 0) {
      if (typeof fm[SUBTITLE_FIELD] === "string") {
        fm[SUBTITLE_FIELD] = [fm[SUBTITLE_FIELD], ...subtitles];
      } else if (Array.isArray(fm[SUBTITLE_FIELD])) {
        fm[SUBTITLE_FIELD] = [...fm[SUBTITLE_FIELD], ...subtitles];
      } else {
        console.warn(
          `Invalid subtitle field: ${SUBTITLE_FIELD}, overwrite with new value`,
          fm[SUBTITLE_FIELD],
        );
        fm[SUBTITLE_FIELD] = subtitles;
      }
    }
  });
}
