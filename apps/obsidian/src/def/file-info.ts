import { assertNever } from "@std/assert/unstable-never";

export interface FileInfo {
  extension: string;
  basename: string;
  path: string;
}

export type FileLike =
  | { name: string }
  | { basename: string; extension: string }
  | { path: string };

export type FileName = {
  extension: string;
  basename: string;
};

export function fileNameFrom(input: FileLike, sep = "/"): FileName | null {
  if ("basename" in input && "extension" in input) {
    return {
      extension: input.extension,
      basename: input.basename,
    };
  }
  if ("name" in input) {
    const segments = input.name.split(".");
    if (segments.length <= 1) {
      return null;
    }
    return {
      extension: segments.pop()!,
      basename: segments.join("."),
    };
  }
  if ("path" in input) {
    const filename = input.path.split(sep).pop()!;
    const segments = filename.split(".");
    if (segments.length <= 1) {
      return null;
    }
    return {
      extension: segments.pop()!,
      basename: segments.join("."),
    };
  }
  assertNever(input);
}

export function toFileInfo(
  filepath: string,
  sep = "/",
): FileInfo & { name: string; parent: string } {
  const filename = filepath.split(sep).pop()!;
  const extension = filename.split(".").pop()!;
  const info = {
    name: filename,
    path: filepath,
    parent: filepath.slice(0, -filename.length - 1),
  };
  if (extension === filename) {
    return {
      extension: "",
      basename: filename,
      ...info,
    };
  }
  return {
    extension,
    basename: filename.slice(0, -extension.length - 1),
    ...info,
  };
}
