// eslint-disable-next-line @typescript-eslint/naming-convention

import {
  type default as MediaF<PERSON>View,
  type MediaFileViewType,
  MEDIA_FILE_VIEW_TYPE,
} from "@/media-view/file-view";
import {
  type default as MediaRemoteView,
  type MediaRemoteViewState,
  type MediaRemoteViewType,
  MEDIA_REMOTE_VIEW_TYPE,
} from "@/media-view/remote-view";
import {
  type default as MediaUrlView,
  type MediaUrlViewState,
  type MediaUrlViewType,
  MEDIA_URL_VIEW_TYPE,
} from "@/media-view/url-view";
import type { WorkspaceLeaf } from "obsidian";
import type { FileMediaInfo } from "./media-info";

export type MediaViewType =
  | MediaFileViewType
  | MediaUrlViewType
  | MediaRemoteViewType;

export function isMediaViewType(type: string): type is MediaViewType {
  return (
    type === MEDIA_FILE_VIEW_TYPE ||
    type === MEDIA_URL_VIEW_TYPE ||
    type === MEDIA_REMOTE_VIEW_TYPE
  );
}

export type MediaLeafSnapshot =
  | FileMediaLeafSnapshot
  | UrlMediaLeafSnapshot
  | RemoteMediaLeafSnapshot;

export type FileMediaLeafSnapshot = MediaLeafSnapshotBase<MediaFileView> & {
  type: typeof MEDIA_FILE_VIEW_TYPE;
  state: { media: FileMediaInfo | null };
};

export type UrlMediaLeafSnapshot = MediaLeafSnapshotBase<MediaUrlView> & {
  type: typeof MEDIA_URL_VIEW_TYPE;
  state: MediaUrlViewState;
};

export type RemoteMediaLeafSnapshot = MediaLeafSnapshotBase<MediaRemoteView> & {
  type: typeof MEDIA_REMOTE_VIEW_TYPE;
  state: MediaRemoteViewState;
};

/**
 * A snapshot of a media leaf, including the leaf itself and its state.
 * If leaf revealed, use leaf.view to get non-deferred view.
 */
type MediaLeafSnapshotBase<View extends MediaView = MediaView> = {
  activeTime: number;
  pinned: boolean | undefined;
  active: boolean | undefined;
  leaf: WorkspaceLeaf;
  /**
   * undefined if being deferred to be loaded
   * @see https://docs.obsidian.md/Plugins/Guides/Understanding+deferred+views
   */
  view: View | undefined;
};

export type MediaView = MediaFileView | MediaUrlView | MediaRemoteView;
