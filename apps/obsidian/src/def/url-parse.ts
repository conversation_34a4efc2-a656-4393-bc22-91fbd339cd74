import URLParser from "@mx/shared/url-parse/parser";

import youtube, { normalizeYouTubeUrl } from "@mx/shared/url-parse/youtube";
import vimeo, { normalizeVimeoUrl } from "@mx/shared/url-parse/vimeo";
import baiduPan, { normalizeBaiduPanUrl } from "@mx/shared/url-parse/baidu-pan";
import bilibili, { normalizeBilibiliUrl } from "@mx/shared/url-parse/bilibili";
import coursera, { normalizeCourseraUrl } from "@mx/shared/url-parse/coursera";
import type {
  HostedUrlInfo,
  HostedVid,
  MediaInfoSrc,
  UrlMediaInfo,
} from "./media-info";
import { assertNever } from "@std/assert/unstable-never";
import { toURL } from "@mx/shared/utils/to-url";
import type { MediaType } from "./media-type";
import { normalizeWin32FileUri } from "@/patch/win32-file-url";
import { parseHashProps } from "@mx/shared/utils/hash-prop";
import type { TempFragment } from "@mx/shared/time/temporal-frag";
import { toTempFragString } from "@mx/shared/time/format";

export const urlParser = new URLParser([
  ...youtube,
  ...vimeo,
  ...baiduPan,
  ...bilibili,
  ...coursera,
]);

export type SupportedHostedMediaUrl = NonNullable<
  ReturnType<typeof urlParser.parse>
>;

function mediaUrlToMediaInfo(
  parsed: SupportedHostedMediaUrl,
): HostedUrlInfo | null {
  if (parsed.host === "youtube") {
    if (parsed.variant === "short-uri") return null;
    return {
      type: "url:hosted",
      vid: parsed.vid,
      url: toNormalizedUrl(parsed.vid),
    };
  }
  if (parsed.host === "vimeo") {
    return {
      type: "url:hosted",
      vid: parsed.vid,
      url: toNormalizedUrl(parsed.vid),
    };
  }
  if (parsed.host === "baidu-pan") {
    return {
      type: "url:hosted",
      vid: parsed.vid,
      url: toNormalizedUrl(parsed.vid),
    };
  }
  if (parsed.host === "bilibili") {
    if (parsed.vid.type === "short-uri") return null;
    return {
      type: "url:hosted",
      vid: parsed.vid,
      url: toNormalizedUrl(parsed.vid),
    };
  }
  if (parsed.host === "coursera") {
    return {
      type: "url:hosted",
      vid: parsed.vid,
      url: toNormalizedUrl(parsed.vid),
    };
  }
  assertNever(parsed);
}

/** sort query params, strip hash to normalize url */
export function normalizeDirectUrl(
  url: URL | string,
  t?: TempFragment | null,
): URL {
  let output = new URL(url);
  if (output.protocol === "file:") {
    output = normalizeWin32FileUri(output);
  }
  output.searchParams.sort();
  output.hash = "";
  if (t) {
    output.hash = toTempFragString(t) ?? "";
  }
  return output;
}

export function parseUrl(
  input: string | URL,
  { mediaType }: { mediaType?: MediaType } = {},
): MediaInfoSrc<UrlMediaInfo> | null {
  const url = toURL(input);
  if (!url) return null;
  const hash = parseHashProps(url.hash);
  if (url.protocol === "file:") {
    return {
      info: { type: "url:direct", mediaType, url: normalizeDirectUrl(url) },
      hash,
    };
  }
  const hostedUrl = urlParser.parse(url);
  mediaType = mediaType ?? hash.as;
  if (!hostedUrl) {
    return {
      info: { type: "url:direct", mediaType, url: normalizeDirectUrl(url) },
      hash,
    };
  }
  const info = mediaUrlToMediaInfo(hostedUrl);
  if (!info) return null;
  return { info, hash };
}

export function toNormalizedUrl(target: HostedVid, t?: TempFragment | null) {
  if (target.host === "youtube") {
    return normalizeYouTubeUrl(target, t);
  }
  if (target.host === "bilibili") {
    return normalizeBilibiliUrl(target, t);
  }
  if (target.host === "coursera") {
    return normalizeCourseraUrl(target, t);
  }
  if (target.host === "baidu-pan") {
    return normalizeBaiduPanUrl(target, t);
  }
  if (target.host === "vimeo") {
    return normalizeVimeoUrl(target, t);
  }
  assertNever(target);
}
