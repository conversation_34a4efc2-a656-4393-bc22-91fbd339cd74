import type { CaptionsFileFormat } from "media-captions";

import * as v from "valibot";

const exts = {
  video: ["mp4", "webm", "ogv", "mov", "mkv"],
  audio: ["mp3", "wav", "m4a", "3gp", "flac", "ogg", "oga", "opus"],
  track: ["vtt", "srt", "ssa", "ass"],
} as const;

export const mediaExtensions = [...exts.video, ...exts.audio];
export const trackExtensions = [...exts.track];
export type SupportedMediaExt = (typeof mediaExtensions)[number];

const schemas = {
  video: v.picklist(exts.video),
  audio: v.picklist(exts.audio),
  track: v.picklist(exts.track),
  media: v.picklist(mediaExtensions),
} as const;

export const trackFormatSchema = schemas.track;

export const getMediaExts = (): { name: MediaType; extensions: string[] }[] => [
  { name: "video", extensions: [...exts.video] },
  { name: "audio", extensions: [...exts.audio] },
];

export function isMediaFile<F extends { extension: string }>(
  file: F,
): file is F & { extension: SupportedMediaExt } {
  return v.is(schemas.media, file.extension);
}

export function isVideoFile<F extends { extension: string }>(
  file: F,
): file is F & { extension: (typeof exts.video)[number] } {
  return v.is(schemas.video, file.extension);
}

export function isAudioFile<F extends { extension: string }>(
  file: F,
): file is F & { extension: (typeof exts.audio)[number] } {
  return v.is(schemas.audio, file.extension);
}

export function isTrackFile<F extends { extension: string }>(
  file: F,
): file is F & { extension: CaptionsFileFormat } {
  return v.is(schemas.track, file.extension);
}

export function isMediaFileUrl(url: URL): boolean {
  const { pathname } = url;
  const ext = pathname.split(".").pop();
  return v.is(schemas.media, ext);
}

const mediaTypes = ["video", "audio"] as const;
export type MediaType = (typeof mediaTypes)[number];
