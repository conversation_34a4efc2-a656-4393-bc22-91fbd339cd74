import type { Component } from "obsidian";
import type MediaExtended from "@/mx-main";
import type { createStore } from "jotai";
import type { MediaInfo } from "./media-info";
import type { ScreenshotOptions } from "@mx/shared/dom/screenshot";
import type { MediaPlayerInstance } from "@vidstack/react";

export type FnScreenshot = (
  options: ScreenshotOptions,
) => Promise<ScreenshotResult<MediaInfo>>;

export type FnTimestamp = () =>
  | { currentTime: number }
  | Promise<{ currentTime: number }>;

export interface PlayerComponent extends Component {
  plugin: MediaExtended;
  store: ReturnType<typeof createStore>;
  containerEl: HTMLElement;
  getPlayer(): MediaPlayerInstance | null;
  getMediaInfo(): MediaInfo | null;
  captureScreenshot?: FnScreenshot;
  takeTimestamp?: FnTimestamp;
}

export type ScreenshotResult<T extends MediaInfo> = {
  info: T;
  blob: Blob;
  size: {
    width: number;
    height: number;
  };
  timestamp: number;
};
