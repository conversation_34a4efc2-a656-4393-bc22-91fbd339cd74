/// we encode track info in external url or internal link to track file

import { assertNever } from "@std/assert/unstable-never";
import { toFileInfo, type FileInfo } from "./file-info";
import type { CaptionsFileFormat } from "media-captions";
import {
  parseLinktext,
  Platform,
  type MetadataCache,
  type TFile,
} from "obsidian";
import * as v from "valibot";
import { trackFormatSchema } from "./media-type";
import { inferTrackInfoFromPath } from "@/transcript/resolve/infer-info";
import { getFileID } from "./tfile-id";
import { requirePath, requireUrl } from "@/lib/node";
import { toURL } from "@mx/shared/utils/to-url";

export interface TrackMeta {
  id: string | null;
  format: CaptionsFileFormat;
  kind: "subtitles" | "captions";
  language: string | null;
  label: string | null;
  isDefault: boolean;
  wid: string | null;
}

export interface UnresolvedTrackLink {
  type: "internal";
  /** path from linktext */
  path: string;
  displayText?: string;
  basename: string;
  /** parsed from subpath (hash) */
  meta: TrackMeta;
}

export interface LocalFileTrack {
  type: "file";
  src: FileInfo;
  basename: string;
  meta: TrackMeta;
}
export interface VaultFileTrack {
  type: "internal.resolved";
  src: TFile;
  basename: string;
  meta: TrackMeta;
}
export interface RemoteTrack {
  type: "url";
  src: URL;
  meta: TrackMeta;
}

export type TextTrackInfo = RemoteTrack | LocalFileTrack | VaultFileTrack;

export type MetaTextTrackInfo =
  | RemoteTrack
  | LocalFileTrack
  | UnresolvedTrackLink;

export const SUBTITLE_FIELD = "subtitles" as const;
export const CAPTION_FIELD = "captions" as const;
export const TRACK_WEBID_FIELD = "wid" as const;

const trackMetaSchema = v.object({
  id: v.nullable(v.string()),
  format: v.nullable(trackFormatSchema),
  kind: v.nullable(v.picklist(["subtitles", "captions"])),
  language: v.nullable(v.string()),
  label: v.nullable(v.string()),
  isDefault: v.boolean(),
  wid: v.nullable(v.string()),
});

export function parseTrackMeta(
  hash: string,
  context: {
    kind: "subtitles" | "captions";
    format?: CaptionsFileFormat | null;
    language?: string | null;
  },
): TrackMeta {
  const query = new URLSearchParams(hash.replace(/^#/, ""));

  const result = v.parse(trackMetaSchema, {
    id: query.get("id"),
    format: query.get("format"),
    kind: query.get("kind"),
    language: query.get("lang") || query.get("language"),
    label: query.get("label"),
    wid: query.get(TRACK_WEBID_FIELD),
    isDefault: query.get("default") === "true" || query.get("default") === "",
  });

  return {
    ...result,
    kind: result.kind || context.kind,
    format: result.format || context.format || "vtt",
    language: result.language || context.language || null,
  };
}

export function getMetaTrackInfoID(info: MetaTextTrackInfo) {
  if (info.type === "internal") {
    return `link:${info.path}`;
  }
  return getTrackInfoID(info);
}

export function getTrackInfoID(info: TextTrackInfo) {
  if (info.type === "url") {
    // url can encode uid within hash, prefer it over url
    if (info.meta.id) return `url:#${info.meta.id}`;
    return `url:${info.src.toString()}`;
  }
  // file don't have way to encode uid within path, we just use path as an uid
  if (info.type === "file") {
    return `file:${info.src.path}`;
  }
  if (info.type === "internal.resolved") {
    return `internal:${getFileID(info.src)}`;
  }
  assertNever(info);
}

export type ResolveTrackLinkCtx = {
  metadataCache: MetadataCache;
  sourcePath: string;
};

function parseFileURL(link: URL): FileInfo | null {
  if (!Platform.isDesktopApp) return null;
  try {
    const { fileURLToPath } = requireUrl();
    const { sep } = requirePath();
    const path = fileURLToPath(link.toString());
    return toFileInfo(path, sep);
  } catch (e) {
    console.warn(`Invalid file URL: ${link.toString()}`, e);
    return null;
  }
}

export function parseTextTrackInfo(
  encoded: string,
  {
    sourcePath,
    metadataCache,
    kind = "captions",
  }: {
    kind?: "subtitles" | "captions";
    sourcePath: string;
    metadataCache: MetadataCache;
  },
): TextTrackInfo | null {
  const url = toURL(encoded);
  if (url) return urlToTrackLink(url, { kind });
  if (encoded.startsWith("[[") && encoded.endsWith("]]")) {
    const link = encoded.slice(2, -2);
    const { link: linktext, displayText } = parseLink(link);
    const unresolved = linkToTrackLink({ link: linktext, displayText }, kind);
    if (!unresolved) return null;
    return resolveTrackLink(unresolved, { metadataCache, sourcePath });
  }
  return null;
}

/**
 * @param input wikilink without `[[` and `]]`
 * @returns linktext and displayText
 */
function parseLink(input: string): { link: string; displayText?: string } {
  const match = input.match(/\\?\|/);
  if (!match?.index) return { link: input };
  return {
    link: input.slice(0, match.index),
    displayText: input.slice(match.index + match[0].length),
  };
}

export function urlToTrackLink(
  link: URL,
  { kind, ...meta }: Partial<TrackMeta> & { kind: "subtitles" | "captions" },
): LocalFileTrack | RemoteTrack | null {
  try {
    const trackinfo = inferTrackInfoFromPath({ path: link.pathname });
    const parsedMeta = parseTrackMeta(link.hash, {
      kind,
      format: trackinfo?.format,
      language: trackinfo?.language,
    });
    if (link.protocol !== "file:") {
      return {
        type: "url",
        src: link,
        meta: { ...parsedMeta, ...meta },
      };
    }
    if (!trackinfo) return null;
    const fileinfo = parseFileURL(link);
    if (!fileinfo) return null;
    return {
      type: "file",
      basename: fileinfo.basename,
      src: fileinfo,
      meta: { ...parsedMeta, ...meta },
    };
  } catch (e) {
    if (e instanceof v.ValiError) {
      console.warn(`Invalid track link: ${link}`, e.issues);
    } else {
      console.warn(`Invalid track link: ${link}`, e);
    }
    return null;
  }
}

export function fileToTrack(
  file: TFile,
  { kind, ...meta }: Partial<TrackMeta> & { kind: "subtitles" | "captions" },
): VaultFileTrack | null {
  const trackinfo = inferTrackInfoFromPath(file);
  if (!trackinfo) return null;
  const parsedMeta = parseTrackMeta("", {
    kind,
    format: trackinfo.format,
    language: trackinfo.language,
  });
  return {
    type: "internal.resolved",
    basename: trackinfo.basename,
    src: file,
    meta: {
      ...parsedMeta,
      ...meta,
    },
  };
}

export function linkToTrackLink(
  link: { link: string; displayText?: string },
  kind: "subtitles" | "captions",
): UnresolvedTrackLink | null {
  try {
    const { path, subpath: hash } = parseLinktext(link.link);
    const trackinfo = inferTrackInfoFromPath({ path });
    if (!trackinfo) return null;
    const meta = parseTrackMeta(hash, {
      kind,
      format: trackinfo.format,
      language: trackinfo.language,
    });
    return {
      type: "internal",
      path,
      basename: trackinfo.basename,
      meta,
      displayText: link.displayText,
    };
  } catch (e) {
    if (e instanceof v.ValiError) {
      console.warn(`Invalid track link: ${link.link}`, e.issues);
    } else {
      console.warn(`Invalid track link: ${link.link}`, e);
    }
    return null;
  }
}

export function resolveTrackLink(
  link: UnresolvedTrackLink,
  ctx: ResolveTrackLinkCtx,
): VaultFileTrack | null {
  const file = ctx.metadataCache.getFirstLinkpathDest(
    link.path,
    ctx.sourcePath,
  );
  if (!file) return null;
  const trackinfo = inferTrackInfoFromPath(file);
  if (!trackinfo) return null;
  return {
    type: "internal.resolved",
    src: file,
    basename: trackinfo.basename,
    meta: {
      ...link.meta,
      format: link.meta.format || trackinfo.format,
      language: link.meta.language || trackinfo.language,
    },
  };
}

export function isTrackInfoEqual(
  a: TextTrackInfo | undefined | null,
  b: TextTrackInfo | undefined | null,
) {
  if (!a || !b) return false;
  return getTrackInfoID(a) === getTrackInfoID(b);
}
