import type { TFile } from "obsidian";
import type { YouTubeVid } from "@mx/shared/url-parse/youtube";
import type { VimeoVid } from "@mx/shared/url-parse/vimeo";
import { isBrowserVid, type BrowserVid } from "@mx/ext-fn/app2ext/playback";
import type { MediaType } from "./media-type";
import { assertNever } from "@std/assert/unstable-never";
import type { HashProps } from "@mx/shared/utils/hash-prop";
import { getFileID } from "./tfile-id";

export interface FileMediaInfo {
  type: "file";
  mediaType?: MediaType;
  file: TFile;
}

export interface DirectUrlInfo {
  type: "url:direct";
  mediaType?: MediaType;
  url: URL;
}

export interface HostedUrlInfo {
  type: "url:hosted";
  vid: HostedVid;
  /** must be normalized url from vid */
  url: URL;
}

export type EmbedVid = YouTubeVid | VimeoVid;
export type HostedVid = BrowserVid | EmbedVid;

export type MediaInfo = FileMediaInfo | DirectUrlInfo | HostedUrlInfo;

export type UrlMediaInfo = DirectUrlInfo | HostedUrlInfo;

export function isUrlMediaInfo(
  info: Record<string, any>,
): info is UrlMediaInfo {
  return info.type === "url:direct" || info.type === "url:hosted";
}

export interface EmbedMediaInfo extends HostedUrlInfo {
  type: "url:hosted";
  vid: EmbedVid;
}
export interface BrowserMediaInfo extends HostedUrlInfo {
  type: "url:hosted";
  vid: BrowserVid;
}

function isEmbedVid(vid: HostedVid): vid is EmbedVid {
  return vid.host === "youtube" || vid.host === "vimeo";
}

export function isEmbedMediaInfo(info: MediaInfo): info is EmbedMediaInfo {
  return info.type === "url:hosted" && isEmbedVid(info.vid);
}

export function isBrowserMediaInfo(info: MediaInfo): info is BrowserMediaInfo {
  return info.type === "url:hosted" && isBrowserVid(info.vid);
}

export function getMediaInfoID(mediaInfo: MediaInfo): string {
  if (mediaInfo.type === "file") {
    return `file:${getFileID(mediaInfo.file)}`;
  }
  if (mediaInfo.type === "url:direct") {
    return `url:${mediaInfo.url.toString()}`;
  }
  if (mediaInfo.type === "url:hosted") {
    return `url:${mediaInfo.url.toString()}`;
  }
  assertNever(mediaInfo);
}

export function isMediaInfoEqual(
  a: MediaInfo | undefined | null,
  b: MediaInfo | undefined | null,
) {
  if (!a || !b) return false;
  const aKey = getMediaInfoID(a);
  const bKey = getMediaInfoID(b);
  return aKey === bKey;
}

export type MediaInfoSrc<I extends MediaInfo = MediaInfo> = {
  info: I;
  hash?: HashProps;
};
