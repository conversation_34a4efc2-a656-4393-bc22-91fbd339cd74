import { atom } from "jotai";
import { withAtomEffect } from "jotai-effect";
import { mediaInfoAtom, pluginAtom } from "./media";
import { defaultMediaMeta, type MediaNoteMeta } from "@/media-lib/parse";
import { toFileInfo } from "../file-info";
import {
  isMediaInfoEqual,
  type HostedVid,
  type MediaInfo,
} from "../media-info";
import { assertNever } from "@std/assert/unstable-never";
import { equal } from "@std/assert";
import { pick } from "@std/collections";
import type { PlayerFlipOption } from "@mx/ui";

const defaultPlayerMediaMeta = pick(defaultMediaMeta, [
  "title",
  "aspectRatio",
  "crossOrigin",
  "flip",
]);
type PlayerMediaMeta = typeof defaultPlayerMediaMeta;

export const mediaTitleAtom = atom<string>();
export const mediaFlipAtom = atom<PlayerFlipOption>();

export const mediaMetaAtom = withAtomEffect(
  atom<PlayerMediaMeta>({ ...defaultPlayerMediaMeta }),
  (get, set) => {
    const mediaInfo = get(mediaInfoAtom);
    const plugin = get(pluginAtom);
    if (!mediaInfo) return;

    const overrides = {
      title: get(mediaTitleAtom),
      flip: get(mediaFlipAtom),
    };
    const onMetaChange = (latestMeta: MediaNoteMeta | null | undefined) => {
      const next = extractMediaMeta(mediaInfo, latestMeta, overrides);
      set(mediaMetaAtom, (prev) => {
        if (equal(prev, next)) return prev;
        return next;
      });
    };
    onMetaChange(plugin.mediaLib.getMediaMeta(mediaInfo)?.meta);
    using stack = new DisposableStack();
    stack.defer(
      plugin.mediaLib.on("media-meta-changed", (media) => {
        if (!isMediaInfoEqual(media.src, mediaInfo)) return;
        onMetaChange(plugin.mediaLib.getMediaMeta(mediaInfo)?.meta);
      }),
    );
    stack.defer(
      plugin.mediaLib.on("media-meta-removed", (media) => {
        if (!isMediaInfoEqual(media.src, mediaInfo)) return;
        onMetaChange(null);
      }),
    );
    if (mediaInfo.type === "file") {
      const ref = plugin.app.vault.on("rename", (file) => {
        if (file === mediaInfo.file) {
          onMetaChange(plugin.mediaLib.getMediaMeta(mediaInfo)?.meta);
        }
      });
      stack.defer(() => plugin.app.vault.offref(ref));
    }
    const disposable = stack.move();
    return () => disposable.dispose();
  },
);

function extractMediaMeta(
  mediaInfo: MediaInfo,
  meta: MediaNoteMeta | null | undefined,
  overrides: Partial<PlayerMediaMeta>,
): PlayerMediaMeta {
  const base = {
    aspectRatio: overrides.aspectRatio || meta?.aspectRatio,
    crossOrigin: overrides.crossOrigin || meta?.crossOrigin || false,
    flip: overrides.flip || meta?.flip || "none",
  } satisfies Partial<PlayerMediaMeta>;
  if (mediaInfo.type === "file") {
    return {
      ...base,
      title: overrides.title || meta?.title || mediaInfo.file.name,
    };
  }
  if (mediaInfo.type === "url:direct") {
    const inferredFile = toFileInfo(mediaInfo.url.pathname);
    return {
      ...base,
      title: overrides.title || meta?.title || inferredFile.name,
    };
  }
  if (mediaInfo.type === "url:hosted") {
    return {
      ...base,
      title: overrides.title || meta?.title || titleFromVid(mediaInfo.vid),
    };
  }
  assertNever(mediaInfo);
}

function titleFromVid(vid: HostedVid) {
  if (vid.host === "youtube") {
    return `YouTube - ${vid.vid}`;
  }
  if (vid.host === "vimeo") {
    return `Vimeo - ${vid.vid}`;
  }
  if (vid.host === "bilibili") {
    switch (vid.type) {
      case "aid":
        return `Bilibili - ${vid.aid}`;
      case "bvid":
        return `Bilibili - ${vid.bvid}`;
      case "epid":
        return `Bilibili - ${vid.epid}`;
      case "ssid":
        return `Bilibili - ${vid.ssid}`;
      default:
        assertNever(vid);
    }
  }
  if (vid.host === "coursera") {
    return `${vid.lectureId} - ${vid.courseId} - Coursera`;
  }
  if (vid.host === "baidu-pan") {
    const file = toFileInfo(vid.path);
    return `${file.name} - Baidu Pan`;
  }
  assertNever(vid);
}
