import { atom } from "jotai";
import type { MediaSrc } from "@vidstack/react";
import type { MediaInfo } from "../media-info";
import type MediaExtended from "@/mx-main";
import { isAudioFile } from "../media-type";
import { assertNever } from "@std/assert/unstable-never";
import type { HashProps } from "@mx/shared/utils/hash-prop";
import { toFileInfo } from "../file-info";
import { Platform } from "obsidian";

export const pluginAtom = atom<MediaExtended>({} as MediaExtended);
export const mediaInfoAtom = atom<MediaInfo | null>(null);
export const mediaHashAtom = atom<HashProps>();
export const embedFlagAtom = atom<boolean>(false);

function toResourceUrl(url: URL): URL | null {
  if (url.protocol !== "file:") return url;
  if (!Platform.isDesktopApp) return null;
  const fixed = new URL(
    Platform.resourcePathPrefix + url.href.substring("file:///".length),
  );
  fixed.search = Date.now().toString();
  return fixed;
}

export const mediaSrcAtom = atom((get): MediaSrc | null => {
  const info = get(mediaInfoAtom);
  if (!info) return null;
  if (info.type === "file") {
    const plugin = get(pluginAtom);
    return {
      src: plugin.app.vault.getResourcePath(info.file),
      type: isAudioFile(info.file) ? "audio/mpeg" : "video/mp4",
    };
  }
  if (info.type === "url:direct") {
    const file = toFileInfo(info.url.pathname);
    const src = toResourceUrl(info.url)?.toString();
    const type = isAudioFile(file) ? "audio/mpeg" : "video/mp4";
    if (!src) return null;
    return { src, type };
  }
  if (info.type === "url:hosted") {
    if (info.vid.host === "youtube") {
      return `https://www.youtube.com/watch?v=${info.vid.vid}`;
    }
    if (info.vid.host === "vimeo") {
      return `https://vimeo.com/${info.vid.vid}`;
    }
    return null;
  }
  assertNever(info);
});
