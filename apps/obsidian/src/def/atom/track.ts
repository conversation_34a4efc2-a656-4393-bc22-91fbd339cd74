import { atom } from "jotai";
import { mediaInfo<PERSON>tom, pluginAtom } from "./media";
import { loadTracks } from "@/transcript/load-media-tracks";
import { getTrackInfoID, type TextTrackInfo } from "../track-info";
import {
  defineWebVTTEditorExtension,
  parseVtt,
  type WebVTTEditorExtension,
} from "@mx/ui";
import { createEditor, type NodeJSON } from "prosekit/core";
import { playerAtom } from "@mx/shared/hooks/use-player";
import { withAtomEffect } from "jotai-effect";
import type { TextTrack } from "@vidstack/react";
import { unwrap } from "jotai/utils";

export const loadedMediaTracksAtom = atom<{
  tracks: TextTrack[];
  trackInfo: TextTrackInfo[];
} | null>(null);

export const mediaTracksAtom = atom(async (get) => {
  const plugin = get(pluginAtom);
  const media = get(mediaInfoAtom);
  if (!plugin || !media) return null;
  return await loadTracks(media, {
    loader: plugin.transcriptLoader,
    settings: plugin.settings,
  });
});

export const playerTrackAtom = withAtomEffect(
  atom<NodeJSON | null>(null),
  (get, set) => {
    const player = get(playerAtom);
    if (!player) {
      set(playerTrackAtom, null);
      return;
    }
    return player.subscribe(({ textTrack }) => {
      if (!textTrack) {
        set(playerTrackAtom, null);
        return;
      }
      const load = () => {
        set(playerTrackAtom, {
          type: "doc",
          content: parseVtt(textTrack.cues),
        });
      };
      if (textTrack.readyState === 2) {
        load();
        return;
      }
      if (textTrack.readyState === 1) {
        textTrack.addEventListener("load", load, { once: true });
        return () => {
          textTrack.removeEventListener("load", load);
        };
      }
      console.error(
        `textTrack cannot become ready: ${textTrack.readyState}`,
        textTrack,
      );
    });
  },
);

export const trackInfoAtom = atom<TextTrackInfo | null>(null);

export const parsedTrackAtom = atom(
  async (get): Promise<{ node: NodeJSON; id: string } | null> => {
    const info = get(trackInfoAtom);
    const plugin = get(pluginAtom);
    if (!info) return null;
    const parsed = await plugin.transcriptLoader.loadAndParseTrack(info);
    return {
      id: getTrackInfoID(info),
      node: { type: "doc", content: parseVtt(parsed.cues) },
    };
  },
);

export const trackEditorAtom = unwrap(
  atom(async (get) => {
    const info = await get(parsedTrackAtom);
    const plugin = get(pluginAtom);
    if (!info || !plugin) return null;
    return createEditor<WebVTTEditorExtension>({
      extension: defineWebVTTEditorExtension(),
      defaultContent: info.node,
    });
  }),
  () => null,
);
export const showSearchAtom = atom(false);

// export const trackLinkedMediaAtom = unwrap(
//   atom(async (get) => {
//     const info = get(trackInfoAtom);
//     const plugin = get(pluginAtom);
//     if (!info || !plugin) return [];
//     return await plugin.transcriptLoader.getLinkedMedia(info);
//   }),
//   (prev) => prev ?? [],
// );
