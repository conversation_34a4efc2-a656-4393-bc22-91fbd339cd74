import { assertNever } from "@std/assert/unstable-never";
import { atom } from "jotai";
import { isAudioFile } from "../media-type";
import { mediaInfoAtom } from "./media";
import { addIcon } from "obsidian";
import { fileNameFrom } from "../file-info";

export const mediaIconAtom = atom<string>((get) => {
  const mediaInfo = get(mediaInfoAtom);
  if (!mediaInfo) return "lucide-file-video";
  if (mediaInfo.type === "file") {
    let mediaType = mediaInfo.mediaType;
    if (!mediaType) {
      mediaType = isAudioFile(mediaInfo.file) ? "audio" : "video";
    }
    return `lucide-file-${mediaType}`;
  }
  if (mediaInfo.type === "url:hosted") {
    if (mediaInfo.vid.host === "youtube") {
      return "youtube";
    }
    if (mediaInfo.vid.host === "vimeo") {
      return "vimeo";
    }
    if (mediaInfo.vid.host === "bilibili") {
      return "bilibili";
    }
    if (mediaInfo.vid.host === "coursera") {
      return "coursera";
    }
    if (mediaInfo.vid.host === "baidu-pan") {
      return "baidu-pan";
    }
    assertNever(mediaInfo.vid);
  }
  if (mediaInfo.type === "url:direct") {
    if (mediaInfo.url.protocol === "file:") {
      const file = fileNameFrom({ path: mediaInfo.url.pathname });
      let mediaType = mediaInfo.mediaType;
      if (!mediaType) {
        mediaType = file && isAudioFile(file) ? "audio" : "video";
      }
      return `lucide-file-${mediaType}`;
    }
    return "lucide-globe";
  }
  assertNever(mediaInfo);
});

addIcon;
