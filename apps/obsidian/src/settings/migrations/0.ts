import type { PaneType } from "obsidian";

type OpenLinkBehavior = false | PaneType | "split-horizontal" | null;

const MEDIA_EMBED_VIEW_TYPE = "mx-embed" as const;
type MediaEmbedViewType = typeof MEDIA_EMBED_VIEW_TYPE;

const MEDIA_WEBPAGE_VIEW_TYPE = "mx-webpage" as const;
type MediaWebpageViewType = typeof MEDIA_WEBPAGE_VIEW_TYPE;

const mediaTypes = ["video", "audio"] as const;
type MediaType = (typeof mediaTypes)[number];

const MEDIA_FILE_VIEW_TYPE = {
  video: "mx-file-video",
  audio: "mx-file-audio",
} as const satisfies Record<MediaType, string>;
type MediaFileViewType =
  (typeof MEDIA_FILE_VIEW_TYPE)[keyof typeof MEDIA_FILE_VIEW_TYPE];

const MEDIA_URL_VIEW_TYPE = {
  video: "mx-url-video",
  audio: "mx-url-audio",
} as const satisfies Record<MediaType, string>;
type MediaUrlViewType =
  (typeof MEDIA_URL_VIEW_TYPE)[keyof typeof MEDIA_URL_VIEW_TYPE];

type RemoteMediaViewType =
  | MediaUrlViewType
  | MediaEmbedViewType
  | MediaWebpageViewType;

type URLMatchPattern =
  | {
      baseURL?: string;
      username?: string;
      password?: string;
      protocol?: string;
      hostname?: string;
      port?: string;
      pathname?: string;
      search?: string;
      hash?: string;
    }
  | string;

const BilibiliQuality = {
  /** 240P 极速
   * 仅 MP4 格式支持
   * 仅`platform=html5`时有效
   */
  LD: 6,

  /** 360P 流畅 */
  SQ: 16,

  /** 480P 清晰 */
  SD: 32,

  /** 720P 高清
   * WEB 端默认值
   * B站前端需要登录才能选择，但是直接发送请求可以不登录就拿到 720P 的取流地址
   * **无 720P 时则为 720P60**
   */
  HD: 64,

  /** 720P60 高帧率
   * 登录认证
   */
  HD_60: 74,

  /** 1080P 高清
   * TV 端与 APP 端默认值
   * 登录认证
   */
  FHD: 80,

  /** 1080P+ 高码率
   * 大会员认证
   */
  FHD_PLUS: 112,

  /** 1080P60 高帧率
   * 大会员认证
   */
  FHD_60: 116,

  /** 4K 超清
   * 需要`fnval&128=128`且`fourk=1`
   * 大会员认证
   */
  UHD_4K: 120,

  /** HDR 真彩色
   * 仅支持 DASH 格式
   * 需要`fnval&64=64`
   * 大会员认证
   */
  HDR: 125,

  /** 杜比视界
   * 仅支持 DASH 格式
   * 需要`fnval&512=512`
   * 大会员认证
   */
  DOLBY_VISION: 126,

  /** 8K 超高清
   * 仅支持 DASH 格式
   * 需要`fnval&1024=1024`
   * 大会员认证
   */
  UHD_8K: 127,
} as const;

type BilibiliQuality = (typeof BilibiliQuality)[keyof typeof BilibiliQuality];

export interface PluginSettingsV0 {
  defaultVolume: number;
  urlMappingData: { appId: string; protocol: string; replace: string }[];
  devices: { appId: string; name: string }[];
  defaultMxLinkClick: {
    click: OpenLinkBehavior;
    alt: OpenLinkBehavior;
  };
  speedStep: number;
  enableSubtitle: boolean;
  defaultLanguage?: string;
  loadStrategy: "play" | "eager";
  linkHandler: Record<RemoteMediaViewType, URLMatchPattern[]>;
  timestampTemplate: string;
  screenshotTemplate: string;
  screenshotEmbedTemplate: string;
  insertBefore: boolean;
  /** in seconds */
  timestampOffset: number;
  biliDefaultQuality: BilibiliQuality;
  screenshotFormat: "image/png" | "image/jpeg" | "image/webp";
  screenshotQuality?: number;
  screenshotFolderPath?: string;
  subtitleFolderPath?: string;
}
