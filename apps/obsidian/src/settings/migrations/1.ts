import type { PaneType } from "obsidian";
import type { PluginSettingsV0 } from "./0";
import * as v from "valibot";
import { isoLangPattern } from "@/lib/lang-iso";

export type NewPanelBehavior = false | PaneType | "split-horizontal" | null;

const openLinkBehavior = v.union([
  v.null(),
  v.literal(false),
  v.picklist(["split-horizontal", "tab", "split", "window"]),
]);
const screenshotFormat = v.picklist(["image/png", "image/jpeg", "image/webp"]);
const insertAt = v.picklist(["before-cursor", "after-cursor"]);
const embedLoadStrategy = v.picklist(["play", "eager"]);
const hostedPrefer = v.picklist(["browser", "iframe"]);

export type HostedPrefer = v.InferOutput<typeof hostedPrefer>;
export type InsertAt = v.InferOutput<typeof insertAt>;

// const DEFAULT_SETTINGS_V1 = {
//   __VERSION__: 1,
//   "playback.default-volume": 100,
//   "playback.speed-step": 0.1,
//   "playback.preserve-pitch": true,

//   "playback.screenshot.format": "image/png",
//   "playback.screenshot.quality": 0.8,

//   "playback.track.default-enabled": false,
//   "playback.track.default-languages": [],
//   "note.embed-load-strategy": "eager",
//   "link.click-behavior": "split",
//   "link.altclick-behavior": "window",
//   "link.handle-hosted": true,
//   "link.handle-direct-url": true,
//   "link.hosted-prefer": "browser",

//   "note.template.timestamp": "\n- {{TIMESTAMP}} ",
//   "note.template.timestamp-offset": 0,
//   "note.template.screenshot": "\n- !{{SCREENSHOT}} {{TIMESTAMP}} ",
//   "note.template.screenshot-embed": "{{TITLE}}{{DURATION}}|50",
//   "note.template.insert-at": "after-cursor",

//   "media-lib.folder-path": "media-lib",
// } satisfies PluginSettingsV1;

const base = { __VERSION__: 1 } as const;
export const pluginSettingsV1 = v.object({
  __VERSION__: v.literal(1),
  "playback.default-volume": v.fallback(
    v.pipe(v.number(), v.minValue(0), v.maxValue(100)),
    100,
  ),
  "playback.speed-step": v.fallback(
    v.pipe(v.number(), v.minValue(0), v.maxValue(1)),
    0.1,
  ),
  "playback.preserve-pitch": v.fallback(v.boolean(), true),

  "playback.track.default-enabled": v.fallback(v.boolean(), false),
  "playback.track.folder-path": v.fallback(v.optional(v.string()), undefined),
  "playback.track.default-languages": v.fallback(
    v.array(
      v.pipe(v.string(), v.regex(isoLangPattern, "Invalid language code")),
    ),
    [],
  ),

  "playback.screenshot.format": v.fallback(screenshotFormat, "image/jpeg"),
  "playback.screenshot.quality": v.fallback(
    v.pipe(v.number(), v.minValue(0), v.maxValue(1)),
    0.8,
  ),
  "playback.screenshot.folder-path": v.fallback(
    v.optional(v.string()),
    undefined,
  ),

  "note.embed-load-strategy": v.fallback(embedLoadStrategy, "eager"),

  "link.click-behavior": v.fallback(openLinkBehavior, "split"),
  "link.altclick-behavior": v.fallback(openLinkBehavior, "window"),

  "link.handle-hosted": v.fallback(v.boolean(), true),
  "link.handle-direct-url": v.fallback(v.boolean(), true),
  "link.hosted-prefer": v.fallback(hostedPrefer, "browser"),

  "note.template.timestamp": v.fallback(v.string(), "\n- {{TIMESTAMP}} "),
  "note.template.timestamp-offset": v.fallback(v.number(), 0),
  "note.template.screenshot": v.fallback(
    v.string(),
    "\n- !{{SCREENSHOT}} {{TIMESTAMP}} ",
  ),
  "note.template.screenshot-embed": v.fallback(
    v.string(),
    "{{TITLE}}{{DURATION}}|50",
  ),
  "note.template.insert-at": v.fallback(insertAt, "after-cursor"),

  "media-lib.folder-path": v.fallback(v.string(), "media-lib"),
});

export type PluginSettingsV1 = v.InferInput<typeof pluginSettingsV1>;

export function migrateSettingsV1(settings: any): PluginSettingsV1 {
  const prev = settings as Partial<PluginSettingsV0>;
  // Create mutable object for migration
  const migrated = v.parse(pluginSettingsV1, base);

  // Migrate playback settings
  if (prev.defaultVolume !== undefined) {
    migrated["playback.default-volume"] = prev.defaultVolume;
  }
  if (prev.speedStep !== undefined) {
    migrated["playback.speed-step"] = prev.speedStep;
  }

  // Migrate subtitle settings
  if (prev.enableSubtitle !== undefined) {
    migrated["playback.track.default-enabled"] = prev.enableSubtitle;
  }

  // Optional fields
  if (prev.subtitleFolderPath !== undefined) {
    migrated["playback.track.folder-path"] = prev.subtitleFolderPath;
  }

  if (prev.defaultLanguage !== undefined) {
    migrated["playback.track.default-languages"] = prev.defaultLanguage
      ? [prev.defaultLanguage]
      : [];
  }

  // Migrate screenshot settings
  if (prev.screenshotFormat !== undefined) {
    // Only assign if it's one of the allowed formats
    const format = prev.screenshotFormat;
    if (v.is(screenshotFormat, format)) {
      migrated["playback.screenshot.format"] = format;
    }
  }

  if (prev.screenshotQuality !== undefined) {
    migrated["playback.screenshot.quality"] = prev.screenshotQuality;
  }

  if (prev.screenshotFolderPath !== undefined) {
    migrated["playback.screenshot.folder-path"] = prev.screenshotFolderPath;
  }

  // Migrate editor settings
  if (prev.loadStrategy !== undefined) {
    const strategy = prev.loadStrategy;
    if (v.is(embedLoadStrategy, strategy)) {
      migrated["note.embed-load-strategy"] = strategy;
    }
  }

  // Migrate mx-link settings
  if (prev.defaultMxLinkClick?.click !== undefined) {
    const behavior = prev.defaultMxLinkClick.click;
    if (v.is(openLinkBehavior, behavior)) {
      migrated["link.click-behavior"] = behavior;
    }
  }

  if (prev.defaultMxLinkClick?.alt !== undefined) {
    const behavior = prev.defaultMxLinkClick.alt;
    if (v.is(openLinkBehavior, behavior)) {
      migrated["link.altclick-behavior"] = behavior;
    }
  }

  // Migrate template settings
  if (prev.timestampTemplate !== undefined) {
    migrated["note.template.timestamp"] = prev.timestampTemplate;
  }
  if (prev.timestampOffset !== undefined) {
    migrated["note.template.timestamp-offset"] = prev.timestampOffset;
  }
  if (prev.screenshotTemplate !== undefined) {
    migrated["note.template.screenshot"] = prev.screenshotTemplate;
  }
  if (prev.screenshotEmbedTemplate !== undefined) {
    migrated["note.template.screenshot-embed"] = prev.screenshotEmbedTemplate;
  }
  if (prev.insertBefore !== undefined) {
    migrated["note.template.insert-at"] = prev.insertBefore
      ? "before-cursor"
      : "after-cursor";
  }

  // Return with type assertion
  return v.parse(pluginSettingsV1, migrated);
}

export function loadSettingsV1(inputs?: any): PluginSettingsV1 {
  const settings = v.safeParse(pluginSettingsV1, inputs || base);
  if (settings.success) return settings.output;
  const defaults = v.parse(pluginSettingsV1, base);
  console.error(
    "Invalid settings in plugin settings v1, fallback to default",
    settings.issues,
  );
  return defaults;
  // const validated = pluginSettingsV1(settings);
  // if (!(validated instanceof type.errors)) {
  //   return validated;
  // }
  // const invalidKeys = distinct(
  //   validated.map((err) => err.path[0]).filter((key) => key !== undefined),
  // ) as (keyof PluginSettingsV1)[];
  // if (invalidKeys.length > 0) {
  //   console.error("No valid settings found in plugin settings v1", settings);
  //   return DEFAULT_SETTINGS_V1;
  // }
  // if (invalidKeys.includes("__VERSION__")) {
  //   throw new Error("Invalid settings version");
  // }
  // console.error(
  //   "Invalid settings in plugin settings v1, fallback to default",
  //   validated,
  //   settings,
  // );
  // return deepMerge(
  //   DEFAULT_SETTINGS_V1,
  //   omit(settings as PluginSettingsV1, invalidKeys),
  // );
}
