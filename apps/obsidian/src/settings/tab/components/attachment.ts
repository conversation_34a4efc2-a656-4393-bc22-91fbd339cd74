import { Setting } from "obsidian";
import type { SettingsContext } from "./_ctx";

export function screenshotDir(ctx: SettingsContext): Disposable {
  using stack = new DisposableStack();
  const screenshotDir = ctx.settings.optionalString(
    "playback.screenshot.folder-path",
  );
  new Setting(ctx.containerEl)
    .setName("Default location for new screenshots")
    .setDesc(
      `Where newly created screenshots will be saved. You can go to "Files and links" to change the default location of new attachments.`,
    )
    .addDropdown((d) =>
      d
        .addOptions({
          default: "Default location for new attachments",
          specific: "In the folder specified below",
        })
        .setValue(
          typeof screenshotDir.value === "undefined" ? "default" : "specific",
        )
        .onChange((val) => {
          if (val === "default") {
            screenshotDir.set(undefined);
          } else {
            screenshotDir.set("/");
          }
        })
        .then((d) =>
          stack.use(
            screenshotDir.sub((s) =>
              d.setValue(typeof s === "undefined" ? "default" : "specific"),
            ),
          ),
        ),
    );
  new Setting(ctx.containerEl)
    .setName("Screenshot folder path")
    .setDesc("Place newly created screenshots in this folder.")
    .then((setting) => {
      if (typeof screenshotDir.value === "undefined") {
        setting.settingEl.style.display = "none";
      }
      setting.addText((input) =>
        input
          .setValue(screenshotDir.value || "/")
          .onChange((val) => {
            if (val === "") {
              input.setValue("/");
              screenshotDir.set("/");
            } else {
              screenshotDir.set(val);
            }
          })
          .then((input) =>
            stack.use(
              screenshotDir.sub((s) => {
                if (typeof s === "undefined") {
                  input.setValue("/");
                  setting.settingEl.style.display = "none";
                } else {
                  input.setValue(s);
                  setting.settingEl.style.display = "";
                }
              }),
            ),
          ),
      );
    });
  return stack.move();
}
