import { Setting } from "obsidian";
import { setLimit } from "../utils";
import type { SettingsContext } from "./_ctx";

export function playbackSection(ctx: SettingsContext): Disposable {
  using stack = new DisposableStack();
  new Setting(ctx.containerEl).setHeading().setName("Playback");
  new Setting(ctx.containerEl)
    .setName("Default volume")
    .setDesc("The default volume for media files")
    .then((settings) => {
      const num = ctx.settings.number("playback.default-volume");
      const inputNum = ctx.settings.inputNumber("playback.default-volume");
      settings
        .addSlider((slide) =>
          slide
            .setLimits(0, 100, 1)
            .setValue(num.value)
            .onChange(num.set)
            .then((slide) => {
              stack.use(num.sub((s) => slide.setValue(s)));
            }),
        )
        .addText((text) =>
          text
            .setValue(inputNum.value)
            .onChange(inputNum.set)
            .then(setLimit(0, 100, 1))
            .then((input) => {
              stack.use(inputNum.sub((s) => input.setValue(s)));
            }),
        );
    });
  new Setting(ctx.containerEl)
    .setName("Preserve pitch")
    .setDesc(
      "Preserve audio pitch to compensate for playback rate changes. Won't work on video hosted in YouTube and Vimeo",
    )
    .then((settings) => {
      const acc = ctx.settings.boolean("playback.preserve-pitch");
      settings.addToggle((toggle) => {
        toggle.setValue(acc.value).onChange(acc.set);
        stack.use(acc.sub((s) => toggle.setValue(s)));
      });
    });
  new Setting(ctx.containerEl)
    .setName("Fine-tune speed adjustment")
    .setDesc(
      "Set the increment/decrement value for fine-tuning playback speed using the fine-tune speed control commands.",
    )
    .then((s) => {
      const num = ctx.settings.number("playback.speed-step");
      const inputNum = ctx.settings.inputNumber("playback.speed-step");
      s.addSlider((slide) =>
        slide
          .setLimits(0.01, 2, 0.01)
          .setValue(num.value)
          .onChange(num.set)
          .then((slide) => {
            stack.use(num.sub((s) => slide.setValue(s)));
          }),
      ).addText((text) =>
        text
          .then(setLimit(0.01, 2, 0.01))
          .setValue(inputNum.value)
          .onChange(inputNum.set)
          .then((input) => {
            stack.use(inputNum.sub((s) => input.setValue(s)));
          }),
      );
      s.controlEl.appendText("x");
    });

  return stack.move();
}
