import { Setting } from "obsidian";
import type { SettingsContext } from "./_ctx";

export function screenshotFormat(ctx: SettingsContext): Disposable {
  using stack = new DisposableStack();
  new Setting(ctx.containerEl)
    .setName("Save screenshot as")
    .setDesc(
      createFragment((frag) => {
        frag.appendText("Choose the format of the saved screenshot");
        frag.createEl("br");
        frag.appendText(
          "WEBP has better compression ratio, but would fallback to JPEG in unsupported browsers like iOS or iPadOS",
        );
      }),
    )
    .addDropdown((d) => {
      const acc = ctx.settings.string("playback.screenshot.format");
      d.addOptions({
        "image/png": "PNG",
        "image/jpeg": "JPEG",
        "image/webp": "WEBP",
      })
        .setValue(acc.value)
        .onChange((val) => acc.set(val))
        .then((d) => stack.use(acc.sub((s) => d.setValue(s))));
    });
  return stack.move();
}
