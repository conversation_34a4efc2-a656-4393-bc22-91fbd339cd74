import { Notice, Setting } from "obsidian";
import type { SettingsContext } from "./_ctx";
import { isoLangPattern } from "@/lib/lang-iso";

export function textTrackSection(ctx: SettingsContext): Disposable {
  using stack = new DisposableStack();
  new Setting(ctx.containerEl).setHeading().setName("Text tracks");
  new Setting(ctx.containerEl)
    .setName("Enable text tracks by default")
    .setDesc("Show subtitles/captions whenever available.")
    .then((settings) => {
      const acc = ctx.settings.boolean("playback.track.default-enabled");
      settings.addToggle((toggle) => {
        toggle.setValue(acc.value).onChange(acc.set);
        stack.use(acc.sub((s) => toggle.setValue(s)));
      });
    });

  defaultLanguages(stack, ctx);
  trackFolder(stack, ctx);

  return stack.move();
}

const defaultLanguages = (_stack: DisposableStack, ctx: SettingsContext) => {
  const acc = ctx.settings.create("playback.track.default-languages");
  const form = ctx.containerEl.createEl("form", {});
  form.onsubmit = (evt) => {
    evt.preventDefault();
    // get input name="default-languages" value
    const formData = new FormData(form);
    let value = formData.get("default-languages");
    if (typeof value !== "string") {
      new Notice("Invalid value");
      return;
    }
    value = value.trim();
    if (!value) {
      acc.set([]);
      new Notice("Languages cleared");
      return;
    }
    const list = value.split("\n").map((lang) => lang.trim());
    const errMessage = list.reduce<string[]>((acc, lang, idx) => {
      if (!isoLangPattern.test(lang)) {
        acc.push(`Invalid language code at line ${idx + 1}: ${lang}`);
      }
      return acc;
    }, []);
    if (errMessage.length > 0) {
      new Notice(errMessage.join("\n"));
      return;
    }
    acc.set(list);
    new Notice("Languages saved");
  };
  const setting = new Setting(form)
    .setName("Default languages")
    .setDesc(
      createFragment((frag) => {
        frag.appendText(
          "A list of languages to show subtitles/captions for by default.",
        );
        frag.createEl("br");
        frag.appendText("Each language code should be on a new line.");
      }),
    )
    .addTextArea((text) => {
      text.setValue(acc.value.join("\n"));
      text.inputEl.name = "default-languages";
      text.inputEl.rows = 6;
      text.inputEl.cols = 10;
    })
    .addButton((btn) => {
      btn.setTooltip("Save").setIcon("lucide-save");
      btn.buttonEl.type = "submit";
    });
  form.className = setting.settingEl.className;
  while (setting.settingEl.firstChild) {
    form.appendChild(setting.settingEl.firstChild);
  }
  setting.settingEl.remove();
};

const trackFolder = (stack: DisposableStack, ctx: SettingsContext) => {
  const trackDir = ctx.settings.optionalString("playback.track.folder-path");
  new Setting(ctx.containerEl)
    .setName("Default location for text tracks")
    .setDesc(
      'Where text tracks (subtitles/captions) will be searched for. You can go to "Files and links" to change the default location of new attachments.',
    )
    .addDropdown((d) =>
      d
        .addOptions({
          default: "Default location for new attachments",
          specific: "In the folder specified below",
        })
        .setValue(
          typeof trackDir.value === "undefined" ? "default" : "specific",
        )
        .onChange((val) => {
          if (val === "default") {
            trackDir.set(undefined);
          } else {
            trackDir.set("/");
          }
        })
        .then((d) =>
          stack.use(
            trackDir.sub((s) =>
              d.setValue(typeof s === "undefined" ? "default" : "specific"),
            ),
          ),
        ),
    );
  new Setting(ctx.containerEl)
    .setName("Text track folder path")
    .setDesc("Search for text tracks in this folder.")
    .then((setting) => {
      if (typeof trackDir.value === "undefined") {
        setting.settingEl.style.display = "none";
      }
      setting.addText((input) =>
        input
          .setValue(trackDir.value || "/")
          .onChange((val) => {
            if (val === "") {
              input.setValue("/");
              trackDir.set("/");
            } else {
              trackDir.set(val);
            }
          })
          .then((input) =>
            stack.use(
              trackDir.sub((s) => {
                if (typeof s === "undefined") {
                  input.setValue("/");
                  setting.settingEl.style.display = "none";
                } else {
                  input.setValue(s);
                  setting.settingEl.style.display = "";
                }
              }),
            ),
          ),
      );
    });
};
