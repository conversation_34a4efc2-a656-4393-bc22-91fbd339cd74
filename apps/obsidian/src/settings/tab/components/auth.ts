import { Setting, ButtonComponent, Notice } from "obsidian";
import type { SettingsContext } from "./_ctx";
import { supabase } from "@/auth/supabase";
import { LoginDialog } from "@/auth/login-dialog";
import type MxPlugin from "@/mx-main";
import type { User } from "@supabase/supabase-js";

interface AuthContext extends SettingsContext {
  plugin: MxPlugin;
}

export function authSection(ctx: AuthContext): Disposable {
  using stack = new DisposableStack();

  const authContainer = ctx.containerEl.createDiv("auth-section");

  // Create dedicated header setting
  new Setting(authContainer).setName("Account").setHeading();

  // Create a reactive auth state handler
  let authSetting: Setting | null = null;

  const updateAuthUI = (currentUser: User | null) => {
    if (authSetting) {
      authSetting.settingEl.remove();
    }

    if (currentUser) {
      const userName =
        currentUser.user_metadata.full_name || currentUser.user_metadata.name;
      const userEmail = currentUser.email;
      // User is logged in - show profile
      authSetting = new Setting(authContainer)
        .setName("Your account")
        .setDesc(
          userName
            ? `You're currently signed in as ${userName} (${userEmail}).`
            : `You're currently signed in via ${userEmail}.`,
        )
        .addButton((button) => {
          button
            .setButtonText("Sign out")
            .setWarning()
            .onClick(async () => {
              try {
                await supabase.auth.signOut();
                // The auth state change will trigger updateAuthUI
              } catch (error) {
                console.error("Error signing out:", error);
              }
            });
        });
    } else {
      // User is not logged in - show login button
      authSetting = new Setting(authContainer)
        .setName("Your account")
        .setDesc(
          "Sign in to access online features like YouTube transcript fetching, AI transcript & summary, and more.",
        )
        .addButton((button) => {
          button
            .setButtonText("Sign in")
            .setCta()
            .onClick(async () => {
              await LoginDialog.open(ctx.plugin);
              // The auth state change will trigger updateAuthUI
            });
        });
    }
  };

  // Initial auth state check
  const checkAuthState = async () => {
    try {
      const { data, error } = await supabase.auth.getUser();
      if (error) {
        console.error("Error checking auth state:", error);
        updateAuthUI(null);
      } else {
        updateAuthUI(data.user);
      }
    } catch (error) {
      console.error("Error checking auth state:", error);
      updateAuthUI(null);
    }
  };

  // Listen for auth state changes
  const {
    data: { subscription },
  } = supabase.auth.onAuthStateChange((event, session) => {
    updateAuthUI(session?.user || null);
    if (event === "SIGNED_OUT") {
      new Notice("You have been signed out");
    }
  });

  // Clean up subscription
  stack.defer(() => {
    subscription.unsubscribe();
  });

  // Initialize
  checkAuthState();

  return stack.move();
}
