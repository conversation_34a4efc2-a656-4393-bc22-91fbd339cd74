import type { AddressInfo } from "node:net";
import {
  type ExtraButtonComponent,
  Notice,
  Setting,
  type TextComponent,
  type ToggleComponent,
} from "obsidian";
import { DEFAULT_PORT } from "@/main-prefs/def";
import { setLimit } from "../utils";
import * as v from "valibot";
import { nullablePort } from "@/lib/validate/port";
import type MxPlugin from "@/mx-main";

export interface RemoteServer {
  getPort: () => Promise<number>;
  setPort: (port: number) => Promise<void>;
  getStatus: () => Promise<AddressInfo | null>;
  listen: () => Promise<boolean>;
}

export function remoteServerSection(ctx: {
  containerEl: HTMLElement;
  plugin: MxPlugin;
}): Disposable {
  using stack = new DisposableStack();
  const { containerEl } = ctx;
  const remoteServer = getRemoteServer(ctx.plugin);
  if (!remoteServer) {
    return stack.move();
  }

  // Remote Server section
  new Setting(containerEl).setHeading().setName("Browser Connect");

  // Server enable/disable toggle
  const serverToggleSetting = new Setting(containerEl)
    .setName("Enable")
    .setDesc(
      createFragment((el) => {
        el.appendText(
          "Allow controlling Obsidian from browsers with the companion extension installed. ",
        );
        el.createEl("a", {
          text: "Get the extension",
          href: "https://example.com",
        });
      }),
    );

  let serverEnabled = false;
  let portInput: TextComponent;
  let resetButton: ExtraButtonComponent;
  const updateServerToggle = async () => {
    const port = await remoteServer.getPort();
    serverEnabled = port > 0;
    serverToggle.setValue(serverEnabled);

    // Enable/disable port input and buttons based on server state
    if (portInput) {
      portInput.setDisabled(!serverEnabled);
    }
    if (resetButton) {
      resetButton.setDisabled(!serverEnabled);
    }

    // Show/hide related settings based on server state
    if (serverStatusSetting) {
      serverStatusSetting.settingEl.style.display = serverEnabled
        ? "flex"
        : "none";
    }
    if (portSetting) {
      portSetting.settingEl.style.display = serverEnabled ? "flex" : "none";
    }
    if (applyPortSetting) {
      applyPortSetting.settingEl.style.display = serverEnabled
        ? "flex"
        : "none";
    }
  };

  let serverToggle: ToggleComponent;
  serverToggleSetting.addToggle((toggle) => {
    serverToggle = toggle;
    toggle.onChange(async (value) => {
      toggle.setDisabled(true);
      try {
        if (value) {
          // Enable server with default port
          await remoteServer.setPort(DEFAULT_PORT);
          if (portInput) {
            portInput.setValue(DEFAULT_PORT.toString());
          }
        } else {
          // Disable server
          await remoteServer.setPort(-1);
          if (portInput) {
            portInput.setValue("-1");
          }
          // Call listen after disabling to apply the change
        }
        await remoteServer.listen();
        await updateStatus();
      } catch (error: unknown) {
        const errorMessage =
          error instanceof Error ? error.message : String(error);
        new Notice(
          `Failed to ${value ? "enable" : "disable"} server: ${errorMessage}`,
        );
        console.error(
          `Failed to ${value ? "enable" : "disable"} server:`,
          error,
        );
        // Revert toggle to actual state
        toggle.setValue(serverEnabled);
      } finally {
        toggle.setDisabled(false);
        await updateServerToggle();
      }
    });
  });

  // Server status
  const serverStatusSetting = new Setting(containerEl)
    .setName("Listening on")
    .setDesc("Check if browser connections are accepted");

  // Hide status setting initially (will be shown based on server state)
  serverStatusSetting.settingEl.style.display = "none";

  const statusContainer = serverStatusSetting.controlEl.createDiv({
    cls: "mx-status-container",
  });
  statusContainer.style.marginRight = "8px";

  // Function to update status display
  const updateStatus = async () => {
    statusContainer.empty();
    const status = await remoteServer.getStatus();
    const port = await remoteServer.getPort();

    if (port <= 0) {
      // Server is disabled
      const statusSpan = statusContainer.createSpan({
        cls: "mx-status-value mx-status-disabled",
      });
      statusSpan.style.color = "var(--color-orange)";
      statusSpan.style.fontWeight = "bold";
      statusSpan.textContent = "Disabled";
    } else if (status) {
      // Server is running
      const statusSpan = statusContainer.createSpan({
        cls: "mx-status-value mx-status-running",
      });
      statusSpan.style.color = "var(--color-green)";
      statusSpan.style.fontWeight = "bold";
      statusSpan.textContent = "Running";

      const addressInfo = statusContainer.createDiv({
        cls: "mx-address-info",
      });
      addressInfo.style.fontSize = "var(--font-smallest)";
      addressInfo.style.marginTop = "4px";
      addressInfo.textContent = `${status.address || "localhost"}:${status.port}`;
    } else {
      // Server is enabled but not running
      const statusSpan = statusContainer.createSpan({
        cls: "mx-status-value mx-status-stopped",
      });
      statusSpan.style.color = "var(--color-red)";
      statusSpan.style.fontWeight = "bold";
      statusSpan.textContent = "Stopped";
    }
  };

  // Initial status update and UI synchronization
  updateStatus().then(() => updateServerToggle());

  // Add refresh button
  serverStatusSetting.addButton((button) =>
    button
      .setIcon("refresh-cw")
      .setTooltip("Refresh server status")
      .onClick(async () => {
        button.setDisabled(true);
        try {
          await updateStatus();
          await updateServerToggle();
        } finally {
          button.setDisabled(false);
        }
      }),
  );

  // Port configuration
  const portSetting = new Setting(containerEl).setName("Port Settings").setDesc(
    createFragment((el) => {
      el.appendText("Set the port to listen on. ");
      el.createEl("br");
      el.appendText(
        "Make sure browser companion extension is configured to use the same port",
      );
    }),
  );

  // Hide port setting initially (will be shown based on server state)
  portSetting.settingEl.style.display = "none";

  portSetting
    .addText((text) => {
      portInput = text;
      text
        .setPlaceholder(DEFAULT_PORT.toString())
        .then(setLimit(1024, 65535, 1));

      // Initialize the port value
      remoteServer.getPort().then((port) => {
        if (port > 0) {
          text.setValue(port.toString());
        } else {
          text.setValue("-1");
          text.setDisabled(true);
        }
      });
    })
    .addExtraButton((btn) => {
      resetButton = btn;
      return btn
        .setIcon("reset")
        .setTooltip("Reset to default port value")
        .onClick(async () => {
          // Reset to default port and apply it immediately
          portInput.setValue(DEFAULT_PORT.toString());
          await remoteServer.setPort(DEFAULT_PORT);
          const success = await remoteServer.listen();
          if (success) {
            new Notice("Reset to default port and restarted server");
          } else {
            new Notice("Reset port but failed to restart server");
          }
          await updateStatus();
        });
    });

  // Start/Stop Server button
  const applyPortSetting = new Setting(containerEl)
    .setName("Apply")
    .setDesc("Restarting the server to apply the port settings");

  // Hide apply setting initially (will be shown based on server state)
  applyPortSetting.settingEl.style.display = "none";

  applyPortSetting.addButton((button) =>
    button
      .setIcon("check")
      .setTooltip("Apply settings and restart server")
      .setCta()
      .onClick(async () => {
        button.setDisabled(true);

        try {
          // Apply the port from the input first
          const portValue = v.parse(nullablePort, Number(portInput.getValue()));

          await remoteServer.setPort(portValue < 0 ? -1 : portValue);
          const port = await remoteServer.getPort();

          if (port <= 0) {
            // If server is disabled, enable it first
            await remoteServer.setPort(DEFAULT_PORT);
            if (portInput) {
              portInput.setValue(DEFAULT_PORT.toString());
            }
            await updateServerToggle();
          }

          const success = await remoteServer.listen();
          if (success) {
            new Notice("Server (re)started successfully");
          } else {
            new Notice("Failed to start server");
          }
          await updateStatus();
        } catch (error: unknown) {
          const errorMessage =
            error instanceof Error ? error.message : String(error);
          new Notice(`Error starting server: ${errorMessage}`);
          console.error("Error starting server:", error);
        } finally {
          button.setDisabled(false);
        }
      }),
  );
  return stack.move();
}

function getRemoteServer(plugin: MxPlugin): RemoteServer | null {
  if (!plugin.rpc) {
    return null;
  }
  const rpc = plugin.rpc;
  return {
    getPort: async () => await rpc.getPref("remote-ws:port", -1),
    setPort: async (port: number) => await rpc.setPref("remote-ws:port", port),
    getStatus: async () => await rpc.getRemoteListeningAddress(),
    listen: async () => await rpc.listenRemotes(),
  };
}
