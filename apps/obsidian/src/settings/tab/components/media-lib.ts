import { Setting } from "obsidian";
import type { SettingsContext } from "./_ctx";

export function mediaLibSection(ctx: SettingsContext): Disposable {
  using stack = new DisposableStack();
  new Setting(ctx.containerEl).setHeading().setName("Media Library");
  new Setting(ctx.containerEl)
    .setName("Media folder path")
    .setDesc("Folder path for media library (relative to vault root)")
    .then((settings) => {
      const string = ctx.settings.string("media-lib.folder-path");
      settings.addText((text) => {
        text.setValue(string.value).onChange(string.set);
        stack.use(string.sub((s) => text.setValue(s)));
      });
    });
  return stack.move();
}
