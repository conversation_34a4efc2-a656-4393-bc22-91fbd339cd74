import type { NewPanelBehavior } from "@/settings/migrations/1";
import { assertNever } from "@std/assert/unstable-never";
import { Platform, Setting, type PaneType } from "obsidian";
import type { SettingAccessor } from "../accessor";
import type { NewPanelBehaviorSettings } from "../utils";
import { mapValues } from "@std/collections";
import type { SettingsContext } from "./_ctx";
import { naSymbol } from "@/settings/registry";

export function linkClickSection(ctx: SettingsContext): Disposable {
  using stack = new DisposableStack();
  new Setting(ctx.containerEl)
    .setName("Link behavior")
    .setHeading()
    .setDesc("Configure how links to media are opened");

  const clickAcc = clickBehaviorAccessor(ctx.settings, "link.click-behavior");
  const altclickAcc = clickBehaviorAccessor(
    ctx.settings,
    "link.altclick-behavior",
  );

  new Setting(ctx.containerEl)
    .setName("Click")
    .setDesc("Behavior when clicking on a link")
    .addDropdown((d) =>
      d
        .addOptions(mapValues(paneBehaviorConfig, (c) => c.description))
        .setValue(clickAcc.value)
        .onChange((val) => clickAcc.set(val))
        .then(() => stack.use(clickAcc.sub((s) => d.setValue(s)))),
    );
  new Setting(ctx.containerEl).setName("Alt click").then((s) => {
    const setup = (clickBehavior: NewPanelBehavior) => {
      if (clickBehavior === null || clickBehavior === false) {
        s.settingEl.style.display = "none";
        return;
      }
      s.settingEl.style.display = "";
      s.setDesc(`Behavior when ${resolvePaneShortcut(clickBehavior)}`);
    };
    setup(clickAcc.valueRaw);
    stack.use(clickAcc.subRaw((val) => setup(val)));
    s.addDropdown((d) =>
      d
        .addOptions(mapValues(paneBehaviorConfig, (c) => c.description))
        .setValue(altclickAcc.value)
        .onChange((val) => altclickAcc.set(val))
        .then(() => stack.use(altclickAcc.sub((s) => d.setValue(s)))),
    );
  });
  return stack.move();
}

function clickBehaviorAccessor(
  accessor: SettingAccessor,
  key: NewPanelBehaviorSettings,
): {
  value: string;
  valueRaw: NewPanelBehavior;
  set: (value: string) => void;
  sub: (handler: (now: string, prev: string | null) => void) => Disposable;
  subRaw: (
    handler: (now: NewPanelBehavior, prev: NewPanelBehavior | null) => void,
  ) => Disposable;
} {
  const { value, set, sub } = accessor.create(key);
  return {
    value: toNewPaneBehaviorKey(value),
    valueRaw: value,
    set: (value) => set(fromNewPaneBehaviorKey(value as NewPaneBehaviorKeys)),
    sub: (handler) =>
      sub((now, prev) =>
        handler(
          toNewPaneBehaviorKey(now),
          prev === naSymbol ? null : toNewPaneBehaviorKey(prev),
        ),
      ),
    subRaw: (handler) =>
      sub((now, prev) => handler(now, prev === naSymbol ? null : prev)),
  };
}

type NewPaneBehaviorKeys =
  | PaneType
  | "split-horizontal"
  | "replace"
  | "default";

const paneBehaviorConfig: Record<
  NewPaneBehaviorKeys,
  {
    value: NewPanelBehavior;
    key: NewPaneBehaviorKeys;
    description: string;
  }
> = {
  split: {
    value: "split",
    key: "split",
    description: "New pane on the right",
  },
  "split-horizontal": {
    value: "split-horizontal",
    key: "split-horizontal",
    description: "New pane on the bottom",
  },
  replace: {
    value: false,
    key: "replace",
    description: "In current pane",
  },
  default: {
    value: null,
    key: "default",
    description: "Default obsidian behavior",
  },
  window: {
    value: "window",
    key: "window",
    description: "New window",
  },
  tab: {
    value: "tab",
    key: "tab",
    description: "New tab",
  },
};

function toNewPaneBehaviorKey(val: NewPanelBehavior): NewPaneBehaviorKeys {
  const config = Object.values(paneBehaviorConfig).find((c) => c.value === val);
  if (!config) {
    throw new Error(`Unknown pane behavior: ${val}`);
  }
  return config.key;
}
function fromNewPaneBehaviorKey(key: NewPaneBehaviorKeys): NewPanelBehavior {
  const config = paneBehaviorConfig[key];
  if (!config) {
    throw new Error(`Unknown pane behavior key: ${key}`);
  }
  return config.value;
}

function resolvePaneShortcut(
  val: PaneType | "split-horizontal",
): string | null {
  switch (val) {
    case "split":
    case "split-horizontal":
      return Platform.isMacOS ? "click holding ⌘+⌥" : "click holding Ctrl+Alt";
    case "window":
      return Platform.isMacOS
        ? "click holding ⌘+⌥+⇧"
        : "click holding Ctrl+Alt+Shift";
    case "tab":
      return Platform.isMacOS
        ? "click holding ⌘ or middle-click"
        : "click holding Ctrl or middle-click";
    default:
      assertNever(val);
  }
}
