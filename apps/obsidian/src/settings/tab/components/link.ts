import { Setting } from "obsidian";
import type { SettingsContext } from "./_ctx";
import type { HostedPrefer } from "@/settings/migrations/1";

export function linkSection(
  ctx: SettingsContext,
  { enableBrowser }: { enableBrowser?: boolean } = {},
): Disposable {
  using stack = new DisposableStack();
  new Setting(ctx.containerEl).setHeading().setName("Link");

  new Setting(ctx.containerEl)
    .setName("Handle link to hosted media")
    .setDesc("Let media extended handle link to YouTube, Vimeo, bilibili, etc")
    .then((settings) => {
      const acc = ctx.settings.boolean("link.handle-hosted");
      settings.addToggle((toggle) => {
        toggle.setValue(acc.value).onChange(acc.set);
        stack.use(acc.sub((s) => toggle.setValue(s)));
      });
    });
  new Setting(ctx.containerEl)
    .setName("Handle link to remote media file")
    .setDesc(
      "Let media extended handle link to remote media file, like http://example.com/path/to/video.mp4",
    )
    .then((settings) => {
      const acc = ctx.settings.boolean("link.handle-direct-url");
      settings.addToggle((toggle) => {
        toggle.setValue(acc.value).onChange(acc.set);
        stack.use(acc.sub((s) => toggle.setValue(s)));
      });
    });
  enableBrowser &&
    new Setting(ctx.containerEl)
      .setName("Open hosted media in...")
      .setDesc(
        createFragment((f) => {
          f.appendText(
            "For hosted media supported in both obsidian player and browser, including YouTube and Viemo, choose your preferred way to open them.",
          );
          f.createEl("br");
          f.appendText(
            "Choose browser to open with browser companion extension to access more features.",
          );
        }),
      )
      .then((settings) => {
        const acc = ctx.settings.string("link.hosted-prefer");
        settings.addDropdown((dp) => {
          dp.addOptions({
            browser: "Browser",
            iframe: "Local player",
          } satisfies Record<HostedPrefer, string>);
          dp.setValue(acc.value).onChange(acc.set);
          stack.use(acc.sub((s) => dp.setValue(s)));
        });
      });

  return stack.move();
}
