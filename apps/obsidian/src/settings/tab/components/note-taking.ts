import { Setting } from "obsidian";
import type { SettingsContext } from "./_ctx";
import { setLimit } from "../utils";
import type { InsertAt } from "@/settings/migrations/1";

export function noteTakingSection(ctx: SettingsContext): Disposable {
  using stack = new DisposableStack();

  const { containerEl } = ctx;

  new Setting(containerEl).setHeading().setName("Note taking");

  // Insert Location
  const insertAt = ctx.settings.create("note.template.insert-at");
  new Setting(containerEl)
    .setDesc("Configure where timestamps and screenshots are inserted")
    .setName("Insert location")
    .addDropdown((d) => {
      const options: Record<InsertAt, string> = {
        "before-cursor": "Latest content on top (Before cursor)",
        "after-cursor": "Latest content at end (After cursor)",
      };
      d.addOptions(options)
        .setValue(insertAt.value)
        .onChange((val) =>
          insertAt.set(val as "before-cursor" | "after-cursor"),
        )
        .then(() => stack.use(insertAt.sub((val) => d.setValue(val))));
    });

  // -- Timestamp --
  const timestampTemplate = ctx.settings.string("note.template.timestamp");
  new Setting(containerEl)
    .setName("Timestamp template")
    .setDesc(
      "The template used to insert timestamps. Supported placeholders: {{TIMESTAMP}}",
    )
    .addTextArea((text) => {
      text.setValue(timestampTemplate.value).onChange(timestampTemplate.set);
      text.inputEl.rows = 5;
      text.inputEl.cols = 40;
      stack.use(timestampTemplate.sub((val) => text.setValue(val)));
    });

  const timestampOffset = ctx.settings.number("note.template.timestamp-offset");
  const timestampOffsetInput = ctx.settings.inputNumber(
    "note.template.timestamp-offset",
  );
  new Setting(containerEl)
    .setName("Timestamp offset")
    .setDesc("Offset in seconds to add to the timestamp")
    .addSlider((slide) =>
      slide
        .setLimits(-10, 10, 0.01)
        .setValue(timestampOffset.value)
        .setDynamicTooltip()
        .onChange(timestampOffset.set)
        .then((slide) => {
          stack.use(timestampOffset.sub((s) => slide.setValue(s)));
        }),
    )
    .addText((text) =>
      text
        .setValue(timestampOffsetInput.value)
        .onChange(timestampOffsetInput.set)
        .then(setLimit(-10, 10, 0.01))
        .then((input) => {
          stack.use(timestampOffsetInput.sub((s) => input.setValue(s)));
        }),
    )
    .then((s) => s.controlEl.appendText("s"));

  // -- Screenshot -
  const screenshotEmbedTemplate = ctx.settings.string(
    "note.template.screenshot-embed",
  );
  new Setting(containerEl)
    .setName("Screenshot linktext template")
    .setDesc(
      "The template used to create screenshot linktext. Supported placeholders: {{DURATION}}, {{TITLE}}. Remove `|50` suffix to embed image in full size.",
    )
    .addTextArea((text) => {
      text
        .setValue(screenshotEmbedTemplate.value)
        .onChange(screenshotEmbedTemplate.set);
      text.inputEl.rows = 5;
      text.inputEl.cols = 40;
      stack.use(screenshotEmbedTemplate.sub((val) => text.setValue(val)));
    });

  const screenshotTemplate = ctx.settings.string("note.template.screenshot");
  new Setting(containerEl)
    .setName("Screenshot template")
    .setDesc(
      "The template used to insert screenshot. Supported placeholders: {{TIMESTAMP}}, {{SCREENSHOT}} (add `!` prefix to insert as image embed).",
    )
    .addTextArea((text) => {
      text.setValue(screenshotTemplate.value).onChange(screenshotTemplate.set);
      text.inputEl.rows = 5;
      text.inputEl.cols = 40;
      stack.use(screenshotTemplate.sub((val) => text.setValue(val)));
    });

  return stack.move();
}
