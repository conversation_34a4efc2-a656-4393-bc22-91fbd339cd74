import type { TextComponent } from "obsidian";
import type { LatestPluginSettings } from "../registry/atom";
import type { NewPanelBehavior } from "../migrations/1";

export function setLimit(min: number, max: number, step: number) {
  return (input: TextComponent) => {
    input.inputEl.type = "number";
    input.inputEl.style.textAlign = "center";
    input.inputEl.min = min.toString();
    input.inputEl.max = max.toString();
    input.inputEl.step = step.toString();
  };
}

export type StringSettings = ExtractTypedKeys<LatestPluginSettings, string>;
export type OptionalStringSettings = ExtractTypedKeys<
  LatestPluginSettings,
  string | undefined
>;
export type NumberSettings = ExtractTypedKeys<LatestPluginSettings, number>;
export type BooleanSettings = ExtractTypedKeys<LatestPluginSettings, boolean>;
export type NewPanelBehaviorSettings = ExtractTypedKeys<
  LatestPluginSettings,
  NewPanelBehavior
>;
type ExtractTypedKeys<R, T> = Exclude<
  {
    [K in keyof R]: R[K] extends T ? K : never;
  }[keyof R],
  "__VERSION__" | undefined
>;
