import type MxPlugin from "@/mx-main";
import { type App, PluginSettingTab, Setting } from "obsidian";
import { remoteServerSection } from "./components/remote-ws";
import { SettingAccessor } from "./accessor";
import { screenshotFormat } from "./components/screenshot";
import { linkSection } from "./components/link";
import { textTrackSection } from "./components/text-track";
import type { SettingsContext } from "./components/_ctx";
import { screenshotDir } from "./components/attachment";
import { mediaLibSection } from "./components/media-lib";
import { playbackSection } from "./components/playback";
import { linkClickSection } from "./components/link-click";
import { noteTakingSection } from "./components/note-taking";
import { authSection } from "./components/auth";
import { isBrowserFlagEnabled } from "./browser-test";
import "./styles.css";

export class MxSettingTab extends PluginSettingTab {
  plugin: MxPlugin;
  stack: DisposableStack | null = null;

  constructor(app: App, plugin: MxPlugin) {
    super(app, plugin);
    this.plugin = plugin;
  }

  hide(): void {
    this.stack?.dispose();
    this.containerEl.empty();
  }

  display(): void {
    this.containerEl.empty();

    const browserFlagEnabled = isBrowserFlagEnabled();

    this.plugin.settings.loaded.then(() => {
      using stack = new DisposableStack();

      const ctx: SettingsContext = {
        containerEl: this.containerEl,
        settings: new SettingAccessor(this.plugin.settings),
      };

      // Add auth section at the top
      stack.use(authSection({ ...ctx, plugin: this.plugin }));

      stack.use(playbackSection(ctx));
      stack.use(textTrackSection(ctx));

      stack.use(
        linkSection(ctx, {
          enableBrowser: browserFlagEnabled,
        }),
      );
      stack.use(linkClickSection(ctx));

      stack.use(noteTakingSection(ctx));

      new Setting(this.containerEl).setHeading().setName("Screenshot");
      stack.use(screenshotDir(ctx));
      stack.use(screenshotFormat(ctx));

      // Media Library section
      stack.use(mediaLibSection(ctx));

      browserFlagEnabled &&
        stack.use(
          remoteServerSection({
            containerEl: this.containerEl,
            plugin: this.plugin,
          }),
        );
      this.stack = stack.move();
    });
  }
}
