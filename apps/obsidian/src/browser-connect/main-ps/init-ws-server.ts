import {
  createServer,
  type Server,
  type IncomingMessage,
  type ServerResponse,
} from "node:http";
import { WebSocketServer } from "ws";
import { WebSocket as WebSocketPolyfill } from "ws";
import cors from "cors";
import type { Duplex } from "node:stream";
import { deadline } from "@std/async";

// polyfill WebSocket for node 20
(globalThis as any).WebSocket ??= WebSocketPolyfill;

export interface WebSocketServerConfig {
  allowedOrigins: Set<string>;
  onConnection: WebSocketConnectionCallback;
  onRequest?: (url: URL, req: IncomingMessage, res: ServerResponse) => void;
}

export type WebSocketConnectionCallback = (
  ws: WebSocket,
  parts: WebSocketProfile,
  request: IncomingMessage,
) => void;

export default class WebSocketServerInstance implements Disposable {
  // #ws: WebSocketServer;
  #http: Server;
  #disposables: DisposableStack;
  #onConnection: WebSocketConnectionCallback;

  get address() {
    if (this.#http.listening) {
      const address = this.#http.address();
      if (typeof address === "string") {
        throw new Error("Server is listening on a pipe or UNIX domain socket");
      }
      return address;
    }
    return null;
  }

  constructor(config: WebSocketServerConfig) {
    using stack = new DisposableStack();
    const ws = new WebSocketServer({ noServer: true });
    stack.defer(() => ws.close());
    const http = createServer();
    stack.defer(() => http.close());

    // Add CORS middleware
    const corsMiddleware = cors({ origin: Array.from(config.allowedOrigins) });

    // Handle WebSocket connections
    this.#onConnection = config.onConnection;

    const handleUpgrade = (
      request: IncomingMessage,
      socket: Duplex,
      head: Buffer,
    ) => {
      const profile = parseWsUrl(request);
      if (profile) {
        if (
          request.headers.origin &&
          config.allowedOrigins.has(request.headers.origin)
        ) {
          ws.handleUpgrade(request, socket, head, (ws) => {
            this.#onConnection(ws as any, profile, request);
          });
        } else {
          socket.write("HTTP/1.1 403 Forbidden\r\n\r\n");
          socket.destroy();
        }
      } else {
        socket.write("HTTP/1.1 404 Not Found\r\n\r\n");
        socket.destroy();
      }
    };
    http.on("upgrade", handleUpgrade);
    stack.defer(() => http.off("upgrade", handleUpgrade));

    const handleRequest = (req: IncomingMessage, res: ServerResponse) => {
      // console.log("request", req.url, req.headers.origin, req.method);
      corsMiddleware(req, res, () => {
        if (!req.url) {
          res.writeHead(400);
          res.end();
          return;
        }
        if (!config.onRequest) {
          res.writeHead(404);
          res.end();
          return;
        }
        const url = new URL(req.url, `http://${req.headers.host}`);
        try {
          config.onRequest(url, req, res);
        } catch (e) {
          console.error("[server error]", e);
          res.writeHead(500);
          res.end();
        }
      });
    };
    http.on("request", handleRequest);
    stack.defer(() => http.off("request", handleRequest));

    this.#disposables = stack.move();
    // this.#ws = ws;
    this.#http = http;
  }

  async listen(port: number): Promise<void> {
    if (this.#http.listening) {
      const address = this.#http.address();
      // For a server listening on a pipe or UNIX domain socket, the name is returned
      // as a string.
      if (typeof address === "string" || address?.port !== port) {
        console.log("Server is already running, closing", address);
        this.#http.close();
      } else {
        console.log("Server is already running, skipping");
        return;
      }
    }

    using stack = new DisposableStack();
    await deadline(
      new Promise<void>((resolve, reject) => {
        const onOpen = () => resolve();
        const onError = (e: Error) => reject(e);
        this.#http.listen(port, "localhost", onOpen);
        stack.defer(() => this.#http.off("open", onOpen));
        this.#http.on("error", onError);
        stack.defer(() => this.#http.off("error", onError));
      }),
      10e3,
    );
  }
  [Symbol.dispose]() {
    this.#disposables.dispose();
  }
}

// export default function initServer(
//   config: WebSocketServerConfig,
//   onConnection: WebSocketServerOnConnection,
//   onRequest?: (url: URL, req: IncomingMessage, res: ServerResponse) => void,
// ): WebSocketServerInstance {
//   const server = {
//     ws: new WebSocketServer({ noServer: true }),
//     http: createServer(),
//     listen(port: number) {
//       this.http.listen(port, () => {
//         console.log(`Server is running on port ${port}`);
//       });
//     },
//     [Symbol.dispose]() {
//       this.ws.close();
//       this.http.close();
//     },
//   };

//   return server;
// }

export interface WebSocketProfile {
  clientId: string;
  name: string | null;
  browser: string | null;
}

function parseWsUrl(request: IncomingMessage): WebSocketProfile | null {
  const url = new URL(`ws://localhost${request.url}`);
  if (!url.pathname.startsWith("/ws/")) {
    return null;
  }
  const [, _ws, clientId, rest] = url.pathname.split("/");
  if (!clientId || rest) {
    return null;
  }
  return {
    clientId,
    name: url.searchParams.get("name"),
    browser: url.searchParams.get("browser"),
  };
}
