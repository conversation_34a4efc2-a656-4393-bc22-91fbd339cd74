import LifeCycle from "@mx/shared/utils/lifecycle";

import {
  CONN_SYMBOL,
  CONN_API_VERSION,
  CHANNEL_CONNECT,
  type InitMessage,
  CHANNEL_DISCONNECT,
} from "@/protocol/const";

import type { WebSocketConnectionCallback } from "./init-ws-server";
import WebSocketServerInstance from "./init-ws-server";
import {
  ConnectionManager,
  RemoteConnection,
  VaultConnection,
  type Connections,
  type IPCContext,
} from "./rpc";
import { ipcMain } from "electron";
import { IpcMainEventRegistry } from "@mx/shared/rpc/electron-main.registry";
import { preferences } from "@/main-prefs";
import { nullablePort } from "@/lib/validate/port";
import * as v from "valibot";

// TBD: modify for production
const ALLOWED_ORIGINS = [
  "http://localhost:3000",
  "chrome-extension://palfkdjannehhpmigeljbanlmfhibbbh",
];

export default class BrowserConnector extends LifeCycle {
  #server: WebSocketServerInstance | null = null;
  #ipcEvtRegistry = new IpcMainEventRegistry(ipcMain);
  connections: Connections = {
    ws: new ConnectionManager(),
    renderer: new ConnectionManager(),
  };

  static async ensureServer(scriptMd5: string): Promise<BrowserConnector> {
    const global = globalThis as {
      [CONN_SYMBOL]?: Map<string, BrowserConnector>;
    };
    global[CONN_SYMBOL] ??= new Map();
    const connectorStore = global[CONN_SYMBOL];
    if (!(connectorStore instanceof Map)) {
      throw new Error("Unexpected global type, restart Obsidian");
    }
    const versionKey = `${CONN_API_VERSION}-${scriptMd5}`;
    if (connectorStore.has(versionKey)) {
      return connectorStore.get(versionKey)!;
    }
    if (connectorStore.size > 1) {
      if (process.env.NODE_ENV === "development") {
        console.warn("Reloading BrowserConnector", {
          current: versionKey,
          prev: Array.from(connectorStore.keys()).join(","),
        });
        await Promise.all(
          [...connectorStore.values()].map((conn) => conn.unload()),
        ).catch(console.error);
      } else {
        throw new Error(
          "Conflict BrowserConnector, make sure media-extended across multiple vaults are up to date and restart Obsidian",
        );
      }
    }
    const instance = new BrowserConnector();
    connectorStore.set(versionKey, instance);
    return instance;
  }

  async onload(stack: AsyncDisposableStack): Promise<void> {
    await super.onload(stack);
    await preferences.load();

    // disabled by default
    const allowedOrigins = new Set(ALLOWED_ORIGINS);
    this.#server = stack.use(
      new WebSocketServerInstance({
        allowedOrigins,
        onConnection: this.#handleWsConnection,
      }),
    );
    stack.use(this.connections.renderer);
    stack.use(this.connections.ws);
    stack.use(this.#ipcEvtRegistry).load();

    ipcMain.handle(CHANNEL_CONNECT, this.#handleRendererConnection);
    stack.defer(() => {
      ipcMain.off(CHANNEL_CONNECT, this.#handleRendererConnection);
    });
    ipcMain.on(CHANNEL_DISCONNECT, this.#handleRendererDisconnect);
    stack.defer(() => {
      ipcMain.off(CHANNEL_DISCONNECT, this.#handleRendererDisconnect);
    });

    this.listenRemotes();
  }

  async listenRemotes() {
    if (!this.#server) {
      throw new Error("Server not initialized");
    }
    const portRaw = preferences.get("remote-ws:port", -1);
    const port = v.parse(nullablePort, portRaw);
    if (port < 0) {
      console.log("Remote connection disabled");
      return false;
    }
    await this.#server.listen(port).catch((e) => {
      console.error("Failed to listen on port", port, e);
      throw e;
    });
    console.log("Remote connection started, listening on port", port);
    return true;
  }
  get #ctx(): IPCContext {
    return {
      connections: this.connections,
      ipcEventRegistry: this.#ipcEvtRegistry,
      listenRemotes: async () => await this.listenRemotes(),
      getRemoteListeningAddress: () => this.#server?.address ?? null,
    };
  }

  #handleRendererConnection = (
    event: Electron.IpcMainInvokeEvent,
    message: InitMessage,
  ) => {
    const webContents = event.sender;
    const connection = new VaultConnection(webContents, this.#ctx);
    this.connections.renderer.add(webContents.id, connection);
    return true;
  };
  #handleRendererDisconnect = (event: Electron.IpcMainEvent) => {
    console.log("vault disconnected", event.sender.id);
    this.connections.renderer.delete(event.sender.id);
  };

  #handleWsConnection: WebSocketConnectionCallback = (ws, profile) => {
    if (this.connections.ws.has(profile.clientId)) {
      console.log("client id already used", profile.clientId);
      ws.close(4403, "Client ID already used");
      return;
    }
    const connection = new RemoteConnection({ ws, profile });
    this.connections.ws.add(profile.clientId, connection);
    ws.addEventListener(
      "close",
      () => {
        console.log("remote browser disconnected", profile.clientId);
        this.connections.ws.delete(profile.clientId);
      },
      { once: true },
    );
    console.log("remote browser connected", profile.clientId);
  };
}
