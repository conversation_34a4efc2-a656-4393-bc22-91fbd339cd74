import {
  type Fn as FnPlayback,
  name as fnNamePlayback,
} from "@mx/ext-fn/app2ext/playback";
import {
  type Fn as FnScreenshot,
  name as fnNameScreenshot,
} from "@mx/ext-fn/app2ext/screenshot";
import {
  type Fn as Fn<PERSON>ink<PERSON>pen,
  name as fnNameLinkOpen,
} from "@mx/ext-fn/app2ext/link-open";
import WebSocketClientAdapter from "@mx/shared/rpc/adapter/ws";
import { defineConsumer } from "@mx/shared/rpc";
import type { RemoteFn as _RemoteFn } from "@mx/shared/rpc";
import type { WebSocketProfile } from "../init-ws-server";

type RemoteFn<T extends (...args: any[]) => any> = _RemoteFn<
  T,
  Record<string, never>
>;

export class RemoteConnection implements Disposable {
  #profile: WebSocketProfile;
  #disposables: DisposableStack;
  constructor({ ws, profile }: { ws: WebSocket; profile: WebSocketProfile }) {
    this.#profile = profile;
    using stack = new DisposableStack();
    stack.defer(() => ws.close());
    const adapter = new WebSocketClientAdapter(ws);
    const consumer = defineConsumer(adapter);
    this.#disposables = stack.move();
    this.controlPlayback = consumer.createRemoteFn<FnPlayback>(fnNamePlayback);
    this.captureScreenshot =
      consumer.createRemoteFn<FnScreenshot>(fnNameScreenshot);
    this.linkOpen = consumer.createRemoteFn<FnLinkOpen>(fnNameLinkOpen);
  }

  get profile() {
    return { ...this.#profile };
  }
  controlPlayback: RemoteFn<FnPlayback>;
  captureScreenshot: RemoteFn<FnScreenshot>;
  linkOpen: RemoteFn<FnLinkOpen>;

  [Symbol.dispose]() {
    this.#disposables.dispose();
  }
}
