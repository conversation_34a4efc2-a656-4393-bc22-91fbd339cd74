export class ConnectionManager<
  Conn extends Disposable,
  Key extends string | number,
> {
  #connections = new Map<Key, Conn>();

  add(id: Key, conn: Conn) {
    this.#connections.set(id, conn);
  }

  get(id: Key) {
    return this.#connections.get(id);
  }

  has(id: Key) {
    return this.#connections.has(id);
  }

  pickFirst() {
    return this.#connections.values().next().value;
  }

  delete(id: Key) {
    const conn = this.#connections.get(id);
    if (!conn) return false;
    this.#connections.delete(id);
    conn[Symbol.dispose]();
    return true;
  }

  async [Symbol.asyncDispose]() {
    for (const conn of this.#connections.values()) {
      conn[Symbol.dispose]();
    }
    this.#connections.clear();
  }
}
