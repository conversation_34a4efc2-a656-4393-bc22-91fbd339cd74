import type { IpcMainEventRegistry } from "@mx/shared/rpc/electron-main.registry";
import type { ConnectionManager } from "./conn-manager";
import type { RemoteConnection } from "../remote-ws";
import type { VaultConnection } from "../vault";
import type { AddressInfo } from "node:net";

export interface IPCContext {
  connections: Connections;
  ipcEventRegistry: IpcMainEventRegistry;
  listenRemotes: () => Promise<boolean>;
  getRemoteListeningAddress: () => AddressInfo | null;
}

export interface Connections {
  ws: ConnectionManager<RemoteConnection, string>;
  renderer: ConnectionManager<VaultConnection, number>;
}
