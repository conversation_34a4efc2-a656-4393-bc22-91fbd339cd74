import ElectronMainAdapter from "@mx/shared/rpc/adapter/electron-main";

import {
  type Fn as FnPlayback,
  name as fnNamePlayback,
} from "@mx/ext-fn/app2ext/playback";
import {
  type Fn as FnScreenshot,
  name as fnNameScreenshot,
} from "@mx/ext-fn/app2ext/screenshot";
import {
  type Fn as FnLink<PERSON>pen,
  name as fnNameLinkOpen,
} from "@mx/ext-fn/app2ext/link-open";

import {
  type Fn as FnGetPref,
  name as fnNameGetPref,
} from "@/browser-connect/fn-def/get-pref";
import {
  type Fn as FnSetPref,
  name as fnNameSetPref,
} from "@/browser-connect/fn-def/set-pref";

import {
  type Fn as FnWsListen,
  name as fnNameWsListen,
} from "@/browser-connect/fn-def/listen-remotes";
import {
  type Fn as FnListenRemotesStatus,
  name as fnNameListenRemotesStatus,
} from "@/browser-connect/fn-def/listen-remotes-status";

import { CodeError, Provider } from "@mx/shared/rpc";
import type { IPCContext } from "./base/ctx";
import { preferences } from "@/main-prefs";

export class VaultConnection implements Disposable {
  #disposables: DisposableStack;
  constructor(webContents: Electron.WebContents, ctx: IPCContext) {
    using stack = new DisposableStack();
    const adapter = new ElectronMainAdapter({
      webContents,
      ipc: ctx.ipcEventRegistry,
    });
    const provider = stack.use(Provider.init(adapter));
    provider.addFnHandler<FnPlayback>({
      fn: async (target, action) => {
        if (!ctx.getRemoteListeningAddress()) {
          throw new CodeError("browser-na");
        }
        // pick first available remote browser
        const ws = ctx.connections.ws.pickFirst();
        if (!ws) {
          throw new CodeError("no-browser-session");
        }
        return await ws.controlPlayback(target, action);
      },
      name: fnNamePlayback,
    });
    provider.addFnHandler<FnScreenshot>({
      fn: async (target, action) => {
        if (!ctx.getRemoteListeningAddress()) {
          throw new CodeError("browser-na");
        }
        const ws = ctx.connections.ws.pickFirst();
        if (!ws) {
          throw new CodeError("no-browser-session");
        }
        return await ws.captureScreenshot(target, action);
      },
      name: fnNameScreenshot,
    });
    provider.addFnHandler<FnLinkOpen>({
      fn: async (target, action) => {
        if (!ctx.getRemoteListeningAddress()) {
          throw new CodeError("browser-na");
        }
        const ws = ctx.connections.ws.pickFirst();
        if (!ws) {
          throw new CodeError("no-browser-session");
        }
        return await ws.linkOpen(target, action);
      },
      name: fnNameLinkOpen,
    });
    provider.addFnHandler<FnGetPref>({
      fn: async (key, defaultValue) => {
        return preferences.get(key, defaultValue);
      },
      name: fnNameGetPref,
    });
    provider.addFnHandler<FnSetPref>({
      fn: async (key, value) => {
        await preferences.set(key, value);
      },
      name: fnNameSetPref,
    });
    provider.addFnHandler<FnWsListen>({
      fn: async () => {
        return await ctx.listenRemotes();
      },
      name: fnNameWsListen,
    });
    provider.addFnHandler<FnListenRemotesStatus>({
      fn: () => ctx.getRemoteListeningAddress(),
      name: fnNameListenRemotesStatus,
    });

    const onDestroyed = () => ctx.connections.renderer.delete(webContents.id);
    webContents.on("destroyed", onDestroyed);
    stack.defer(() => {
      webContents.off("destroyed", onDestroyed);
    });
    this.#disposables = stack.move();
  }

  [Symbol.dispose]() {
    this.#disposables.dispose();
  }
}
