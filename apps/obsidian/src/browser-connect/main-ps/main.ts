import "@mx/shared/polyfills/disposable";

import { webContents } from "electron";
import BrowserConnector from "./connector";
import { CHANNEL_READY, CONN_API_VERSION } from "@/protocol/const";

export const init = async (scriptMd5: string) => {
  const wsServer = await BrowserConnector.ensureServer(scriptMd5);
  await wsServer.load();
  console.log("connector server ready");

  // Broadcast to all webContents
  for (const contents of webContents.getAllWebContents()) {
    contents.send(CHANNEL_READY, { version: CONN_API_VERSION });
  }
};
