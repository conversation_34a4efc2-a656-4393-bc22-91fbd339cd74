import {
  type Fn as FnPlayback,
  name as fnNamePlayback,
} from "@mx/ext-fn/app2ext/playback";
import {
  type Fn as FnScreenshot,
  name as fnNameScreenshot,
} from "@mx/ext-fn/app2ext/screenshot";
import {
  type Fn as Fn<PERSON>ink<PERSON>pen,
  name as fnNameLinkOpen,
} from "@mx/ext-fn/app2ext/link-open";

import {
  type Fn as FnGetPref,
  name as fnNameGetPref,
} from "@/browser-connect/fn-def/get-pref";
import {
  type Fn as FnSetPref,
  name as fnNameSetPref,
} from "@/browser-connect/fn-def/set-pref";
import {
  type Fn as FnWsListen,
  name as fnNameWsListen,
} from "@/browser-connect/fn-def/listen-remotes";
import {
  type Fn as FnListenRemotesStatus,
  name as fnNameListenRemotesStatus,
} from "@/browser-connect/fn-def/listen-remotes-status";

import { defineConsumer } from "@mx/shared/rpc";
import ElectronRendererAdapter from "@mx/shared/rpc/adapter/electron-renderer";
import type { IpcRenderer } from "electron";

export default function initRendererConnection(ipcRenderer: IpcRenderer) {
  using stack = new DisposableStack();
  const adapter = new ElectronRendererAdapter(ipcRenderer);
  const consumer = defineConsumer(adapter);
  const disposables = stack.move();
  return {
    controlPlayback: consumer.createRemoteFn<FnPlayback>(fnNamePlayback),
    captureScreenshot: consumer.createRemoteFn<FnScreenshot>(fnNameScreenshot),
    linkOpen: consumer.createRemoteFn<FnLinkOpen>(fnNameLinkOpen),
    getPref: consumer.createRemoteFn<FnGetPref>(fnNameGetPref) as FnGetPref,
    setPref: consumer.createRemoteFn<FnSetPref>(fnNameSetPref),
    listenRemotes: consumer.createRemoteFn<FnWsListen>(fnNameWsListen),
    getRemoteListeningAddress: consumer.createRemoteFn<FnListenRemotesStatus>(
      fnNameListenRemotesStatus,
    ),
    [Symbol.dispose]: () => disposables.dispose(),
  };
}

export type RendererConnection = ReturnType<typeof initRendererConnection>;
