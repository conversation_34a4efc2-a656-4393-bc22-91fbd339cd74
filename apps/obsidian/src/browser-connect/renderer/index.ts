import { type App, Component, Platform } from "obsidian";
import hack from "inline:../main-ps/main.ts";
import {
  CHANNEL_CONNECT,
  CHANNEL_DISCONNECT,
  CHANNEL_READY,
  CONN_API_VERSION,
  type InitMessage,
} from "@/protocol/const";
import initRendererConnection, { type RendererConnection } from "./init-client";
import {
  requireCrypto,
  requireElectron,
  requireElectronRemote,
  requireFs,
  requirePath,
} from "@/lib/node";

declare module "obsidian" {
  interface App {
    appId: string;
  }
}

export default class BrowserConnect extends Component {
  #connected: boolean | null = null;
  get connected() {
    return this.#connected;
  }
  app: App;
  constructor(app: App) {
    super();
    this.app = app;
  }
  instance: RendererConnection | null = null;
  onload() {
    super.onload();
    if (!Platform.isDesktopApp) {
      throw new Error("BrowserConnect is only available on desktop");
    }
    const { ipcRenderer } = requireElectron();
    const { createHash } = requireCrypto();
    this.#connected = false;
    this.register(() => {
      this.#connected = null;
    });
    this.instance = initRendererConnection(ipcRenderer);
    this.register(() => {
      this.instance?.[Symbol.dispose]();
    });

    console.log("waiting for connector ready");
    ipcRenderer.once(CHANNEL_READY, async (_, message: { version: string }) => {
      console.log("connector ready", message);
      if (message.version !== CONN_API_VERSION) {
        throw new Error(`Unsupported API version: ${message.version}`);
      }
      const hashedAppId = createHash("sha256")
        .update(this.app.appId)
        .digest("hex");
      console.log("connecting", hashedAppId);
      const connected = await ipcRenderer.invoke(CHANNEL_CONNECT, {
        name: this.app.vault.getName(),
      } satisfies InitMessage);
      if (connected) {
        this.#connected = true;
        console.log("connected to main process");
      }
    });
    this.register(() => {
      ipcRenderer.send(CHANNEL_DISCONNECT);
    });
    this.#registerHack();
  }

  async #createHackScript() {
    const tmpDir = requireElectronRemote().app.getPath("temp");
    const { createHash } = requireCrypto();
    const scriptMd5 = createHash("md5").update(hack).digest("hex");
    const { join } = requirePath();
    const { writeFile, unlink } = requireFs();
    const scriptPath = join(tmpDir, `mx-hack-${scriptMd5}.js`);
    await writeFile(scriptPath, hack);
    return {
      path: scriptPath,
      md5: scriptMd5,
      async [Symbol.asyncDispose]() {
        await unlink(scriptPath).catch(console.error);
      },
    };
  }

  async #registerHack() {
    await using script = await this.#createHackScript();
    const { init } = requireElectronRemote().require(script.path);
    await init(script.md5);
  }
}
