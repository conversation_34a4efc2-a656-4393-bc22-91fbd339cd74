// preferences.ts

import { app } from "electron";
import { join } from "node:path";
import { readFile, writeFile, mkdir } from "node:fs/promises";

// Type for preference options
export interface PreferenceOptions {
  // The filename to store preferences (default: 'mx-pref.json')
  filename?: string;
  // Default preferences to use if no file exists
  defaults?: Record<string, any>;
}

// Main preference manager class
export class PreferenceManager<Pref extends Record<string, any>> {
  #filePath: string;
  #preferences: Record<string, any>;
  #initialized = false;
  #defaults: Record<string, any>;
  #saveInProgress = false;
  #saveQueued = false;

  constructor(options: PreferenceOptions = {}) {
    // Use provided filename or default to 'mx-pref.json'
    const filename = options.filename || "mx-pref.json";
    // Get userData path from Electron app
    const userDataPath = app.getPath("userData");
    // Create full path to preferences file
    this.#filePath = join(userDataPath, filename);
    // Store default preferences
    this.#defaults = options.defaults || {};
    // Initialize preferences with defaults
    this.#preferences = { ...this.#defaults };
  }

  // Load preferences from disk
  public async load(): Promise<Record<string, any>> {
    try {
      // Try to read and parse file directly
      const data = await readFile(this.#filePath, "utf8");
      this.#preferences = { ...this.#defaults, ...JSON.parse(data) };
      this.#initialized = true;

      return this.#preferences;
    } catch (error) {
      // Handle file not existing or other read errors
      if ((error as NodeJS.ErrnoException).code === "ENOENT") {
        // File doesn't exist, use defaults and save them
        if (!this.#initialized) {
          await this.save();
          this.#initialized = true;
        }
      } else {
        // Log other unexpected errors but continue with defaults
        console.error("Error loading preferences:", error);
      }

      return this.#preferences;
    }
  }

  // Save preferences to disk with protection against duplicate invocations
  public async save(): Promise<void> {
    // If save is already in progress, mark as queued and return
    if (this.#saveInProgress) {
      this.#saveQueued = true;
      return;
    }

    try {
      this.#saveInProgress = true;

      // Write preferences to file, handling directory creation if needed
      try {
        await writeFile(
          this.#filePath,
          JSON.stringify(this.#preferences, null, 2),
          "utf8",
        );
      } catch (error) {
        // If directory doesn't exist, create it and retry
        if ((error as NodeJS.ErrnoException).code === "ENOENT") {
          await mkdir(app.getPath("userData"), { recursive: true });
          await writeFile(
            this.#filePath,
            JSON.stringify(this.#preferences, null, 2),
            "utf8",
          );
        } else {
          // Re-throw other errors
          throw error;
        }
      }

      // Handle queued save requests
      if (this.#saveQueued) {
        this.#saveQueued = false;
        this.#saveInProgress = false;
        // Process the queued save with the latest data
        await this.save();
      } else {
        this.#saveInProgress = false;
      }
    } catch (error) {
      this.#saveInProgress = false;
      this.#saveQueued = false;
      console.error("Error saving preferences:", error);
      throw error;
    }
  }

  // Get a preference value
  public get<T extends Extract<keyof Pref, string>>(
    key: T,
    defaultValue: Pref[T],
  ): Pref[T] {
    if (key in this.#preferences) {
      return this.#preferences[key];
    }
    return defaultValue;
  }

  // Set a preference value
  public async set<T extends Extract<keyof Pref, string>>(
    key: T,
    value: Pref[T],
  ): Promise<void> {
    // Update in memory
    this.#preferences[key] = value;
    // Save to disk
    await this.save();
  }

  // Get all preferences
  public getAll(): Record<string, any> {
    return { ...this.#preferences };
  }

  // Set multiple preferences at once
  public async setMultiple(values: Record<string, any>): Promise<void> {
    // Update in memory
    this.#preferences = { ...this.#preferences, ...values };
    // Save to disk
    await this.save();
  }

  // Reset preferences to defaults
  public async reset(): Promise<void> {
    this.#preferences = { ...this.#defaults };
    await this.save();
  }

  // Delete a preference
  public async delete(key: string): Promise<void> {
    if (key in this.#preferences) {
      delete this.#preferences[key];
      await this.save();
    }
  }
}
