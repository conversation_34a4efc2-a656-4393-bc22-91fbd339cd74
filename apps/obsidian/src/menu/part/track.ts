import type { Menu } from "obsidian";
import type { PlayerContext } from "../def";
import type MediaExtended from "@/mx-main";
import { openTrack } from "@/track-view/open";
import { formatLabel } from "@/transcript/load-media-tracks";

export function trackMenu(
  menu: Menu,
  ctx: PlayerContext & { plugin: MediaExtended },
) {
  if (!ctx.player) return;
  const player = ctx.player;
  const tracks = player.textTracks.toArray();
  if (tracks.length === 0) return;
  const isActive = player.textTracks.selectedIndex !== -1;
  menu.addItem((item) => {
    const menu = item
      .setIcon("captions")
      .setTitle("Subtitles")
      .setSection("view")
      // .setChecked(isActive)
      .setSubmenu();
    if (isActive) {
      menu
        .addItem((item) =>
          item
            .setTitle("Disable")
            .setIcon("captions-off")
            .onClick(() => {
              player.textTracks.selected?.setMode("disabled");
            }),
        )
        .addSeparator();
    }
    for (const track of tracks) {
      menu.addItem((item) => {
        item
          .setTitle(track.label)
          .setChecked(track.mode === "showing")
          .onClick(() => {
            track.setMode("showing");
          });
      });
    }
  });
}

export function transcriptMenu(
  menu: Menu,
  ctx: PlayerContext & { plugin: MediaExtended },
) {
  if (ctx.tracks.length > 0) {
    menu.addItem((item) => {
      const menu = item
        .setSection("view")
        .setTitle("Open transcript")
        .setIcon("book-text")
        .setSubmenu();
      ctx.tracks.forEach((track, idx) => {
        const label = formatLabel(track.meta, idx);
        menu.addItem((item) => {
          item.setTitle(label).onClick(() => {
            openTrack(track, {
              workspace: ctx.plugin.app.workspace,
              newLeaf: "split",
              direction: "horizontal",
            });
          });
        });
      });
    });
  }
}
