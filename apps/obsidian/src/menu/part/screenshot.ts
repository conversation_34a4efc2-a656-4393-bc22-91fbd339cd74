import type { PlayerContext } from "../def";
import { Notice, type Menu } from "obsidian";
import { copyBlob } from "@/link/insert/screenshot";
import { isVideoProvider } from "@vidstack/react";

export function screenshotMenu(
  menu: Menu,
  ctx: Pick<PlayerContext, "captureScreenshot" | "player">,
) {
  if (!ctx.captureScreenshot) return;
  const capture = ctx.captureScreenshot;
  if (!ctx.player || isVideoProvider(ctx.player.provider))
    menu.addItem((item) =>
      item
        .setTitle("Copy Screenshot")
        .setSection("action")
        .setIcon("focus")
        .onClick(async () => {
          try {
            const screenshot = await capture({ format: "image/png" });
            await copyBlob(screenshot.blob);
            new Notice("Screenshot copied to clipboard");
          } catch (e) {
            new Notice("Failed to copy screenshot, see console for details");
            console.error("Failed to copy screenshot", e);
          }
        }),
    );
}
