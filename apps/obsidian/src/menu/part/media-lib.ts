import type { MediaInfo } from "@/def/media-info";
import type MediaExtended from "@/mx-main";
import { type Menu, Notice } from "obsidian";

export function mediaLibMenu(
  menu: Menu,
  ctx: { src: MediaInfo; plugin: MediaExtended },
) {
  const mediaPropsNote = ctx.plugin.mediaLib.findNoteByMedia(ctx.src);
  if (!mediaPropsNote) {
    menu.addItem((item) => {
      item
        .setTitle("Add to media library")
        .setIcon("badge-plus")
        .setSection("action")
        .onClick(async () => {
          const media = await ctx.plugin.mediaItemFor(ctx.src);
          new Notice(`Added to media library: ${media.note.path}`);
        });
    });
  } else {
    menu.addItem((item) =>
      item
        .setTitle("Open media properties")
        .setIcon("table-properties")
        .setSection("view")
        .onClick(() => {
          ctx.plugin.app.workspace.openLinkText(mediaPropsNote.path, "", "tab");
        }),
    );
    menu.addItem((item) => {
      item
        .setTitle("Remove from media library")
        .setIcon("trash")
        .setSection("danger")
        .onClick(async () => {
          await ctx.plugin.app.vault.delete(mediaPropsNote);
          new Notice(`Removed from media library: ${mediaPropsNote.path}`);
        });
    });
  }
}
