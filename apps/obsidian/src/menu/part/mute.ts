import type { Menu } from "obsidian";
import type { PlayerContext } from "../def";

export function muteMenu(menu: Menu, ctx: PlayerContext) {
  if (!ctx.player) return;
  const player = ctx.player;
  menu.addItem((item) =>
    item
      .setTitle("Mute")
      .setSection("action")
      .setIcon("volume-high")
      .setChecked(player.muted)
      .onClick(() => {
        player.muted = !player.muted;
      }),
  );
}
