import type MediaExtended from "@/mx-main";
import {
  isAudioProvider,
  isVideoProvider,
  type MediaPlayerInstance,
} from "@vidstack/react";
import type { Menu } from "obsidian";
import { speedOptions } from "../../def/speed";
import type { PlayerContext } from "../def";

export function speedMenu(
  menu: Menu,
  ctx: PlayerContext & { plugin: MediaExtended },
) {
  if (!ctx.player) return;
  const player = ctx.player;
  menu.addItem((item) => {
    const speedMenu = item
      .setSection("action")
      .setIcon("gauge")
      .setTitle(`Speed ${player.state.playbackRate}x`)
      .setSubmenu();

    if (preservePitchMenu(speedMenu, player)) {
      speedMenu.addSeparator();
    }
    for (const option of speedOptions) {
      speedMenu.addItem((item) => {
        item
          .setTitle(`${option}x`)
          .setChecked(player.state.playbackRate === option)
          .onClick(() => {
            player.playbackRate = option;
          });
      });
    }
  });
}

function preservePitchMenu(menu: Menu, player: MediaPlayerInstance): boolean {
  if (
    (isVideoProvider(player.provider) || isAudioProvider(player.provider)) &&
    "preservesPitch" in HTMLMediaElement.prototype
  ) {
    const media = player.provider.media;
    menu.addItem((item) =>
      item
        .setTitle("Preserve pitch")
        .setChecked(media.preservesPitch)
        .onClick(() => {
          media.preservesPitch = !media.preservesPitch;
        }),
    );
    return true;
  }
  return false;
}
