import { Notice, type Menu } from "obsidian";
import { isUrlMediaInfo } from "@/def/media-info";
import { Platform } from "obsidian";
import { showItemInFolder, openPath } from "@/lib/file-op";
import { requireUrl } from "@/lib/node";
import type { PlayerContext } from "../def";
import { normalizeDirectUrl, toNormalizedUrl } from "@/def/url-parse";
import type { TempFragment } from "@mx/shared/time/temporal-frag";
import { assertNever } from "@std/assert/unstable-never";

export function urlMenu(
  menu: Menu,
  { src, takeTimestamp }: Pick<PlayerContext, "src" | "takeTimestamp">,
  { source }: { source: "player" | "url-menu" },
) {
  if (!isUrlMediaInfo(src)) return;

  if (src.type === "url:direct" && src.url.protocol === "file:") {
    if (!Platform.isDesktopApp) return;

    const { fileURLToPath } = requireUrl();
    const filePath = fileURLToPath(src.url);
    menu
      .addItem((item) =>
        item
          .setIcon("folder")
          .setTitle(
            Platform.isMacOS ? "Reveal in Finder" : "Show in system explorer",
          )
          .setSection("system")
          .onClick(() => {
            try {
              showItemInFolder(filePath);
            } catch (err) {
              new Notice(
                `Failed to open file in file explorer: ${
                  err instanceof Error ? err.message : String(err)
                }`,
              );
              console.error("Failed to open file in file explorer", err);
            }
          }),
      )
      .addItem((item) =>
        item
          .setIcon("arrow-up-right")
          .setTitle("Open in system default player")
          .setSection("system")
          .onClick(() => {
            openPath(filePath).catch((err) => {
              new Notice(
                `Failed to open file in system player: ${err.message}`,
              );
              console.error("Failed to open file in system player", err);
            });
          }),
      );
    return;
  }

  if (source !== "url-menu")
    menu.addItem((item) =>
      item
        .setTitle("Copy URL")
        .setIcon("copy")
        .setSection("action")
        .onClick(() => {
          navigator.clipboard.writeText(src.url.toString());
          new Notice("URL copied to clipboard");
        }),
    );
  if (takeTimestamp) {
    menu.addItem((item) =>
      item
        .setTitle("Copy URL with timestamp")
        .setIcon("copy-check")
        .setSection("action")
        .onClick(async () => {
          const { currentTime } = await takeTimestamp();
          if (currentTime <= 0) {
            new Notice("Playback must be started before copying timestamp");
            return;
          }
          const t: TempFragment = { start: currentTime, end: -1 };
          let url: URL;
          if (src.type === "url:direct") {
            url = normalizeDirectUrl(src.url, t);
          } else if (src.type === "url:hosted") {
            url = toNormalizedUrl(src.vid, t);
          } else {
            assertNever(src);
          }
          navigator.clipboard.writeText(url.toString());
          new Notice("URL with timestamp copied");
        }),
    );
  }
}
