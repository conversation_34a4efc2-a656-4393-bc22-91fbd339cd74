import type { MediaInfo } from "@/def/media-info";
import type MediaExtended from "@/mx-main";
import type { Menu } from "obsidian";

export function embedMenu(
  menu: Menu,
  ctx: { src: MediaInfo; plugin: MediaExtended },
) {
  menu
    .addItem((item) =>
      item
        .setTitle("Open to the right")
        .setIcon("separator-vertical")
        .setSection("open")
        .onClick(() => {
          ctx.plugin.link.openMedia({ info: ctx.src }, { newLeaf: "split" });
        }),
    )
    .addItem((item) =>
      item
        .setTitle("Open in new tab")
        .setSection("open")
        .setIcon("file-plus")
        .onClick(() => {
          ctx.plugin.link.openMedia({ info: ctx.src }, { newLeaf: "tab" });
        }),
    )
    .addItem((item) =>
      item
        .setTitle("Open in new window")
        .setSection("open")
        .setIcon("maximize")
        .onClick(() => {
          ctx.plugin.link.openMedia({ info: ctx.src }, { newLeaf: "window" });
        }),
    );
}
