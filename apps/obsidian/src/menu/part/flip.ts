import type { PlayerContext } from "../def";
import type { Menu } from "obsidian";
import { isAudioProvider } from "@vidstack/react";

export function flipMenu(
  menu: Menu,
  ctx: Pick<PlayerContext, "setFlip" | "flip" | "player">,
) {
  if (ctx.player && !isAudioProvider(ctx.player.provider))
    menu.addItem((item) =>
      item
        .setTitle("Flip Video")
        .setSection("action")
        .setIcon("flip-horizontal-2")
        .setSubmenu()
        .addItem((item) =>
          item
            .setTitle("Disable")
            .setIcon("rotate-ccw-square")
            .setChecked(ctx.flip === "none")
            .onClick(() => ctx.setFlip("none")),
        )
        .addItem((item) =>
          item
            .setTitle("Horizontal (Mirror)")
            .setIcon("flip-horizontal")
            .setChecked(ctx.flip === "horizontal" || ctx.flip === "both")
            .onClick(() => {
              if (ctx.flip === "horizontal") {
                ctx.setFlip("none");
              } else if (ctx.flip === "vertical") {
                ctx.setFlip("both");
              } else if (ctx.flip === "both") {
                ctx.setFlip("vertical");
              } else {
                ctx.setFlip("horizontal");
              }
            }),
        )
        .addItem((item) =>
          item
            .setTitle("Vertical (Upside down)")
            .setIcon("flip-vertical")
            .setChecked(ctx.flip === "vertical" || ctx.flip === "both")
            .onClick(() => {
              if (ctx.flip === "vertical") {
                ctx.setFlip("none");
              } else if (ctx.flip === "horizontal") {
                ctx.setFlip("both");
              } else if (ctx.flip === "both") {
                ctx.setFlip("horizontal");
              } else {
                ctx.setFlip("vertical");
              }
            }),
        ),
    );
}
