import type { MediaInfo } from "@/def/media-info";
import type { FnScreenshot, FnTimestamp } from "@/def/player-comp";
import type { TextTrackInfo } from "@/def/track-info";
import type { PlayerFlipOption } from "@mx/ui";
import type { MediaPlayerInstance } from "@vidstack/react";

declare module "obsidian" {
  interface Workspace {
    on(name: "url-menu", callback: (menu: Menu, link: string) => any): EventRef;
    on(
      name: "mx:media-menu",
      callback: (
        menu: Menu,
        player: PlayerContext,
        source:
          | "player-menu-view"
          | "player-menu-embed"
          | "sidebar-context-menu"
          | "tab-header"
          | "more-options",
        // leaf?: WorkspaceLeaf,
      ) => any,
      ctx?: any,
    ): EventRef;

    trigger(
      name: "mx:media-menu",
      menu: Menu,
      player: PlayerContext,
      source:
        | "player-menu-view"
        | "player-menu-embed"
        | "sidebar-context-menu"
        | "tab-header"
        | "more-options"
        | string,
      // leaf?: WorkspaceLeaf,
    ): void;
  }
}

export interface PlayerContext {
  player: MediaPlayerInstance | null;
  takeTimestamp?: FnTimestamp;
  captureScreenshot?: FnScreenshot;
  setFlip: (flip: PlayerFlipOption) => void;
  flip: PlayerFlipOption;
  src: MediaInfo;
  tracks: TextTrackInfo[];
}
