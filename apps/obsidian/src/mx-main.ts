import "@mx/shared/polyfills/disposable";
import "@mx/shared/polyfills/with-resolvers";
import "@mx/shared/polyfills/regexp-escape";
import "@mx/shared/polyfills/urlpattern";

import { MxSettingTab } from "@/settings/tab";
import MxSettings from "@/settings/registry";
import fixLinkLabel from "@/patch/link.label-fix";
import { LinkRegistry } from "@/link";
import injectMediaView from "@/patch/view";
import MediaFileView, { MEDIA_FILE_VIEW_TYPE } from "@/media-view/file-view";
import MediaUrlView, { MEDIA_URL_VIEW_TYPE } from "@/media-view/url-view";
import { getPlayerStylesheets } from "./components/shadow-dom";
import { registerMenuHandler } from "./menu";
import { Plugin } from "obsidian";
import type BrowserConnect from "@/browser-connect/renderer";
import { MediaLibraryIndex } from "./media-lib/indexer";
import type { MediaInfo } from "./def/media-info";
import { mediaLibItemNoteFor } from "./media-lib/insert";
import { registerControlCommands } from "./command/media";
import { registerNoteCommands } from "./command/note";
import { TranscriptLoader } from "./transcript/loader";
import { mediaExtensions, trackExtensions } from "./def/media-type";
import VaultTrackFileView, {
  VAULT_TRACK_VIEW_TYPE,
} from "./track-view/file-view";
import UrlTrackView, { URL_TRACK_VIEW_TYPE } from "./track-view/url-view";
import MediaRemoteView from "./media-view/remote-view";
import { MEDIA_REMOTE_VIEW_TYPE } from "./media-view/remote-view";
import { registerIcons } from "./lib/icons";
import { registerGlobalCommands } from "./command/global";
// import { isBrowserFlagEnabled } from "./settings/tab/browser-test";
import { RecorderNote } from "./audio-rec";
import injectMediaEmbed from "./patch/embed";
import { initSwitcher, openMediaSwitcher } from "./switcher";
import { addEmptyViewActions } from "./lib/mod-empty";
import { registerProtocol } from "./protocol/obsidian";
import { handleTrackPaste } from "./track-view/paste";
import "./icons";
import { AuthService } from "./auth/service";

export default class MxPlugin extends Plugin {
  settings = this.addChild(new MxSettings(this));
  link = this.addChild(new LinkRegistry(this));
  // #browserConnect =
  //   Platform.isDesktopApp && isBrowserFlagEnabled()
  //     ? this.addChild(new BrowserConnect(this.app))
  //     : null;
  #browserConnect: BrowserConnect | null = null;
  mediaLib = this.addChild(new MediaLibraryIndex(this));
  transcriptLoader = new TranscriptLoader(this);
  recorderNote = this.addChild(new RecorderNote(this));
  auth = this.addChild(new AuthService(this));

  /**
   * Get the media item note from media lib for a given media,
   * if not found, create a new one
   */
  async mediaItemFor(media: MediaInfo) {
    return await mediaLibItemNoteFor(media, {
      fileManager: this.app.fileManager,
      mediaLibIndex: this.mediaLib,
      metadataCache: this.app.metadataCache,
      vault: this.app.vault,
      settings: this.settings,
    });
  }

  get rpc() {
    if (!this.#browserConnect || !this.#browserConnect.connected) {
      return null;
    }
    return this.#browserConnect.instance;
  }
  async isRemoteSupported() {
    return !!(await this.rpc?.getRemoteListeningAddress());
  }

  onload() {
    super.onload();
    registerIcons();
    registerMenuHandler(this);
    // prepare the player stylesheets ahead of time
    void getPlayerStylesheets();
    fixLinkLabel(this);
    this.addSettingTab(new MxSettingTab(this.app, this));
    injectMediaView.call(
      this,
      MEDIA_FILE_VIEW_TYPE,
      (leaf) => new MediaFileView(leaf, this),
      mediaExtensions,
    );
    this.registerView(
      MEDIA_URL_VIEW_TYPE,
      (leaf) => new MediaUrlView(leaf, this),
    );
    this.registerView(
      VAULT_TRACK_VIEW_TYPE,
      (leaf) => new VaultTrackFileView(leaf, this),
    );
    this.registerView(
      URL_TRACK_VIEW_TYPE,
      (leaf) => new UrlTrackView(leaf, this),
    );
    this.registerView(
      MEDIA_REMOTE_VIEW_TYPE,
      (leaf) => new MediaRemoteView(leaf, this),
    );
    injectMediaEmbed(this);
    this.registerExtensions(trackExtensions, VAULT_TRACK_VIEW_TYPE);
    registerControlCommands(this);
    registerNoteCommands(this);
    registerGlobalCommands(this);
    initSwitcher(this);
    handleTrackPaste(this);
    addEmptyViewActions(this.app, [
      {
        callback: () => openMediaSwitcher(this),
        title: "Open external media",
      },
    ]).then((unload) => {
      this.register(() => unload[Symbol.dispose]());
    });
    registerProtocol(this);
    console.log("Hello from Media Extended");
  }
}
