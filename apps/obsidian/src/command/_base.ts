import type {
  Editor,
  MarkdownFileInfo,
  Command,
  Plugin,
  Modifier,
  Workspace,
  Vault,
} from "obsidian";
import { Keymap, Platform, type MarkdownView } from "obsidian";
import type MxPlugin from "@/mx-main";
import {
  getActiveMediaView,
  getMostRecentEditorLeaf,
} from "@/link/leaf/finder";
import { getWorkspaceMediaView } from "@/link/active-view";
import type { MediaLibraryIndex } from "@/media-lib/indexer";
import type { PlayerComponent } from "@/def/player-comp";

export type MediaCheckCallback = (
  checking: boolean,
  player: PlayerComponent,
  note: {
    editor?: Editor;
    ctx: MarkdownView | MarkdownFileInfo;
  } | null,
) => boolean | undefined;

function toCommand(
  mediaCheckCallback: MediaCheckCallback,
  ctx: {
    workspace: Workspace;
    mediaLib: MediaLibraryIndex;
    vault: Vault;
  },
) {
  return (checking: boolean): boolean | undefined => {
    const activeEditor = ctx.workspace.activeEditor;
    // if current active view is an editor
    if (activeEditor) {
      const linkedMediaView = getWorkspaceMediaView({
        workspace: ctx.workspace,
        mediaLib: ctx.mediaLib,
        vault: ctx.vault,
      });
      if (!linkedMediaView) return false;
      return mediaCheckCallback(checking, linkedMediaView, {
        editor: activeEditor.editor,
        ctx: activeEditor,
      });
    }
    // if current active view is a media view
    const activeMediaView = getActiveMediaView(ctx.workspace);
    if (activeMediaView) {
      // find if there are any editor available in current workspace
      const editorLeaf = getMostRecentEditorLeaf(ctx.workspace);
      return mediaCheckCallback(
        checking,
        activeMediaView,
        editorLeaf && {
          editor: editorLeaf.view.editor,
          ctx: editorLeaf.view,
        },
      );
    }
    return false;
  };
}

interface MenuProps {
  section?: string;
}

export function addMediaViewCommand(
  {
    id,
    name,
    hotkeys,
    icon,
    menu,
    mobileOnly,
    repeatable,
    checkCallback,
  }: Omit<
    Command,
    "callback" | "checkCallback" | "editorCheckCallback" | "editorCallback"
  > & {
    menu?: true | MenuProps;
    checkCallback: MediaCheckCallback;
  },
  plugin: MxPlugin,
): void {
  const { app } = plugin;
  plugin.addCommand({
    id,
    name,
    hotkeys,
    icon,
    mobileOnly,
    repeatable,
    checkCallback: toCommand(checkCallback, {
      workspace: app.workspace,
      mediaLib: plugin.mediaLib,
      vault: plugin.app.vault,
    }),
  });
  if (menu) {
    const menuProps = typeof menu === "boolean" ? {} : menu;
    plugin.registerEvent(
      plugin.app.workspace.on("editor-menu", (menu, editor, ctx) => {
        if (mobileOnly && Platform.isMobile) return;
        const linkedMediaView = getWorkspaceMediaView({
          workspace: plugin.app.workspace,
          vault: plugin.app.vault,
          mediaLib: plugin.mediaLib,
        });
        if (
          !linkedMediaView ||
          !checkCallback(true, linkedMediaView, { editor, ctx })
        )
          return;
        menu.addItem((item) => {
          icon && item.setIcon(icon);
          name && item.setTitle(name);
          menuProps.section && item.setSection(menuProps.section);
          item.onClick(() => {
            checkCallback(false, linkedMediaView, { editor, ctx });
          });
        });
      }),
    );
  }
}

export function handleRepeatHotkey<Params extends any[]>(
  plugin: Plugin,
  {
    onKeyDown,
    onTrigger,
    onKeyUp,
  }: {
    onKeyDown: (evt: KeyboardEvent, ...params: Params) => void;
    onTrigger?: (...params: Params) => void;
    onKeyUp?: (evt: KeyboardEvent, ...params: Params) => void;
  },
) {
  let dispatched: Params | null = null;
  const keyupHandlers = new Set<(...arg: any[]) => void>();
  plugin.register(() => {
    for (const handler of keyupHandlers) {
      window.removeEventListener("keyup", handler, { capture: true });
    }
  });
  plugin.registerDomEvent(
    window,
    "keydown",
    (evt) => {
      if (!dispatched) return;
      const target = evt.target as HTMLElement;
      if (
        target.instanceOf(HTMLElement) &&
        target.matches("input.prompt-input")
      )
        return;

      // if keydown event is not fired
      const hotkey = evt;
      onKeyDown(evt, ...dispatched);
      if (onKeyUp) {
        const params = dispatched;
        const handler = (evt: KeyboardEvent) => {
          const modifiers = toModifiers(hotkey);
          // in macOS, regular key up will not fired when meta key is pressed, only meta keyup is fired
          if (
            (evt.code === hotkey.code &&
              modifiers.every((m) => Keymap.isModifier(evt, m))) ||
            modifiers.some((m) => evt.key === m)
          ) {
            onKeyUp(evt, ...params);
            window.removeEventListener("keyup", handler, { capture: true });
            keyupHandlers.delete(handler);
          }
        };
        keyupHandlers.add(handler);
        window.addEventListener("keyup", handler, {
          passive: true,
          capture: true,
        });
      }
      dispatched = null;
    },
    true,
  );
  return {
    callback: (...params: Params) => {
      dispatched = params;
      setTimeout(() => {
        // macrotask are executed after all event handlers
        // here check if the event is handled by keydown handler
        if (dispatched === null) {
          // console.debug("command evoked from keyboard");
        } else {
          // console.log("command evoked from command");
          onTrigger?.(...params);
          dispatched = null;
        }
      }, 0);
    },
  };
}

function toModifiers(evt: KeyboardEvent) {
  const modifiers = [] as Modifier[];
  if (evt.ctrlKey) modifiers.push("Ctrl");
  if (evt.altKey) modifiers.push("Alt");
  if (evt.shiftKey) modifiers.push("Shift");
  if (evt.metaKey) modifiers.push("Meta");
  return modifiers;
}
