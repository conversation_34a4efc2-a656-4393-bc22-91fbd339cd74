import { isAudioProvider, type MediaPlayerInstance } from "@vidstack/react";
import { Notice, debounce } from "obsidian";
import type MxPlugin from "@/mx-main";
import { addMediaViewCommand, handleRepeatHotkey } from "./_base";
import { speedOptions } from "@/def/speed";
import { promptPlaybackSpeed } from "@/menu/speed-prompt";
import type { PlayerFlipOption } from "@mx/ui";
import { mediaFlipAtom } from "@/def/atom/media-meta";

const createMediaCommands = (plugin: MxPlugin): Controls[] => [
  {
    id: "toggle-play",
    label: "Play/pause",
    icon: "play",
    action: (media) => {
      media.paused = !media.paused;
    },
  },
  {
    id: "play",
    label: "Play",
    icon: "play",
    action: (media) => {
      media.play();
    },
  },
  {
    id: "pause",
    label: "Pause",
    icon: "pause",
    action: (media) => {
      media.pause();
    },
  },
  ...[0.5, 1, 2, 5, 10, 30, 60].flatMap((sec): Controls[] => [
    {
      id: `forward-${sec}s`,
      label: `Forward ${sec}s`,
      icon: "forward",
      action: (media) => {
        media.currentTime += sec;
      },
      repeat: true,
    },
    {
      id: `rewind-${sec}s`,
      label: `Rewind ${sec}s`,
      icon: "rewind",
      action: (media) => {
        media.currentTime -= sec;
      },
      repeat: true,
    },
  ]),
  {
    id: "toggle-mute",
    label: "Mute/unmute",
    icon: "volume-x",
    action: (media) => {
      media.muted = !media.muted;
    },
  },
  {
    id: "toggle-fullscreen",
    label: "Enter/exit fullscreen",
    icon: "expand",
    check: (media) => media.state.canFullscreen,
    action: (media) => {
      if (media.state.fullscreen) {
        media.exitFullscreen();
      } else {
        media.enterFullscreen();
      }
    },
  },
  ...speed(plugin),
];
function speed(plugin: MxPlugin): Controls[] {
  // reuse notice if user is spamming speed change
  let notice: Notice | null = null;
  const hide = debounce(() => notice?.hide(), 2000, true);
  function notify(message: string) {
    if (!notice || notice.messageEl.isConnected === false) {
      notice = new Notice(message, 0);
    } else {
      notice.setMessage(message);
    }
    hide();
  }
  function notifyManualHide(message: string) {
    if (!notice || notice.messageEl.isConnected === false) {
      notice = new Notice(message, 0);
    } else {
      notice.setMessage(message);
    }
  }
  function notifyAllowDup(message: string) {
    new Notice(message, 2000);
  }
  return [
    ...speedOptions
      .filter((s) => s > 1)
      .map((speed): Controls => {
        // this callback will be set up when the key starts repeating
        let resetCallback: (() => void) | null = null;
        const reset = debounce(
          () => {
            resetCallback?.();
            resetCallback = null;
          },
          1e3,
          true,
        );
        const { callback } = handleRepeatHotkey<[MediaPlayerInstance]>(plugin, {
          onKeyDown(evt, media) {
            if (!evt.repeat) return;
            if (!resetCallback) {
              // task not started yet, set up the callback
              resetCallback = () => {
                media.playbackRate = 1;
                notice?.hide();
                resetCallback = null;
              };
              media.playbackRate = speed;
              notifyManualHide(`Fast forwarding at ${speed}x`);
            }
            // in case that key up handler is not triggered, reset the speed
            // this will not fire if the key is still repeating
            reset();
          },
          onKeyUp() {
            resetCallback?.();
          },
        });
        return {
          id: `fast-forward-${speed}x`,
          label: `Fast forward at ${speed}x by holding hotkey`,
          icon: "forward",
          action: callback,
          repeat: true,
        };
      }),
    ...speedOptions.map(
      (speed): Controls => ({
        id: `set-speed-${speed}x`,
        label: `Set playback speed to ${speed}x`,
        icon: "gauge",
        action: async (media) => {
          media.playbackRate = speed;
          notify(`Speed set to ${speed}x`);
        },
      }),
    ),
    {
      id: "reset-speed",
      label: "Reset playback speed",
      icon: "reset",
      check: (media) => media.state.playbackRate !== 1,
      action: (media) => {
        media.playbackRate = 1;
        notifyAllowDup("Speed reset to 1x");
      },
    },
    {
      id: "increase-speed",
      label: "Increase playback speed",
      icon: "arrow-up",
      action: (media) => {
        const curr = media.playbackRate;
        if (curr >= speedOptions.last()!) {
          notifyAllowDup("Cannot increase speed further");
          return;
        }
        // find nearest speed option greater than current speed
        const next = speedOptions.find((speed) => speed > curr)!;
        media.playbackRate = next;
        notify(`Speed increased to ${next}x`);
      },
    },
    {
      id: "decrease-speed",
      label: "Decrease playback speed",
      icon: "arrow-down",
      action: (media) => {
        const curr = media.playbackRate;
        if (curr <= speedOptions.first()!) {
          notifyAllowDup("Cannot decrease speed further");
          return;
        }
        // find nearest speed option less than current speed
        const prev = speedOptions
          .slice()
          .reverse()
          .find((speed) => speed < curr)!;
        media.playbackRate = prev;
        notify(`Speed decreased to ${prev}x`);
      },
    },
    {
      id: "set-speed",
      label: "Set playback speed to custom value",
      icon: "gauge",
      action: async (media) => {
        const newSpeed = await promptPlaybackSpeed(plugin.app);
        if (!newSpeed) return;
        media.playbackRate = newSpeed;
        notify(`Speed set to ${newSpeed}x`);
      },
    },
    {
      id: "increase-speed-granular",
      label: "Fine-tune increase playback speed",
      icon: "arrow-up",
      action: async (media) => {
        const curr = media.playbackRate;
        const maxSpeed = speedOptions.at(-1)!;
        if (curr >= maxSpeed) {
          notifyAllowDup("Cannot increase speed further");
          return;
        }
        const settings = await plugin.settings.loaded;
        const step = settings["playback.speed-step"];
        const next = Math.round((curr + step) * 100) / 100;
        if (next > maxSpeed) {
          notifyAllowDup("Cannot increase speed further");
          return;
        }
        media.playbackRate = next;
        notify(`Speed increased to ${next}x`);
      },
    },
    {
      id: "decrease-speed-granular",
      label: "Fine-tune decrease playback speed",
      icon: "arrow-down",
      action: async (media) => {
        const curr = media.playbackRate;
        const minSpeed = speedOptions.at(0)!;
        if (curr <= minSpeed) {
          notifyAllowDup("Cannot decrease speed further");
          return;
        }
        const settings = await plugin.settings.loaded;
        const step = settings["playback.speed-step"];
        const prev = Math.round((curr - step) * 100) / 100;
        if (prev < minSpeed) {
          notifyAllowDup("Cannot decrease speed further");
          return;
        }
        media.playbackRate = prev;
        notify(`Speed decreased to ${prev}x`);
      },
    },
  ];
}
interface Controls {
  id: string;
  label: string;
  icon: string;
  repeat?: boolean;
  check?: (media: MediaPlayerInstance) => boolean;
  action: (media: MediaPlayerInstance) => void;
}

export function registerControlCommands(plugin: MxPlugin) {
  for (const { id, label, icon, action, repeat, check } of createMediaCommands(
    plugin,
  )) {
    addMediaViewCommand(
      {
        id,
        name: label,
        icon,
        repeatable: repeat,
        checkCallback: (checking, mediaView, _note) => {
          const player = mediaView.getPlayer();
          if (!player) return false;
          if (check && !check(player)) return false;
          if (checking) return true;
          if (player.el?.doc) {
            const prevActiveDoc = window.activeDocument;
            window.activeDocument = player.el.doc;
            action(player);
            window.activeDocument = prevActiveDoc;
          } else {
            action(player);
          }
        },
      },
      plugin,
    );
  }

  for (const { option, label, icon, message } of flipCommands) {
    addMediaViewCommand(
      {
        id: `flip-video-${option}`,
        name: label,
        icon,
        checkCallback: (checking, mediaView, _note) => {
          const player = mediaView.getPlayer();
          if (!player || !isAudioProvider(player.provider)) return false;
          if (checking) return true;
          if (player.el?.doc) {
            const prevActiveDoc = window.activeDocument;
            window.activeDocument = player.el.doc;
            mediaView.store.set(mediaFlipAtom, option);
            new Notice(message);
            window.activeDocument = prevActiveDoc;
          } else {
            mediaView.store.set(mediaFlipAtom, option);
            new Notice(message);
          }
        },
      },
      plugin,
    );
  }
}

const flipCommands: {
  label: string;
  option: PlayerFlipOption;
  icon: string;
  message: string;
}[] = [
  {
    option: "none",
    label: "Reset Video Flip",
    message: "Video flip reset",
    icon: "rotate-ccw-square",
  },
  {
    option: "horizontal",
    label: "Flip Video Horizontally (Mirror)",
    message: "Video flipped to horizontal",
    icon: "flip-horizontal",
  },
  {
    option: "vertical",
    label: "Flip Video Vertically (Upside down)",
    message: "Video flipped to vertical",
    icon: "flip-vertical",
  },
  {
    option: "both",
    label: "Flip Video Both Ways",
    message: "Video flipped to both ways",
    icon: "rotate-cw-square",
  },
];
