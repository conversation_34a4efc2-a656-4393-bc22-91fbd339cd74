import { migrateMediaUid } from "@/lib/migrate-media-uid";
import type MxPlugin from "@/mx-main";
import VaultTrackFileView from "@/track-view/file-view";
import UrlTrackView from "@/track-view/url-view";
import { around } from "monkey-around";
import { Notice, type Command } from "obsidian";

export function registerGlobalCommands(plugin: MxPlugin) {
  plugin.addCommand({
    id: "mx:migrate-media-uid",
    name: "Migrate media notes from old format",
    callback: async () => {
      const notice = new Notice("Migrating media notes from old format...", 0);
      try {
        const notes = await migrateMediaUid(plugin.app);
        notice.setMessage(`Migrated ${notes.length} media notes`);
        console.log("Migrated media notes from old format", notes);
      } catch (error) {
        notice.setMessage(
          "Error migrating media notes from old format, please check console for more details",
        );
        console.error("Error migrating media notes from old format", error);
      } finally {
        await sleep(2000);
        notice.hide();
      }
    },
  });
  // hijack the editor:open-search command
  const openSearchCommand = (plugin.app as any).commands.commands[
    "editor:open-search"
  ] as Required<Command> | undefined;
  if (!openSearchCommand || !openSearchCommand.checkCallback) {
    console.warn("editor:open-search command not found");
  } else {
    plugin.register(
      around(openSearchCommand, {
        checkCallback: (next) =>
          function (this: Command, checking: boolean) {
            const activeView =
              plugin.app.workspace.getActiveViewOfType(VaultTrackFileView) ||
              plugin.app.workspace.getActiveViewOfType(UrlTrackView);
            if (!activeView) return next(checking);
            if (checking) return true;
            activeView.toggleSearch();
          },
      }),
    );
  }
}
