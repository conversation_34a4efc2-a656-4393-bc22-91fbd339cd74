import uiStyleCss from "inline:@mx/ui/style.css";
import uiVariablesCss from "inline:@mx/ui/variables.css";
import shadowDomCss from "inline:./shadow-dom.css";
// at rules only works in global scope, not in shadow dom
import "@mx/ui/properties.css";

import {
  ShadowRoot as BaseShadowRoot,
  type ShadowRootProps as BaseShadowRootProps,
} from "@mx/shared/shadow-dom";
import { useState } from "react";

const playerStylesheets = new WeakMap<Document, Promise<CSSStyleSheet[]>>();

export async function getPlayerStylesheets(
  win: Window = window,
): Promise<CSSStyleSheet[]> {
  const document = win.document;
  if (playerStylesheets.has(document)) {
    return playerStylesheets.get(document)!;
  }
  const stylesheets = Promise.all([
    buildStylesheets(uiStyleCss, win),
    buildStylesheets(uiVariablesCss, win),
    buildStylesheets(shadowDomCss, win),
  ]);
  playerStylesheets.set(document, stylesheets);
  return await stylesheets;
}

async function buildStylesheets(css: string, win: Window) {
  const { CSSStyleSheet } = win as Window & typeof globalThis;
  const stylesheet = new CSSStyleSheet();
  await stylesheet.replace(css);
  return stylesheet;
}

export type ShadowRootProps = Omit<BaseShadowRootProps, "styleSheets">;

export function ShadowRoot(props: ShadowRootProps) {
  const [stylesheets, setStylesheets] = useState<CSSStyleSheet[]>([]);

  return (
    <BaseShadowRoot
      ref={(el) => {
        if (!el) return;
        el.onWindowMigrated((win) => {
          getPlayerStylesheets(win).then(setStylesheets);
        });
        getPlayerStylesheets(el.win).then(setStylesheets);
      }}
      styleSheets={stylesheets}
      {...props}
    />
  );
}
