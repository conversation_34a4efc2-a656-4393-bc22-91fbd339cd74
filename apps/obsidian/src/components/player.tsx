import {
  isAudioProvider,
  is<PERSON>ideoProvider,
  MediaProvider,
  type MediaPlayerProps,
  type MediaSrc,
  type Src,
} from "@vidstack/react";
import {
  usePlayerInitRef,
  useMediaProvider,
  useMediaPlayer,
} from "@mx/shared/hooks/use-player";
import {
  AudioLayout,
  VideoLayout,
  MediaPlayer,
  SettingsButton,
  EditButton,
} from "@mx/ui";

import { atom, useAtomValue, useStore, useAtom, useSetAtom } from "jotai";
import {
  embedFlagAtom,
  mediaHashAtom,
  mediaInfoAtom,
  mediaSrcAtom,
  pluginAtom,
} from "@/def/atom/media";
import { useEffect, useMemo, useRef } from "react";
import { useState } from "react";
import { equal } from "@std/assert";
import { Menu, Notice } from "obsidian";
import { isTimestamp } from "@mx/shared/time/temporal-frag";
import { playerTrackLoader } from "@/transcript/player-loader";
import { media<PERSON>lip<PERSON>tom, mediaMeta<PERSON>tom } from "@/def/atom/media-meta";

import "./player.css";
import { loadedMediaTracksAtom } from "@/def/atom/track";
import type { FnScreenshot, FnTimestamp } from "@/def/player-comp";

const isNonHtmlMediaSrc = (src: Src<unknown> | MediaSrc | undefined | null) => {
  if (!src) return false;
  // Handle both Src<unknown> and string-based MediaSrc
  const type = typeof src === "string" ? src : (src as Src<unknown>).type;
  return (
    typeof type === "string" &&
    (!type || type.includes("vimeo") || type.includes("youtube"))
  );
};

function useDefaultVolume() {
  const [defaultVolume, setDefaultVolume] = useState<number | null>(null);
  const plugin = useAtomValue(pluginAtom);

  useEffect(() => {
    plugin.settings.loaded.then((v) =>
      setDefaultVolume(v["playback.default-volume"]),
    );
  }, [plugin]);

  useEffect(() => {
    const disposables = plugin.settings.subscribe(
      "playback.default-volume",
      (v) => setDefaultVolume(v),
    );
    return () => disposables[Symbol.dispose]();
  }, [plugin]);

  return defaultVolume === null ? null : defaultVolume / 100;
}

function useHashProps(): Omit<MediaPlayerProps, "children"> {
  const hash = useAtomValue(mediaHashAtom);
  if (!hash) return {};
  const timeProps = hash?.tempFragment
    ? isTimestamp(hash.tempFragment)
      ? { currentTime: hash.tempFragment.start }
      : {
          clipStartTime: hash.tempFragment.start,
          clipEndTime: hash.tempFragment.end,
        }
    : {};
  return {
    ...timeProps,
    loop: hash.loop,
    muted: hash.muted,
    volume: hash.volume,
    autoPlay: hash.autoplay,
  };
}

function PreservePitchSetter() {
  const plugin = useAtomValue(pluginAtom);
  const provider = useMediaProvider();

  useEffect(() => {
    if (!isAudioProvider(provider) && !isVideoProvider(provider)) {
      return;
    }
    plugin.settings.loaded.then((v) => {
      provider.media.preservesPitch = v["playback.preserve-pitch"];
    });
  }, [plugin, provider]);

  // it's default pitch setter, so we don't need to subscribe to setting changes
  return <></>;
}

function HashPropsSetter() {
  const player = useMediaPlayer();
  const timestampAtom = useMemo(
    () =>
      atom((get) => {
        const hash = get(mediaHashAtom);
        if (!hash || !isTimestamp(hash.tempFragment)) return;
        return {
          currentTime: hash.tempFragment?.start,
          play: hash.autoplay,
        };
      }),
    [],
  );
  const timestamp = useAtomValue(timestampAtom);
  useEffect(() => {
    if (!player || !timestamp) return;
    if (!player.state.canPlay) {
      const unload = player.listen("can-play", () => {
        if (timestamp.play) {
          player.play();
        }
        if (timestamp.currentTime !== undefined) {
          player.currentTime = timestamp.currentTime;
        }
        unload();
      });
      return unload;
    }
    if (timestamp.play) {
      player.play();
    }
    if (timestamp.currentTime !== undefined) {
      player.currentTime = timestamp.currentTime;
    }
  }, [player, timestamp]);
  return <></>;
}

/**
 * Hook to handle the vidstack issue of refusing to load a second media
 * when using HTML <video>/<audio> elements
 */
function useMediaSourceReloadPatch() {
  const prevMediaRef = useRef<Src<unknown> | null>(null);
  const isReloadingRef = useRef(false);
  const [disabled, setDisabled] = useState(false);

  const handleSourceChange = (now: Src<unknown> | null) => {
    const prev = prevMediaRef.current;
    prevMediaRef.current = now;

    // if source changed, and is non-iframe(youtube/vimeo) -> non-iframe
    // force reload player to patch vidstack issue of
    // refusing to load 2nd media if using html <video>/<audio>
    if (
      prev &&
      now &&
      !equal(prev, now) &&
      !isNonHtmlMediaSrc(prev) &&
      !isNonHtmlMediaSrc(now) &&
      !isReloadingRef.current
    ) {
      // console.log("reload", prev, now);
      isReloadingRef.current = true;
      setDisabled(true);
      sleep(100)
        .then(() => setDisabled(false))
        .finally(() => {
          isReloadingRef.current = false;
        });
    }
  };

  return { disabled, handleSourceChange };
}

export function Player({
  checkInEditor,
  ...settingProps
}: {
  checkInEditor?: () => boolean;
} & SettingsProps) {
  const media = useAtomValue(mediaSrcAtom);
  const meta = useAtomValue(mediaMetaAtom);
  const ref = usePlayerInitRef();
  const patch = useMediaSourceReloadPatch();
  const defaultVolume = useDefaultVolume();
  const hashProps = useHashProps();

  useAtom(playerTrackLoader);

  if (defaultVolume === null) return null;

  return (
    // biome-ignore lint/a11y/useKeyWithClickEvents: <explanation>
    <div
      onClick={
        settingProps.onEditClick
          ? (evt) => {
              if (!checkInEditor?.()) return;
              if (!isEditButton(evt.target)) {
                evt.nativeEvent.stopImmediatePropagation();
              }
            }
          : undefined
      }
    >
      {!media && <p>Not supported</p>}
      <MediaPlayer
        {...hashProps}
        title={meta?.title}
        aspectRatio={meta?.aspectRatio}
        keyDisabled
        flip={meta?.flip}
        ref={ref}
        onError={(e) => {
          new Notice("Error loading media, see console for details");
          console.error("media error", media, e);
        }}
        crossOrigin={meta.crossOrigin || null}
        logLevel={process.env.NODE_ENV === "development" ? "debug" : "error"}
        volume={defaultVolume}
        src={patch.disabled || !media ? (undefined as any) : media}
        onSourceChange={patch.handleSourceChange}
      >
        <MediaProvider />
        <VideoLayout utilsSlot={<Settings {...settingProps} />} />
        <AudioLayout utilsSlot={<Settings {...settingProps} />} />
        <PreservePitchSetter />
        <HashPropsSetter />
      </MediaPlayer>
    </div>
  );
}

interface SettingsProps {
  onEditClick?: () => void;
  captureScreenshot?: FnScreenshot;
  takeTimestamp?: FnTimestamp;
}

function Settings({
  onEditClick,
  captureScreenshot,
  takeTimestamp,
}: SettingsProps) {
  const player = useMediaPlayer();
  const setFlip = useSetAtom(mediaFlipAtom);
  const flip = useAtomValue(mediaMetaAtom).flip;
  const store = useStore();
  return (
    <>
      <SettingsButton
        variant="ghost"
        onClick={(evt) => {
          if (!player) return;
          const mediaInfo = store.get(mediaInfoAtom);
          if (!mediaInfo) return;
          const button = evt.currentTarget as HTMLButtonElement;
          const rect = button.getBoundingClientRect();
          const plugin = store.get(pluginAtom);
          const isEmbed = store.get(embedFlagAtom);
          const mainMenu = new Menu();
          mainMenu.addSections(["open", "action", "view", "danger", "system"]);
          plugin.app.workspace.trigger(
            "mx:media-menu",
            mainMenu,
            {
              player,
              src: mediaInfo,
              tracks: store.get(loadedMediaTracksAtom)?.trackInfo ?? [],
              takeTimestamp,
              captureScreenshot,
              setFlip,
              flip,
            },
            isEmbed ? "player-menu-embed" : "player-menu-view",
          );
          mainMenu.showAtPosition({
            x: rect.x,
            y: rect.bottom,
            width: rect.width,
            overlap: true,
            left: true,
          });
        }}
      />
      <EditButton
        type="button"
        // @ts-ignore
        part="edit-button"
        variant="ghost"
        onClick={onEditClick}
        {...{ [FLAG_LP_PASSTHROUGH]: "" }}
      />
    </>
  );
}

const FLAG_LP_PASSTHROUGH = "data-lp-passthrough";

export function isEditButton(target: EventTarget | null): boolean {
  if (!(target instanceof Element)) return false;
  const button = target.closest("button");
  if (!button) return false;
  return button.hasAttribute(FLAG_LP_PASSTHROUGH);
}
