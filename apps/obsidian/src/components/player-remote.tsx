import { mediaInfoAtom } from "@/def/atom/media";
import { isBrowserMediaInfo, type BrowserMediaInfo } from "@/def/media-info";
import { useAtomValue } from "jotai";

export function PlayerRemote({
  onPlayPause,
  onMute,
  onUnmute,
  onScreenshot,
  onTimestamp,
}: {
  onPlayPause?: (info: BrowserMediaInfo) => void;
  onMute?: (info: BrowserMediaInfo) => void;
  onUnmute?: (info: BrowserMediaInfo) => void;
  onScreenshot?: (info: BrowserMediaInfo) => void;
  onTimestamp?: (info: BrowserMediaInfo) => void;
}) {
  const info = useAtomValue(mediaInfoAtom);
  return (
    <div>
      <h1>PlayerRemote</h1>
      {info && isBrowserMediaInfo(info) && (
        <div>
          <button
            disabled={!onPlayPause}
            onClick={() => onPlayPause?.(info)}
            type="button"
          >
            Play/Pause
          </button>
          <button
            disabled={!onMute}
            onClick={() => onMute?.(info)}
            type="button"
          >
            Mute
          </button>
          <button
            disabled={!onUnmute}
            onClick={() => onUnmute?.(info)}
            type="button"
          >
            Unmute
          </button>
          <button
            disabled={!onScreenshot}
            onClick={() => onScreenshot?.(info)}
            type="button"
          >
            Screenshot
          </button>
          <button
            disabled={!onTimestamp}
            onClick={() => onTimestamp?.(info)}
            type="button"
          >
            Timestamp
          </button>
        </div>
      )}
    </div>
  );
}
