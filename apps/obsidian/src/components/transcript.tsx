import { plugin<PERSON>tom } from "@/def/atom/media";
import {
  parsedTrack<PERSON>tom,
  showSearch<PERSON>tom,
  trackEditor<PERSON>tom,
  trackInfo<PERSON>tom,
} from "@/def/atom/track";
import { encodeTextTrackInfo } from "@/transcript/resolve/parse-fm";
import { useMediaPlayer, useMediaRemote } from "@mx/shared/hooks/use-player";
import { createEventEmitter } from "@mx/shared/utils/event";
import {
  WebVTTEditor,
  type PlayerEvents,
  type CopyExtensionProps,
} from "@mx/ui";
import { useAtom, useAtomValue } from "jotai";
import { Slice } from "prosekit/pm/model";
import { useEffect, useMemo } from "react";

export function Transcript() {
  const textTrack = useAtomValue(parsedTrackAtom);
  const editor = useAtomValue(trackEditorAtom);
  const remote = useMediaRemote();
  const emitter = usePlayerEvents(textTrack?.id);
  const copyHandler = useCopyHandler();
  const [showSearch, setShowSearch] = useAtom(showSearchAtom);
  return (
    editor && (
      <WebVTTEditor
        editor={editor}
        remote={remote}
        emitter={emitter}
        showSearch={showSearch}
        onClose={() => {
          setShowSearch(false);
        }}
        handleSearchKeyDown={(event, editor) => {
          // shift-enter to find previous
          if (event.key === "Enter" && event.shiftKey) {
            editor.commands.findPrev();
          } else if (event.key === "Enter") {
            editor.commands.findNext();
          }
        }}
        {...copyHandler}
      />
    )
  );
}

function useCopyHandler(): CopyExtensionProps {
  const trackinfo = useAtomValue(trackInfoAtom);
  const plugin = useAtomValue(pluginAtom);
  if (!trackinfo || !plugin) return {};

  return {
    transformCopied: (slice, view) => {
      const content = slice.content.addToEnd(
        view.state.schema.nodes.metaTag!.create({
          name: "mx:track-src",
          content: encodeTextTrackInfo(trackinfo, {
            sourcePath: "",
            metadataCache: plugin.app.metadataCache,
          }),
        }),
      );
      return new Slice(content, slice.openStart, slice.openEnd);
    },
    // clipboardTextSerializer: (slice) => {
    //   console.log("clipboardTextSerializer", slice);
    //   const content = slice.content.textBetween(0, slice.content.size, "\n\n");
    //   // find the first timestamp for selected slice
    //   let firstCue: (Node & { attrs: WebVTTCueAttrs }) | undefined;
    //   for (let i = 0; i < slice.content.childCount; i++) {
    //     const node = slice.content.child(i);
    //     if (node.type.name === "webvttCue") {
    //       firstCue = node as Node & { attrs: WebVTTCueAttrs };
    //       break;
    //     }
    //   }
    //   const firstCueStart = firstCue?.attrs.start.getTimestamp();
    //   const firstTimestamp = (
    //     firstCue?.children.find(
    //       (child) => child.type.name === "webvttTimestamp",
    //     )?.attrs as WebVTTTimestampAttrs | undefined
    //   )?.time.getTimestamp();
    //   const targetTime = firstTimestamp ?? firstCueStart ?? -1;
    //   if (targetTime === -1) {
    //     // no timestamp found, return original content
    //     return content;
    //   }
    //   const timestampLink = timestampLinkFactory(
    //     {
    //       currentTime: targetTime,
    //       src: linkedMedia[0]!,
    //     },
    //     {
    //       fileManager: plugin.app.fileManager,
    //       timestampOffset: 0,
    //     },
    //   )(
    //     "" /* we don't know where the link goes, so assume sourcePath is in root folder */,
    //   );
    //   // include as suffix
    //   return `${timestampLink} ${content.trim()}`;
    // },
  };
}

function usePlayerEvents(trackId: string | undefined) {
  const player = useMediaPlayer();
  const emitter = useMemo(() => createEventEmitter<PlayerEvents>(), []);
  useEffect(() => {
    if (!player || !trackId) return;
    using stack = new DisposableStack();
    stack.defer(
      player.subscribe(({ textTrack }) => {
        if (textTrack?.id !== trackId) {
          emitter.emit("track-change", null);
        } else {
          emitter.emit("track-change", textTrack);
        }
      }),
    );
    stack.defer(
      player.subscribe(({ currentTime }) => {
        emitter.emit("time-update", currentTime);
      }),
    );
    // reset connected track, to unbind to track cue-change event
    stack.defer(() => emitter.emit("track-change", null));
    const disposable = stack.move();
    return () => disposable.dispose();
  }, [player, emitter, trackId]);
  return emitter;
}

// import { useEffect, useState } from "react";
// import { parseVtt } from "@mx/ui";
// import { useMediaPlayer, useMediaRemote } from "@mx/shared/hooks/use-player";

// export function PlayerTranscript() {
//   const player = useMediaPlayer();
//   const remote = useMediaRemote();
//   const [data, setData] = useState<NodeJSON | null>(null);
//   useEffect(() => {
//     if (!player) return;
//     return player.subscribe(({ textTrack }) => {
//       if (!textTrack) {
//         setData(null);
//         return;
//       }
//       const load = () => {
//         setData({
//           type: "doc",
//           content: parseVtt(textTrack.cues),
//         });
//       };
//       if (textTrack.readyState === 2) {
//         load();
//         return;
//       }
//       if (textTrack.readyState === 1) {
//         textTrack.addEventListener("load", load, { once: true });
//         return () => {
//           textTrack.removeEventListener("load", load);
//         };
//       }
//       console.error("textTrack cannot become ready", textTrack.readyState);
//     });
//   }, [player]);

//   return <TranscriptView data={data} player={player} remote={remote} />;
// }
