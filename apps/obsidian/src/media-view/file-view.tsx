import { mediaHash<PERSON>tom, mediaInfo<PERSON>tom } from "@/def/atom/media";
import type { FileMediaInfo } from "@/def/media-info";
import type { PlayerComponent, ScreenshotResult } from "@/def/player-comp";
import type { PaneMenuSource } from "@/lib/menu";
import type MediaExtended from "@/mx-main";
import { createStore } from "jotai";
import {
  EditableFileView,
  Scope,
  type WorkspaceLeaf,
  type TFile,
  type Menu,
} from "obsidian";
import { Player } from "../components/player";
import { isMediaFile } from "@/def/media-type";
import { parseHashProps } from "@mx/shared/utils/hash-prop";
import { parseFile } from "@/def/file-parse";
import {
  captureScreenshot,
  type ScreenshotOptions,
} from "@mx/shared/dom/screenshot";
import { playerAtom } from "@mx/shared/hooks/use-player";
import { isVideoProvider, type MediaPlayerInstance } from "@vidstack/react";
import { renderView } from "@/media-view/_render";
import { addAction, onPaneMenu, updateHeader } from "./_action";
import { mediaIconAtom } from "@/def/atom/media-icon";
import { registerMediaViewScopes } from "./_scope";

export const MEDIA_FILE_VIEW_TYPE = "mx-file";

export type MediaFileViewType = typeof MEDIA_FILE_VIEW_TYPE;

export default class MediaFileView
  extends EditableFileView
  implements PlayerComponent
{
  allowNoFile = false;
  // inherit from EditableFileView, no need to set explicitly
  // navigation = true
  // no need to manage scope manually,
  // as it's implicitly called and handled by the WorkspaceLeaf
  scope: Scope;
  store;
  constructor(
    leaf: WorkspaceLeaf,
    public plugin: MediaExtended,
  ) {
    super(leaf);
    this.scope = new Scope(this.app.scope);
    this.store = createStore();
    this.contentEl.addClasses(["mx", "custom"]);
    registerMediaViewScopes(this);
    using stack = new DisposableStack();
    stack.use(addAction(this));
    stack.defer(updateHeader(this, { disableTitleUpdate: true }));
    const disposables = stack.move();
    this.register(() => disposables.dispose());
  }

  #reset() {
    this.#renderDisposables?.[Symbol.dispose]();
    this.#renderDisposables = null;
  }

  protected async onOpen(): Promise<void> {
    await super.onOpen();
    this.#reset();
    this.#renderDisposables = renderView({
      ctx: this,
      target: this.contentEl,
      children: (
        <Player
          captureScreenshot={(opts) => this.captureScreenshot(opts)}
          takeTimestamp={() => this.takeTimestamp()}
        />
      ),
    });
  }
  async onClose() {
    this.#reset();
    await super.onClose();
  }

  getPlayer(): MediaPlayerInstance | null {
    return this.store.get(playerAtom);
  }

  async onLoadFile(file: TFile): Promise<void> {
    const info = parseFile(file);
    if (!info) {
      throw new Error("File is not a media file");
    }
    this.store.set(
      mediaInfoAtom,
      this.plugin.mediaLib.getMediaMeta(info)?.src ?? info,
    );
  }
  onPaneMenu(menu: Menu, menuSource: PaneMenuSource): void {
    super.onPaneMenu(menu, menuSource);
    onPaneMenu(this, menu, menuSource);
  }

  setEphemeralState(state: any): void {
    const subpath = state?.subpath;
    if (typeof subpath === "string") {
      this.store.set(mediaHashAtom, parseHashProps(subpath));
    }
    super.setEphemeralState(state);
  }

  #renderDisposables: Disposable | null = null;

  // we don't want to interfere with file header,
  // so we don't implement getDisplayText
  getIcon(): string {
    return this.store.get(mediaIconAtom);
  }
  getMediaInfo(): FileMediaInfo | null {
    if (!this.file) return null;
    return { type: "file", file: this.file };
  }
  getViewType() {
    return MEDIA_FILE_VIEW_TYPE;
  }
  canAcceptExtension(extension: string): boolean {
    return isMediaFile({ extension });
  }
  async captureScreenshot(
    options: ScreenshotOptions,
  ): Promise<ScreenshotResult<FileMediaInfo>> {
    const player = this.store.get(playerAtom);
    const info = this.getMediaInfo();
    if (!player || !info) {
      throw new Error("Load player with video first");
    }
    if (!isVideoProvider(player.provider)) {
      throw new Error(
        `Screenshot is not supported for ${player.provider?.type || "this media"}`,
      );
    }
    const screenshot = await captureScreenshot(player.provider.video, options);
    return {
      info,
      blob: new Blob([screenshot.blob.arrayBuffer], {
        type: screenshot.blob.type,
      }),
      size: screenshot.size,
      timestamp: screenshot.timestamp,
    };
  }
  takeTimestamp() {
    const player = this.store.get(playerAtom);
    if (!player) {
      throw new Error("Load player with video first");
    }
    return { currentTime: player.currentTime };
  }
}
