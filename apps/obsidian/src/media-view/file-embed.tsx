import type { EmbedComponent, EmbedInfo, TFile } from "obsidian";
import { Component } from "obsidian";
import { Player } from "@/components/player";
import type MxPlugin from "@/mx-main";
import type { PlayerComponent } from "@/def/player-comp";
import { createStore } from "jotai";
import { renderView } from "@/media-view/_render";
import { mediaHashAtom, mediaInfoAtom } from "@/def/atom/media";
import { getMediaInfoID, type FileMediaInfo } from "@/def/media-info";
import { parseFile } from "@/def/file-parse";
import { parseHashProps } from "@mx/shared/utils/hash-prop";
import { playerAtom } from "@mx/shared/hooks/use-player";

export class FileMediaEmbed
  extends Component
  implements EmbedComponent, PlayerComponent
{
  store;
  public containerEl: HTMLElement;

  constructor(
    public info: EmbedInfo,
    public file: TFile,
    public subpath: string,
    public plugin: MxPlugin,
  ) {
    super();
    this.containerEl = info.containerEl;
    this.store = createStore();
    const { containerEl } = info;
    containerEl.addClasses(["mx", "mx-media-embed", "custom"]);
    // containerEl.style.display = "contents";
  }

  getPlayer() {
    return this.store.get(playerAtom);
  }

  getMediaInfo(): FileMediaInfo | null {
    const info = this.store.get(mediaInfoAtom);
    if (!info) return null;
    if (info.type !== "file") {
      console.warn("Unexpected media info in file embed", info);
      return null;
    }
    return info;
  }

  #renderDisposables: Disposable | null = null;

  render() {
    this.#renderDisposables = renderView({
      ctx: {
        store: this.store,
        plugin: this.plugin,
        isEmbed: true,
      },
      target: this.containerEl,
      children: (
        <Player
          checkInEditor={() => this.containerEl.matches(".cm-editor *")}
        />
      ),
    });
  }
  onload(): void {
    super.onload();
    this.render();
  }

  async loadFile() {
    const info = parseFile(this.file);
    if (!info) {
      throw new Error("File is not a media file");
    }
    this.store.set(mediaInfoAtom, (prev) => {
      if (prev && getMediaInfoID(prev) === getMediaInfoID(info)) return prev;
      return info;
    });
    this.store.set(mediaHashAtom, parseHashProps(this.subpath));
  }

  onunload() {
    // unmount before detach from DOM
    this.#renderDisposables?.[Symbol.dispose]();
    super.onunload();
  }
}
