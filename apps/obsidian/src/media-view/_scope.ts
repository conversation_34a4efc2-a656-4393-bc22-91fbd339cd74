import type { Hotkey, ItemView } from "obsidian";
import type { PlayerComponent } from "@/def/player-comp";
import { mediaRemoteAtom, playerAtom } from "@mx/shared/hooks/use-player";
import type { MediaPlayerInstance, MediaRemoteControl } from "@vidstack/react";

// implement the following keymaps
// The shortcuts can be specified as a space-separated list of combinations (e.g., p Control+Space), array of keys, or a callback. This property is loosely modelled after the aria-keyshortcuts attribute, see the link for more information and tips for picking good shortcuts.
// ---
// togglePaused: 'k Space',
// toggleMuted: 'm',
// toggleFullscreen: 'f',
// togglePictureInPicture: 'i',
// toggleCaptions: 'c',
// seekBackward: 'j J ArrowLeft',
// seekForward: 'l L ArrowRight',
// volumeUp: 'ArrowUp',
// volumeDown: 'ArrowDown',
// speedUp: '>',
// slowDown: '<',

export function registerMediaViewScopes(view: ItemView & PlayerComponent) {
  function registerAction(
    hotkeys: Hotkey[],
    action: (ctx: {
      remote: MediaRemoteControl;
      evt: KeyboardEvent;
      player: MediaPlayerInstance;
    }) => void,
  ) {
    const scope = view.scope;
    if (!scope) return;
    for (const hotkey of hotkeys) {
      scope.register(hotkey.modifiers, hotkey.key, (evt) => {
        const player = view.store.get(playerAtom);
        const remote = view.store.get(mediaRemoteAtom);
        if (!player || !remote) return;
        action({ remote, evt, player });
      });
    }
  }

  // togglePaused: 'k Space'
  registerAction(
    [
      { modifiers: [], key: " " },
      { modifiers: [], key: "K" },
    ],
    (ctx) => {
      ctx.remote.togglePaused(ctx.evt);
    },
  );

  // toggleMuted: 'm'
  registerAction([{ modifiers: [], key: "m" }], (ctx) => {
    ctx.remote.toggleMuted(ctx.evt);
  });

  // toggleFullscreen: 'f'
  registerAction([{ modifiers: [], key: "f" }], (ctx) => {
    ctx.remote.toggleFullscreen("prefer-media", ctx.evt);
  });

  // togglePictureInPicture: 'i'
  registerAction([{ modifiers: [], key: "i" }], (ctx) => {
    ctx.remote.togglePictureInPicture(ctx.evt);
  });

  // toggleCaptions: 'c'
  registerAction([{ modifiers: [], key: "c" }], (ctx) => {
    ctx.remote.toggleCaptions(ctx.evt);
  });

  // seekBackward: 'j J ArrowLeft'
  registerAction(
    [
      { modifiers: [], key: "j" },
      { modifiers: [], key: "ArrowLeft" },
    ],
    (ctx) => {
      const currentTime = ctx.player.currentTime;
      ctx.remote.seek(currentTime - 5, ctx.evt);
    },
  );

  // seekForward: 'l L ArrowRight'
  registerAction(
    [
      { modifiers: [], key: "l" },
      { modifiers: [], key: "ArrowRight" },
    ],
    (ctx) => {
      const currentTime = ctx.player.currentTime;
      ctx.remote.seek(currentTime + 5, ctx.evt);
    },
  );

  // volumeUp: 'ArrowUp'
  registerAction([{ modifiers: [], key: "ArrowUp" }], (ctx) => {
    const newVolume = clampVolume(roundVolume(ctx.player.volume + 0.05));
    ctx.remote.changeVolume(newVolume, ctx.evt);
  });

  // volumeDown: 'ArrowDown'
  registerAction([{ modifiers: [], key: "ArrowDown" }], (ctx) => {
    const newVolume = clampVolume(roundVolume(ctx.player.volume - 0.05));
    ctx.remote.changeVolume(newVolume, ctx.evt);
  });

  // speedUp: '>' (Shift + .)
  registerAction([{ modifiers: ["Shift"], key: "." }], (ctx) => {
    const newRate = roundPlaybackRate(ctx.player.playbackRate + 0.1);
    ctx.remote.changePlaybackRate(newRate, ctx.evt);
  });

  // slowDown: '<' (Shift + ,)
  registerAction([{ modifiers: ["Shift"], key: "," }], (ctx) => {
    const playbackRate = ctx.player.playbackRate;
    if (playbackRate <= 0.1) return;
    const newRate = roundPlaybackRate(playbackRate - 0.1);
    ctx.remote.changePlaybackRate(newRate, ctx.evt);
  });
}

function roundVolume(volume: number): number {
  return Math.round(volume * 100) / 100;
}

function roundPlaybackRate(rate: number): number {
  return Math.round(rate * 10) / 10;
}

function clampVolume(volume: number): number {
  return Math.max(0, Math.min(1, volume));
}
