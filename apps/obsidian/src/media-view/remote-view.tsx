import type { PlayerComponent, ScreenshotResult } from "@/def/player-comp";
import type { Workspace<PERSON>ea<PERSON>, <PERSON>u, ViewStateResult } from "obsidian";
import { ItemView, Scope } from "obsidian";
import type MediaExtended from "@/mx-main";
import { createStore } from "jotai";
import type { PaneMenuSource } from "@/lib/menu";
import { mediaInfoAtom, pluginAtom } from "@/def/atom/media";
import {
  isMediaInfoEqual,
  isBrowserMediaInfo,
  type BrowserMediaInfo,
} from "@/def/media-info";
import { parseHashProps } from "@mx/shared/utils/hash-prop";
import type { ScreenshotOptions } from "@mx/shared/dom/screenshot";
import { renderView } from "@/media-view/_render";
import { decodeBase64 } from "@std/encoding";
import { PlayerRemote } from "@/components/player-remote";
import { isTimestamp } from "@mx/shared/time/temporal-frag";
import type { MediaPlayerInstance } from "@vidstack/react";
import { addAction, onPaneMenu, updateHeader } from "./_action";
import { mediaIconAtom } from "@/def/atom/media-icon";
import { mediaMetaAtom } from "@/def/atom/media-meta";
import { deserialize, serialize } from "./_state";
import { registerMediaViewScopes } from "./_scope";

export const MEDIA_REMOTE_VIEW_TYPE = "mx-remote";

export type MediaRemoteViewType = typeof MEDIA_REMOTE_VIEW_TYPE;

export interface MediaRemoteViewState {
  media?: BrowserMediaInfo;
}

export default class MediaRemoteView
  extends ItemView
  implements PlayerComponent
{
  // no need to manage scope manually,
  // as it's implicitly called and handled by the WorkspaceLeaf
  scope: Scope;
  navigation = true;
  store;

  getPlayer(): MediaPlayerInstance | null {
    return null;
  }

  getViewType(): MediaRemoteViewType {
    return MEDIA_REMOTE_VIEW_TYPE;
  }
  getDisplayText(): string {
    return this.store.get(mediaMetaAtom).title;
  }
  getIcon(): string {
    return this.store.get(mediaIconAtom);
  }
  getMediaInfo(): BrowserMediaInfo | null {
    const info = this.store.get(mediaInfoAtom);
    if (info && isBrowserMediaInfo(info)) return info;
    return null;
  }

  constructor(
    leaf: WorkspaceLeaf,
    public plugin: MediaExtended,
  ) {
    super(leaf);
    this.scope = new Scope(this.app.scope);
    this.store = createStore();
    this.store.set(pluginAtom, this.plugin);
    this.contentEl.addClasses(["mx", "custom"]);
    registerMediaViewScopes(this);
    using stack = new DisposableStack();
    stack.use(addAction(this));
    stack.defer(updateHeader(this));
    const disposables = stack.move();
    this.register(() => disposables.dispose());
  }

  render(): Disposable {
    return renderView({
      ctx: this,
      target: this.contentEl,
      children: (
        <PlayerRemote
          onScreenshot={() =>
            this.captureScreenshot({}).then((o) => {
              console.log("screenshot", o);
            })
          }
          onPlayPause={(info) =>
            this.plugin.rpc?.controlPlayback(info.vid, {
              type: "play-pause",
              paused: null,
            })
          }
          onMute={(info) =>
            this.plugin.rpc?.controlPlayback(info.vid, {
              type: "set-muted",
              muted: true,
            })
          }
          onUnmute={(info) =>
            this.plugin.rpc?.controlPlayback(info.vid, {
              type: "set-muted",
              muted: false,
            })
          }
        />
      ),
    });
  }

  onPaneMenu(menu: Menu, menuSource: PaneMenuSource): void {
    super.onPaneMenu(menu, menuSource);
    onPaneMenu(this, menu, menuSource);
  }

  getState(): Record<string, unknown> {
    const state = super.getState() as MediaRemoteViewState;
    const info = this.store.get(mediaInfoAtom);
    if (!info) return state as any;
    if (!isBrowserMediaInfo(info)) {
      console.warn("Unexpected media info in remote view", info);
      return state as any;
    }
    return { ...state, ...serialize<MediaRemoteViewState>({ media: info }) };
  }

  async setState(state: any, result: ViewStateResult): Promise<void> {
    await super.setState(state, result);
    const viewState = deserialize<MediaRemoteViewState>(state);
    if (!viewState?.media) return;
    const current = this.store.get(mediaInfoAtom);
    if (!isMediaInfoEqual(current, state.media)) {
      this.store.set(
        mediaInfoAtom,
        this.plugin.mediaLib.getMediaMeta(state.media)?.src ?? state.media,
      );
    }
  }
  setEphemeralState(state: any): void {
    const subpath = state?.subpath;
    if (typeof subpath === "string") {
      // this.store.set(mediaHashAtom, parseHashProps(subpath));
      const hash = parseHashProps(subpath);
      const info = this.getMediaInfo();
      if (isTimestamp(hash.tempFragment) && info) {
        this.plugin.rpc?.controlPlayback(info.vid, {
          type: "seek",
          time: hash.tempFragment.start,
        });
      }
    }
    super.setEphemeralState(state);
  }

  protected async onOpen(): Promise<void> {
    await super.onOpen();
    this.#reset();
    this.#renderDisposables = this.render();
  }

  #renderDisposables: Disposable | null = null;
  #reset() {
    this.#renderDisposables?.[Symbol.dispose]();
    this.#renderDisposables = null;
  }

  async onClose() {
    this.#reset();
    return super.onClose();
  }

  async captureScreenshot(
    options: ScreenshotOptions,
  ): Promise<ScreenshotResult<BrowserMediaInfo>> {
    const info = this.getMediaInfo();
    const rpc = this.plugin.rpc;
    if (!info || !rpc) {
      throw new Error("Load player with video first");
    }
    const screenshot = await rpc.captureScreenshot(info.vid, options);
    const ab = decodeBase64(screenshot.blob.arrayBufferBase64);
    return {
      info,
      blob: new Blob([ab], { type: screenshot.blob.type }),
      size: screenshot.size,
      timestamp: screenshot.timestamp,
    };
  }
}
