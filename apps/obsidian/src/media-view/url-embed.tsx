import { MarkdownRenderChild } from "obsidian";
import { Player } from "@/components/player";
import type MxPlugin from "@/mx-main";
import {
  isUrlMediaInfo,
  type MediaInfoSrc,
  type UrlMediaInfo,
} from "@/def/media-info";
import type { PlayerComponent } from "@/def/player-comp";
import { createStore } from "jotai";
import { mediaHashAtom, mediaInfoAtom } from "@/def/atom/media";
import { renderView } from "@/media-view/_render";
import { mediaTitleAtom } from "@/def/atom/media-meta";
import { equal } from "@std/assert";
import { playerAtom } from "@mx/shared/hooks/use-player";

export class MediaRenderChild
  extends MarkdownRenderChild
  implements PlayerComponent
{
  store;
  containerEl;
  plugin;
  onEditClick;

  constructor(
    containerEl: HTMLElement,
    plugin: MxPlugin,
    onEditClick?: () => void,
  ) {
    super(containerEl);
    this.containerEl = containerEl;
    this.plugin = plugin;
    this.onEditClick = onEditClick;
    this.store = createStore();
    containerEl.addClasses(["mx", "custom", "mx-media-embed"]);
  }

  getMediaInfo() {
    const info = this.store.get(mediaInfoAtom);
    if (!info) return null;
    if (!isUrlMediaInfo(info)) {
      console.warn("Unexpected media info in url embed", info);
      return null;
    }
    return info;
  }

  getPlayer() {
    return this.store.get(playerAtom);
  }

  async setSource(
    media: MediaInfoSrc<UrlMediaInfo>,
    other: Partial<{ title: string }> = {},
  ) {
    if (other.title) {
      this.store.set(mediaTitleAtom, other.title);
    }
    this.store.set(mediaInfoAtom, (prev) => {
      const curr =
        this.plugin.mediaLib.getMediaMeta(media.info)?.src ?? media.info;
      if (equal(prev, curr)) return prev;
      return curr;
    });
    this.store.set(mediaHashAtom, media.hash);
  }

  #renderDisposables: Disposable | null = null;
  render() {
    this.#renderDisposables = renderView({
      ctx: {
        store: this.store,
        plugin: this.plugin,
        isEmbed: true,
      },
      target: this.containerEl,
      children: <Player onEditClick={this.onEditClick} />,
    });
  }
  onload(): void {
    super.onload();
    this.render();
  }

  onunload() {
    // unmount before detach from DOM
    this.#renderDisposables?.[Symbol.dispose]();
    super.onunload();
  }
}
