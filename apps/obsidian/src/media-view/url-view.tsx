import type { PlayerComponent, ScreenshotResult } from "@/def/player-comp";
import { around } from "monkey-around";
import type { WorkspaceLeaf, <PERSON>u, ViewStateResult } from "obsidian";
import { Item<PERSON>ie<PERSON>, Scope } from "obsidian";
import type MediaExtended from "@/mx-main";
import { createStore } from "jotai";
import type { PaneMenuSource } from "@/lib/menu";
import { handleWindowMigration } from "@/lib/window-migration";
import { mediaHashAtom, mediaInfoAtom, pluginAtom } from "@/def/atom/media";
import {
  isMediaInfoEqual,
  isUrlMediaInfo,
  type UrlMediaInfo,
} from "@/def/media-info";
import { Player } from "../components/player";
import { parseHashProps } from "@mx/shared/utils/hash-prop";
import {
  captureScreenshot,
  type ScreenshotOptions,
} from "@mx/shared/dom/screenshot";
import { player<PERSON><PERSON> } from "@mx/shared/hooks/use-player";
import { isVideoProvider, type MediaPlayerInstance } from "@vidstack/react";
import { renderView } from "@/media-view/_render";
import { addAction, onPaneMenu, updateHeader } from "./_action";
import { mediaIconAtom } from "@/def/atom/media-icon";
import { mediaMetaAtom } from "@/def/atom/media-meta";

import { deserialize, serialize } from "./_state";
import { registerMediaViewScopes } from "./_scope";

export const MEDIA_URL_VIEW_TYPE = "mx-url";

export type MediaUrlViewType = typeof MEDIA_URL_VIEW_TYPE;

export interface MediaUrlViewState {
  media?: UrlMediaInfo;
}

export default class MediaUrlView extends ItemView implements PlayerComponent {
  // no need to manage scope manually,
  // as it's implicitly called and handled by the WorkspaceLeaf
  scope: Scope;
  navigation = true;
  store;

  getViewType(): MediaUrlViewType {
    return MEDIA_URL_VIEW_TYPE;
  }
  getDisplayText(): string {
    return this.store.get(mediaMetaAtom).title;
  }
  getIcon(): string {
    return this.store.get(mediaIconAtom);
  }
  getMediaInfo(): UrlMediaInfo | null {
    const info = this.store.get(mediaInfoAtom);
    if (info && isUrlMediaInfo(info)) return info;
    return null;
  }

  constructor(
    leaf: WorkspaceLeaf,
    public plugin: MediaExtended,
  ) {
    super(leaf);
    this.scope = new Scope(this.app.scope);
    this.store = createStore();
    this.store.set(pluginAtom, this.plugin);
    this.contentEl.addClasses(["mx", "custom"]);
    registerMediaViewScopes(this);
    using stack = new DisposableStack();
    stack.use(addAction(this));
    stack.defer(updateHeader(this));
    const disposables = stack.move();
    this.register(() => disposables.dispose());
  }

  getPlayer(): MediaPlayerInstance | null {
    return this.store.get(playerAtom);
  }

  onload(): void {
    const self = this;
    super.onload();
    // handleTrackUpdate.call(this);
    this.register(
      around(this.leaf, {
        // make sure to unmount the player before the leaf detach it from DOM
        detach: (next) =>
          function (this: WorkspaceLeaf, ...args) {
            self.#renderDisposables?.[Symbol.dispose]();
            return next.call(this, ...args);
          },
      }),
    );

    handleWindowMigration(this, () => this.render());
  }

  render(): Disposable {
    return renderView({
      ctx: this,
      target: this.contentEl,
      children: (
        <Player
          captureScreenshot={(opts) => this.captureScreenshot(opts)}
          takeTimestamp={() => this.takeTimestamp()}
        />
      ),
    });
  }

  onPaneMenu(menu: Menu, menuSource: PaneMenuSource): void {
    super.onPaneMenu(menu, menuSource);
    onPaneMenu(this, menu, menuSource);
  }

  getState(): Record<string, unknown> {
    const state = super.getState() as MediaUrlViewState;
    const info = this.store.get(mediaInfoAtom);
    if (!info) return state as any;
    if (!isUrlMediaInfo(info)) {
      console.warn("Unexpected media info in remote view", info);
      return state as any;
    }
    return { ...state, ...serialize<MediaUrlViewState>({ media: info }) };
  }

  async setState(state: any, result: ViewStateResult): Promise<void> {
    await super.setState(state, result);
    const viewState = deserialize<MediaUrlViewState>(state);
    if (!viewState?.media) return;
    const current = this.store.get(mediaInfoAtom);
    if (!isMediaInfoEqual(current, viewState.media)) {
      this.store.set(
        mediaInfoAtom,
        this.plugin.mediaLib.getMediaMeta(viewState.media)?.src ??
          viewState.media,
      );
    }
  }
  setEphemeralState(state: any): void {
    const subpath = state?.subpath;
    if (typeof subpath === "string") {
      this.store.set(mediaHashAtom, parseHashProps(subpath));
    }
    super.setEphemeralState(state);
  }

  protected async onOpen(): Promise<void> {
    await super.onOpen();
    this.#reset();
    this.#renderDisposables = this.render();
  }

  #renderDisposables: Disposable | null = null;
  #reset() {
    this.#renderDisposables?.[Symbol.dispose]();
    this.#renderDisposables = null;
  }

  close() {
    this.#reset();
    // @ts-expect-error -- this would call leaf.detach()
    return super.close();
  }
  async onClose() {
    this.#reset();
    return super.onClose();
  }

  async captureScreenshot(
    options: ScreenshotOptions,
  ): Promise<ScreenshotResult<UrlMediaInfo>> {
    const player = this.store.get(playerAtom);
    const info = this.getMediaInfo();
    if (!player || !info) {
      throw new Error("Load player with video first");
    }
    if (!isVideoProvider(player.provider)) {
      throw new Error(
        `Screenshot is not supported for ${player.provider?.type || "this media"}`,
      );
    }
    const screenshot = await captureScreenshot(player.provider.video, options);
    return {
      info,
      blob: new Blob([screenshot.blob.arrayBuffer], {
        type: screenshot.blob.type,
      }),
      size: screenshot.size,
      timestamp: screenshot.timestamp,
    };
  }
  takeTimestamp() {
    const player = this.store.get(playerAtom);
    if (!player) {
      throw new Error("Load player with video first");
    }
    return { currentTime: player.currentTime };
  }
}
