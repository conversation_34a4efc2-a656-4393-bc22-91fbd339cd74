import { ShadowRoot } from "@/components/shadow-dom";
import { type createStore, Provider } from "jotai";
import ReactDOM from "react-dom/client";
import type MediaExtended from "@/mx-main";
import AtomsHydrator from "@/components/atom-hydrator";
import { embed<PERSON>lag<PERSON>tom, pluginAtom } from "@/def/atom/media";

export function renderView({
  children,
  target,
  shadowRootClassName,
  ctx: { store, plugin, isEmbed },
}: {
  children: React.ReactNode;
  target: HTMLElement;
  ctx: {
    store: ReturnType<typeof createStore>;
    plugin: MediaExtended;
    isEmbed?: boolean;
  };
  shadowRootClassName?: string;
}): Disposable {
  using stack = new DisposableStack();
  const root = ReactDOM.createRoot(target);
  stack.defer(() => {
    root.unmount();
  });

  root.render(
    <ShadowRoot className={shadowRootClassName}>
      <Provider store={store}>
        <AtomsHydrator
          atomValues={[
            [plugin<PERSON><PERSON>, plugin],
            [embedFlag<PERSON>tom, isEmbed],
          ]}
        >
          {children}
        </AtomsHydrator>
      </Provider>
    </ShadowRoot>,
  );

  return stack.move();
}
