.login-modal-content {
	padding: 1.5rem;
	max-width: 400px;
	margin: 0 auto;
}

.login-header {
	text-align: center;
	margin-bottom: 2rem;
}

.login-header h3 {
	margin: 0 0 0.5rem 0;
	font-size: 1.5rem;
	font-weight: 600;
	color: var(--text-normal);
}

.login-subtitle {
	margin: 0;
	color: var(--text-muted);
	font-size: 0.875rem;
	line-height: 1.4;
}

.login-features {
	display: flex;
	flex-direction: column;
	gap: 1.25rem;
	margin-bottom: 2rem;
}

.login-feature-item {
	display: flex;
	align-items: flex-start;
	gap: 1rem;
}

.login-feature-icon {
	flex-shrink: 0;
	margin-top: 2px;
	color: var(--interactive-accent);
}

.login-feature-icon svg {
	width: 20px;
	height: 20px;
}

.login-feature-text {
	text-align: left;
}

.login-feature-text strong {
	display: block;
	font-weight: 600;
	color: var(--text-normal);
	font-size: 0.875rem;
	margin-bottom: 0.25rem;
}

.login-feature-text p {
	margin: 0;
	font-size: 0.875rem;
	color: var(--text-muted);
	line-height: 1.4;
}

.login-options {
	display: flex;
	flex-direction: column;
	gap: 0.75rem;
	margin-bottom: 1.5rem;
}

.login-button-container {
	width: 100%;
}

/* Minimal styling for login buttons using Obsidian's native ButtonComponent */
.login-button {
	width: 100%;
	justify-content: center;
	min-height: 44px;
	display: flex;
	align-items: center;
	gap: 0.75rem;
	position: relative;
  cursor: pointer;
}

/* Icon styling within buttons */
.login-button .svg-icon {
	flex-shrink: 0;
	width: 20px;
	height: 20px;
}

/* Specific styling for Google login button */
.login-button.google-login {
	background: var(--background-primary);
	color: var(--text-normal);
}

.login-button.google-login:hover {
	background: var(--background-modifier-hover);
}

/* Specific styling for GitHub login button */
.login-button.github-login {
	background: var(--text-normal);
	color: var(--background-primary);
}

.login-button.github-login:hover {
	background: var(--text-muted);
}

.login-button.github-login .svg-icon {
	color: inherit;
}

/* Divider styling */
.login-divider {
	display: flex;
	align-items: center;
	margin: 1.5rem 0;
	gap: 1rem;
}

.login-divider-line {
	flex: 1;
	height: 1px;
	background: var(--background-modifier-border);
}

.login-divider-text {
	font-size: 0.75rem;
	color: var(--text-muted);
	text-transform: uppercase;
	letter-spacing: 0.5px;
}

/* Email section styling */
.login-email-section {
	margin-bottom: 1rem;
}

.login-email-form {
	display: flex;
	flex-direction: column;
	gap: 1rem;
}

.login-label {
	display: block;
	margin-bottom: 0.5rem;
	font-size: 0.875rem;
	font-weight: 500;
	color: var(--text-normal);
}

.login-input {
	width: 100%;
	padding: 0.875rem 1rem;
	border: 1px solid var(--background-modifier-border);
	border-radius: 0.5rem;
	background: var(--background-primary);
	font-size: 0.875rem;
	color: var(--text-normal);
	transition: border-color 0.2s ease;
	box-sizing: border-box;
}

.login-input:focus {
	outline: none;
	border-color: var(--interactive-accent);
	box-shadow: 0 0 0 2px var(--interactive-accent-hover);
}

.login-input::placeholder {
	color: var(--text-muted);
}

/* Footer styling */
.login-footer {
	margin-top: 2rem;
	text-align: center;
}

.login-terms {
	margin: 0;
	font-size: 0.75rem;
	color: var(--text-muted);
	line-height: 1.4;
  text-wrap: balance;
}


/* Responsive adjustments */
@media (max-width: 480px) {
	.login-modal-content {
		padding: 1rem;
	}
	
	.login-header h3 {
		font-size: 1.25rem;
	}
}

/* Focus styles for accessibility */
.login-input:focus-visible {
	outline: none;
	border-color: var(--interactive-accent);
	box-shadow: 0 0 0 2px var(--interactive-accent-hover);
}
