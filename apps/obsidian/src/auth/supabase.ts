import { Platform } from "obsidian";
import { createClient } from "@supabase/supabase-js";
import { requireElectronSafeStorage } from "@/lib/node";

export const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_ANON_KEY,
  {
    auth: {
      detectSessionInUrl: false,
      flowType: "pkce",
      persistSession: true,
      autoRefreshToken: false,
      storageKey: "mx-auth-session",
      storage: (() => {
        if (!Platform.isDesktopApp) {
          return localStorage;
        }
        const safeStorage = requireElectronSafeStorage();
        return {
          isServer: false,
          getItem: (key) => {
            const value = localStorage.getItem(key);
            if (!value) {
              return null;
            }
            const buffer = Buffer.from(value, "base64");
            return safeStorage.decryptString(buffer);
          },
          setItem: (key, value) => {
            const buffer = safeStorage.encryptString(value);
            localStorage.setItem(key, buffer.toString("base64"));
          },
          removeItem: (key) => {
            localStorage.removeItem(key);
          },
        };
      })(),
    },
  },
);
