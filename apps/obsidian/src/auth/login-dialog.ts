import { Mo<PERSON>, ButtonComponent, Notice, type App } from "obsidian";
import "./styles.css";
import { supabase } from "./supabase";
import { AUTH_CALLBACK_ACTION } from "./service";
import { openUrlExternally } from "@/lib/url-open";
import type MxPlugin from "@/mx-main";
import type { User } from "@supabase/supabase-js";

export class LoginDialog extends Modal {
  private constructor(app: App, onClose: () => void) {
    super(app);
    this.onClose = onClose.bind(this);
  }

  static async open(plugin: MxPlugin) {
    using disposables = new DisposableStack();
    try {
      const user = await new Promise<User | null>((resolve, reject) => {
        const dialog = new LoginDialog(plugin.app, () => resolve(null));
        dialog.open();
        disposables.defer(() => {
          dialog.close();
        });
        disposables.defer(
          plugin.auth.on("oauth-login:success", (user) => resolve(user)),
        );
        disposables.defer(
          plugin.auth.on("oauth-login:error", (error) => reject(error)),
        );
      });
      if (!user) return;
      const name = user.user_metadata?.full_name || user.email?.split("@")[0];
      new Notice(name ? `Welcome ${name}!` : "Welcome to Media Extended!");
    } catch (e) {
      new Notice("Error logging in, see console for details");
    }
  }

  onOpen() {
    // this.setTitle("Media Extended");
    this.createLoginContent();
  }

  onClose() {
    this.contentEl.empty();
  }

  private createLoginContent() {
    const { contentEl } = this;
    contentEl.empty();
    contentEl.addClass("login-modal-content");

    // Header
    const header = contentEl.createDiv("login-header");
    header.createEl("h3", { text: "Connect to Media Extended" });
    header.createEl("p", {
      text: "Sign in or create an account to access online features",
      cls: "login-subtitle",
    });

    // Features list
    // const featuresContainer = contentEl.createDiv("login-features");

    // const features = [
    //   {
    //     icon: "captions",
    //     title: "AI-Powered Transcripts & Summaries",
    //     description:
    //       "Automatic transcription and chapter generation for your media.",
    //   },
    //   {
    //     icon: "globe",
    //     title: "Browser Connector",
    //     description: "Sync playback with YouTube and other supported websites.",
    //   },
    //   {
    //     icon: "scan-text",
    //     title: "Screenshot OCR",
    //     description: "Extract text from screenshots effortlessly.",
    //   },
    // ];

    // for (const feature of features) {
    //   const featureItem = featuresContainer.createDiv("login-feature-item");
    //   const iconEl = featureItem.createDiv("login-feature-icon");
    //   setIcon(iconEl, feature.icon);
    //   const textEl = featureItem.createDiv("login-feature-text");
    //   textEl.createEl("strong", { text: feature.title });
    //   textEl.createEl("p", { text: feature.description });
    // }

    // Login options container
    const loginOptionsContainer = contentEl.createDiv("login-options");

    // Google login button
    const googleButtonContainer = loginOptionsContainer.createDiv(
      "login-button-container",
    );
    const googleButton = new ButtonComponent(googleButtonContainer)
      .setButtonText("Continue with Google")
      .setIcon("google")
      .setDisabled(true)
      .onClick(() => {
        this.handleGoogleLogin();
      });
    googleButton.buttonEl.appendText("Continue with Google");
    googleButton.buttonEl.addClass("login-button", "google-login");

    // GitHub login button
    const githubButtonContainer = loginOptionsContainer.createDiv(
      "login-button-container",
    );
    const githubButton = new ButtonComponent(githubButtonContainer)
      .setIcon("github")
      .onClick(() => {
        this.handleGithubLogin();
      });
    githubButton.buttonEl.appendText("Continue with GitHub");
    githubButton.buttonEl.addClass("login-button", "github-login");

    // Divider
    // const divider = contentEl.createDiv("login-divider");
    // divider.createDiv("login-divider-line");
    // divider.createDiv("login-divider-text").setText("or");
    // divider.createDiv("login-divider-line");

    // // Email input section
    // const emailSection = contentEl.createDiv("login-email-section");
    // const emailForm = emailSection.createEl("form", {
    //   cls: "login-email-form",
    // });

    // emailForm.createEl("label", {
    //   text: "Email address",
    //   cls: "login-label",
    // });
    // const emailInput = emailForm.createEl("input", {
    //   type: "email",
    //   placeholder: "Enter your email address",
    //   cls: "login-input",
    //   attr: {
    //     required: "true",
    //     name: "email",
    //   },
    // });

    // // Continue button
    // const continueButton = emailForm.createEl("button", {
    //   type: "submit",
    //   cls: "login-button primary-login",
    // });
    // continueButton
    //   .createDiv("login-button-text")
    //   .setText("Continue with Email");

    // emailForm.addEventListener("submit", (e) => {
    //   e.preventDefault();
    //   this.handleEmailLogin(emailInput.value);
    // });

    // Footer
    const footer = contentEl.createDiv("login-footer");
    const termsText = footer.createEl("p", {
      cls: "login-terms",
    });
    termsText.appendText("By continuing, you agree to our ");
    termsText.createEl("a", {
      text: "Terms of Service",
      href: "#",
      cls: "login-link",
    });
    termsText.appendText(" and ");
    termsText.createEl("a", {
      text: "Privacy Policy",
      href: "#",
      cls: "login-link",
    });
  }

  private handleGoogleLogin() {
    // TODO: Implement Google OAuth
    console.log("Google login clicked");
  }

  private async handleGithubLogin() {
    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: "github",
      options: {
        skipBrowserRedirect: true,
        redirectTo: `obsidian://${AUTH_CALLBACK_ACTION}`,
      },
    });
    if (error) {
      console.error("Error signing in with GitHub:", error);
      new Notice("Error signing in with GitHub");
    } else {
      openUrlExternally(data.url);
    }
  }

  private handleEmailLogin(email: string) {
    // TODO: Implement email login
    console.log("Email login clicked:", email);
  }
}
