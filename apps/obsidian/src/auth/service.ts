import type MxPlugin from "@/mx-main";
import { supabase } from "./supabase";
import { Component, Notice } from "obsidian";
import { createEventEmitter } from "@mx/shared/utils/event";
import type { User } from "@supabase/supabase-js";
import { LoginDialog } from "./login-dialog";

interface Events {
  "oauth-login:success": (user: User) => void;
  "oauth-login:error": (error: Error) => void;
}

export const AUTH_CALLBACK_ACTION = "mx-auth-callback";

export class AuthService extends Component {
  plugin;
  constructor(plugin: MxPlugin) {
    super();
    this.plugin = plugin;
  }

  #emitter = createEventEmitter<Events>();

  onload(): void {
    this.#setupAuthRefresh();
    this.#setupAuthCallback();
    this.#setupCommand();
  }

  on<E extends keyof Events>(event: E, callback: Events[E]) {
    return this.#emitter.on(event, callback);
  }

  #setupAuthCallback() {
    this.plugin.registerObsidianProtocolHandler(
      AUTH_CALLBACK_ACTION,
      async ({ code }) => {
        if (!code) {
          console.error("No code provided from PKCE callback");
          return;
        }
        console.debug("Auth callback received", code);
        const { data, error } =
          await supabase.auth.exchangeCodeForSession(code);
        if (error) {
          console.error("Error exchanging code for session", error);
          this.#emitter.emit("oauth-login:error", error);
        } else {
          console.info("Media Extended: User logged in via OAuth");
          this.#emitter.emit("oauth-login:success", data.user);
        }
      },
    );
  }

  #setupCommand() {
    this.plugin.addCommand({
      id: "mx-auth-logout",
      name: "Logout",
      callback: async () => {
        const { error } = await supabase.auth.signOut();
        if (error) {
          console.error("Error signing out", error);
          new Notice(`Error signing out: ${error.message}`);
        } else {
          new Notice("You have been signed out");
        }
      },
    });
    this.plugin.addCommand({
      id: "mx-auth-login",
      name: "Login",
      icon: "log-in",
      callback: async () => {
        const session = (await supabase.auth.getSession()).data.session;
        if (session) {
          new Notice(`You are already logged in as ${session.user.email}`);
          return;
        }
        await LoginDialog.open(this.plugin);
      },
    });
  }

  #setupAuthRefresh() {
    supabase.auth.startAutoRefresh();
    this.register(() => supabase.auth.stopAutoRefresh());
    this.registerDomEvent(document, "visibilitychange", () => {
      if (document.visibilityState === "visible") {
        supabase.auth.startAutoRefresh();
      } else {
        supabase.auth.stopAutoRefresh();
      }
    });
  }
}
