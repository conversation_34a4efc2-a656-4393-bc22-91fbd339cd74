export const CONN_SYMBOL = Symbol.for("mx:browser-conn");

export const CHANNEL_READY = "mx:browser-conn:ready";
export const CHANNEL_CONNECT = "mx:browser-conn:connect";
export const CHANNEL_DISCONNECT = "mx:browser-conn:disconnect";
export interface InitMessage {
  name: string;
}

import { version as pluginVersion } from "../../manifest.json";
export const CONN_API_VERSION =
  process.env.NODE_ENV === "development"
    ? `${pluginVersion}-dev`
    : pluginVersion;
