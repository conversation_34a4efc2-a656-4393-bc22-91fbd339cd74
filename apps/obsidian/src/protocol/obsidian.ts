import { around } from "monkey-around";
import type { ObsidianProtocolData } from "obsidian";
import { Notice } from "obsidian";
import type MxPlugin from "@/mx-main";
import { toURL } from "@mx/shared/utils/to-url";
import { parseUrl } from "@/def/url-parse";
import type { MediaInfoSrc, UrlMediaInfo } from "@/def/media-info";

declare global {
  // eslint-disable-next-line no-var
  var OBS_ACT: ((params: ObsidianProtocolData) => void) | undefined;
}

const ACTION = "mx-open";
export function registerProtocol(plugin: MxPlugin) {
  if (window.OBS_ACT) {
    plugin.register(
      around(window as { OBS_ACT: (params: ObsidianProtocolData) => void }, {
        OBS_ACT: (next) =>
          function OBS_ACT(
            this: unknown,
            params: ObsidianProtocolData,
            ...args: []
          ) {
            if (params.action.startsWith(`${ACTION}/`)) {
              handlePathnameProtocol(params);
              return;
            }
            return next.call(this, params, ...args);
          },
      }),
    );
  }

  plugin.registerObsidianProtocolHandler("mx-open", async (params) => {
    const urlInfo = parseUrl(params.url || "");
    if (!urlInfo) {
      new Notice(`Invalid URL to open in Media Extended: ${params.url}`);
      return;
    }
    await handleUrl(urlInfo);
  });

  async function handlePathnameProtocol(params: ObsidianProtocolData) {
    const urlInfo = parsePathnameUrl(params, ACTION);
    if (!urlInfo) {
      new Notice(`Invalid URL to open in Media Extended: ${params.action}`);
      return;
    }
    await handleUrl(urlInfo);
  }
  async function handleUrl(urlInfo: MediaInfoSrc<UrlMediaInfo>) {
    new Notice(
      createFragment((e) => {
        e.appendText("Opening URL from browser: ");
        e.createEl("br");
        const link = urlInfo.info.url.href;
        e.createEl("a", {
          text:
            link.length > 50
              ? `${link.slice(0, 25)}...${link.slice(-25)}`
              : link,
          href: link,
        });
      }),
    );
    await plugin.link.openMedia(urlInfo, { newLeaf: "tab" });
  }
}

function parsePathnameUrl(params: ObsidianProtocolData, actionPrefix: string) {
  const base = params.action.substring(actionPrefix.length + 1);
  const url = toURL(base);
  if (!url) return null;
  const search = new URLSearchParams(params);
  search.delete("action");
  url.search = search.toString();
  return parseUrl(url);
}
