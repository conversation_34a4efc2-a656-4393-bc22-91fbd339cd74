import type { TFile } from "obsidian";

export type MediaSourceFieldType = "media" | "video" | "audio";

export interface InternalLinkField {
  type: "internal";
  media: "video" | "audio";
  source: TFile;
  subpath: string;
  original: string;
}
export interface ExternalLinkField {
  type: "external";
  media: MediaSourceFieldType;
  source: URL;
  subpath: string;
  original: string;
  isSameSource: (src: string) => boolean;
}

declare module "obsidian" {
  interface MetadataCache {
    on(
      name: "mx:track-changed",
      callback: (trackIDs: Set<string>, mediaID: string) => any,
      ctx?: any,
    ): EventRef;
    trigger(
      name: "mx:track-changed",
      trackIDs: Set<string>,
      mediaID: string,
    ): void;
  }
}
