import { parseLinktext } from "obsidian";
import type { MetadataCache, CachedMetadata, TFile } from "obsidian";
import type { MediaInfo } from "@/def/media-info";
import type { MediaSourceFieldType } from "./def";
import { parseMediaNoteMeta, type MediaNoteMeta } from "./parse";
import { parseUrl } from "@/def/url-parse";
import { parseFile } from "@/def/file-parse";
import { MEDIA_ID_FIELD } from "@/def/media-id";
import type { MediaType } from "@/def/media-type";

export interface ParsedMediaNoteMetadata {
  src: MediaInfo;
  uid: string;
  meta: MediaNoteMeta;
}

export function getMediaMeta(
  file: TFile,
  { metadataCache }: { metadataCache: MetadataCache },
): ParsedMediaNoteMetadata | null {
  const meta = metadataCache.getFileCache(file);
  if (!meta) return null;
  const ctx = { metadataCache, sourcePath: file.path };

  const uid = meta.frontmatter?.[MEDIA_ID_FIELD];
  if (!uid || typeof uid !== "string") return null;

  // prefer explicit typed media
  const src = getMediaInfoFromMeta(meta, ctx);
  if (!src) return null;
  return {
    src,
    uid: uid.trim(),
    get meta() {
      return parseMediaNoteMeta(meta);
    },
  };
}

export function getMediaInfoFromMeta(
  meta: CachedMetadata,
  ctx: { metadataCache: MetadataCache; sourcePath: string },
) {
  return (
    getMediaInfoFromMetaInternal("video", meta, ctx) ??
    getMediaInfoFromMetaInternal("audio", meta, ctx) ??
    getMediaInfoFromMetaInternal("media", meta, ctx)
  );
}

function getMediaInfoFromMetaInternal(
  key: MediaSourceFieldType,
  meta: CachedMetadata,
  ctx: { metadataCache: MetadataCache; sourcePath: string },
): MediaInfo | null {
  const mediaType: MediaType | undefined = key !== "media" ? key : undefined;
  const { frontmatter, frontmatterLinks } = meta;
  if (!frontmatter || !(key in frontmatter)) return null;
  const fieldLink = frontmatterLinks?.find((link) => link.key === key);
  if (fieldLink) {
    // attempt to resolve as file link
    const { path: linkpath } = parseLinktext(fieldLink.link);
    const mediaFile = ctx.metadataCache.getFirstLinkpathDest(
      linkpath,
      ctx.sourcePath,
    );
    return parseFile(mediaFile, { mediaType });
  }
  const fieldValue = frontmatter[key];
  if (typeof fieldValue !== "string") return null;
  const src = parseUrl(fieldValue, { mediaType });
  if (!src) return null;
  return src.info;
}
