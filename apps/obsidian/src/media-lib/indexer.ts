import { type MetadataCache, type Vault, type App, TFile } from "obsidian";
import { Component } from "obsidian";
import { getMediaInfoID } from "@/def/media-info";
import type { MediaInfo } from "@/def/media-info";
import type MxPlugin from "@/mx-main";
import iterateFiles from "@/lib/iterate-files";
import waitUntilMetaInited from "@/lib/meta-resolve";

import type { ParsedMediaNoteMetadata } from "./extract";
import { getMediaMeta } from "./extract";
import { pick } from "@std/collections";
import {
  getMetaTrackInfoID,
  getTrackInfoID,
  resolveTrackLink,
  type MetaTextTrackInfo,
  type ResolveTrackLinkCtx,
  type TextTrackInfo,
} from "@/def/track-info";
import { createEventEmitter } from "@mx/shared/utils/event";

export interface MediaLibraryItemInit {
  title: string;
  fm: (newNotePath: string) => Record<string, any>;
  sourcePath?: string;
}

declare module "obsidian" {
  interface MetadataCache {
    on(name: "finished", callback: () => any, ctx?: any): EventRef;
    // on(name: "initialized", callback: () => any, ctx?: any): EventRef;
    initialized: boolean;
  }
}

export interface MediaLibItemCache {
  src: MediaInfo;
  uid: string;
}

/**
 * Note: we only emit events for those media saved in media lib,
 * aka those with media meta note
 */
export interface MediaLibIndexEvents {
  "media-meta-changed": (media: MediaLibItemCache, note: TFile) => void;
  "media-meta-removed": (media: MediaLibItemCache, note: TFile) => void;
}

export class MediaLibraryIndex extends Component {
  app;
  noteTo;
  noteFrom;

  #idxDisposables;
  constructor(public plugin: MxPlugin) {
    super();
    this.app = plugin.app;
    using stack = new DisposableStack();
    this.noteTo = stack.use(new NoteToIndex());
    this.noteFrom = stack.use(new NoteFromIndex());
    this.#idxDisposables = stack.move();
  }

  resetIndex() {
    using stack = new DisposableStack();
    this.noteTo = stack.use(new NoteToIndex());
    this.noteFrom = stack.use(new NoteFromIndex());
    this.#idxDisposables.dispose();
    this.#idxDisposables = stack.move();
  }

  [Symbol.dispose]() {
    this.#idxDisposables.dispose();
  }
  onunload(): void {
    this[Symbol.dispose]();
  }

  findTracksByMedia(media: MediaInfo): TextTrackInfo[] {
    const note = this.findNoteByMedia(media);
    if (!note) return [];
    const tracks = this.noteTo.getTracks(note.path, {
      metadataCache: this.app.metadataCache,
    });
    return tracks;
  }
  findMediaByTrack(track: TextTrackInfo): MediaLibItemCache[] {
    const notes = this.noteTo.getNotesFromTrack(track, {
      metadataCache: this.app.metadataCache,
      vault: this.app.vault,
    });
    return notes
      .map((note) => this.findMediaByNote(note))
      .filter((v) => v !== null);
  }

  findNoteByMedia(media: MediaInfo): TFile | null {
    return this.noteFrom.media.get(getMediaInfoID(media)) ?? null;
  }
  getMediaMeta(media: MediaInfo) {
    const note = this.findNoteByMedia(media);
    if (!note) return null;
    const meta = getMediaMeta(note, { metadataCache: this.app.metadataCache });
    if (!meta) return null;
    return {
      note,
      uid: meta.uid,
      // media info with preferred media type from metadata
      src: meta.src,
      get meta() {
        return meta.meta;
      },
    };
  }
  findMediaByNote(itemNote: TFile): MediaLibItemCache | null {
    return this.noteTo.getMedia(itemNote.path) ?? null;
  }

  /**
   * Find a note by its media UID
   * @param uid The media UID from frontmatter
   * @returns The note file or null if not found
   */
  findNoteByUid(uid: string): TFile | null {
    return this.noteFrom.uid.get(uid) ?? null;
  }

  /**
   * Find media info by its UID
   * @param uid The media UID from frontmatter
   * @returns The media info or null if not found
   */
  findMediaByUid(uid: string): MediaLibItemCache | null {
    const note = this.findNoteByUid(uid);
    if (!note) return null;
    return this.findMediaByNote(note);
  }

  #removeNote(note: TFile) {
    const removedData = this.noteTo.remove(note);
    if (!removedData) return;

    if (removedData.media) {
      this.noteFrom.remove([removedData.media]);
    }
    this.#emitter.emit("media-meta-removed", removedData.media, note);
  }
  #addNote(newNote: TFile, data: ParsedMediaNoteMetadata) {
    const mediaID = getMediaInfoID(data.src);
    const prevNote = this.noteFrom.media.get(mediaID);
    if (
      prevNote &&
      prevNote !== newNote &&
      prevNote.stat.ctime <= newNote.stat.ctime
    )
      return;
    this.noteTo.add(newNote, data);
    this.noteFrom.addNote(newNote, data);
    this.#emitter.emit("media-meta-changed", data, newNote);
  }

  #emitter = createEventEmitter<MediaLibIndexEvents>();

  on<E extends keyof MediaLibIndexEvents>(
    event: E,
    callback: MediaLibIndexEvents[E],
  ) {
    return this.#emitter.on(event, callback);
  }

  #onResolved() {
    this.resetIndex();
    const ctx = {
      metadataCache: this.app.metadataCache,
      vault: this.app.vault,
      plugin: this.plugin,
    };
    for (const { file, meta } of iterateMediaNote(ctx)) {
      this.#addNote(file, meta);
    }
    this.registerEvent(
      this.app.metadataCache.on("changed", (file) => {
        const meta = getMediaMeta(file, ctx);
        if (!meta) {
          this.#removeNote(file);
        } else {
          this.#addNote(file, meta);
        }
      }),
    );
    this.registerEvent(
      this.app.metadataCache.on("deleted", (file) => {
        this.#removeNote(file);
      }),
    );
    this.registerEvent(
      this.app.vault.on("rename", (file, oldPath) => {
        if (file instanceof TFile) {
          this.noteTo.rename(oldPath, file);
        }
        // noteFrom don't need to update
        // since TFile pointer is not changed
      }),
    );
  }

  onload(): void {
    waitUntilMetaInited(this.app.metadataCache, this).then(() => {
      this.#onResolved();
    });
  }
}

interface NoteToIndexEvents {
  "tracks-changed": (updatedTracks: MetaTextTrackInfo[], note: TFile) => void;
  "media-changed": (
    oldMedia: MediaLibItemCache,
    newMedia: MediaLibItemCache | null,
    note: TFile,
  ) => void;
}

// media - note map is one-to-one
class NoteToIndex implements Disposable {
  #data = new Map<
    string,
    { media: MediaLibItemCache; tracks: MetaTextTrackInfo[] }
  >();

  #emitter = createEventEmitter<NoteToIndexEvents>();
  #evtDisposables = new DisposableStack();

  on<E extends keyof NoteToIndexEvents>(
    event: E,
    callback: NoteToIndexEvents[E],
  ) {
    return this.#evtDisposables.use({
      [Symbol.dispose]: this.#emitter.on(event, callback),
    });
  }

  add(note: TFile, data: ParsedMediaNoteMetadata) {
    const oldTrackIdMap = new Map(
      this.#data
        .get(note.path)
        ?.tracks.map((track) => [getMetaTrackInfoID(track), track]) ?? [],
    );
    const newTrackIdMap = new Map(
      data.meta.textTracks.map((track) => [getMetaTrackInfoID(track), track]),
    );
    const oldMediaMeta = this.#data.get(note.path)?.media;
    const newMedia = data.src;

    this.#data.set(note.path, {
      media: pick(data, ["src", "uid"]),
      tracks: data.meta.textTracks,
    });

    const updatedTrackIds = new Set(oldTrackIdMap.keys()).symmetricDifference(
      new Set(newTrackIdMap.keys()),
    );
    if (updatedTrackIds.size > 0) {
      this.#emitter.emit(
        "tracks-changed",
        [...updatedTrackIds].map(
          (id) => (oldTrackIdMap.get(id) || newTrackIdMap.get(id))!,
        ),
        note,
      );
    }

    if (
      oldMediaMeta &&
      (getMediaInfoID(oldMediaMeta.src) !== getMediaInfoID(newMedia) ||
        oldMediaMeta.uid !== data.uid)
    ) {
      this.#emitter.emit("media-changed", oldMediaMeta, data, note);
    }
  }

  rename(oldPath: string, newFile: TFile) {
    const prevData = this.#data.get(oldPath);
    if (prevData) {
      this.#data.delete(oldPath);
      this.#data.set(newFile.path, prevData);
    }
  }

  remove(note: TFile) {
    const existingData = this.#data.get(note.path);
    if (!existingData) return undefined;

    this.#data.delete(note.path);

    if (existingData.tracks.length > 0) {
      this.#emitter.emit("tracks-changed", existingData.tracks, note);
    }
    this.#emitter.emit("media-changed", existingData.media, null, note);

    return existingData;
  }

  getMedia(notePath: string): MediaLibItemCache | undefined {
    return this.#data.get(notePath)?.media;
  }

  getTracks(
    notePath: string,
    ctx: { metadataCache: MetadataCache },
  ): TextTrackInfo[] {
    const tracks = this.#data.get(notePath)?.tracks;
    if (!tracks) return [];
    return tracks
      .map((t) =>
        resolveTrack(t, {
          metadataCache: ctx.metadataCache,
          sourcePath: notePath,
        }),
      )

      .filter((v) => v !== null);
  }

  getNotesFromTrack(
    track: TextTrackInfo,
    ctx: { metadataCache: MetadataCache; vault: Vault },
  ): TFile[] {
    const trackID = getTrackInfoID(track);
    const notePaths = [...this.#data.entries()]
      .map(([path, { tracks }]) => {
        const resolvedTracks = tracks
          .map((t) =>
            resolveTrack(t, {
              metadataCache: ctx.metadataCache,
              sourcePath: path,
            }),
          )
          .filter((v) => v !== null);
        if (!resolvedTracks.some((t) => getTrackInfoID(t) === trackID))
          return null;
        return ctx.vault.getFileByPath(path);
      })
      .filter((v) => v !== null);
    return notePaths;
  }

  [Symbol.dispose]() {
    this.#data.clear();
  }
}

function resolveTrack(
  track: MetaTextTrackInfo,
  ctx: ResolveTrackLinkCtx,
): TextTrackInfo | null {
  if (track.type === "internal") {
    return resolveTrackLink(track, ctx);
  }
  return track;
}

class NoteFromIndex implements Disposable {
  media = new Map<string, TFile>();
  uid = new Map<string, TFile>();

  addNote(note: TFile, data: ParsedMediaNoteMetadata) {
    this.media.set(getMediaInfoID(data.src), note);
    this.uid.set(data.uid, note);
  }
  remove(targets: MediaLibItemCache[]) {
    for (const { uid, src } of targets) {
      this.uid.delete(uid);
      this.media.delete(getMediaInfoID(src));
    }
  }
  removeNote(notePath: string) {
    for (const [id, indexNote] of this.media) {
      if (indexNote.path === notePath) {
        this.media.delete(id);
      }
    }
    for (const [uid, indexNote] of this.uid) {
      if (indexNote.path === notePath) {
        this.uid.delete(uid);
      }
    }
  }

  [Symbol.dispose]() {
    this.media.clear();
    this.uid.clear();
  }
}

function* iterateMediaNote(ctx: {
  metadataCache: MetadataCache;
  vault: Vault;
  plugin: MxPlugin;
}) {
  for (const file of iterateFiles(ctx.vault.getRoot())) {
    if (file.extension !== "md") continue;
    const meta = getMediaMeta(file, ctx);
    if (!meta) continue;
    yield { meta, file };
  }
}
