import type MxPlugin from "@/mx-main";
import { MediaSwitcherModal } from "./modal";

const COMMAND_OPEN_MEDIA_SWITCHER = "open-media-switcher";

export function initSwitcher(plugin: MxPlugin) {
  plugin.addCommand({
    id: COMMAND_OPEN_MEDIA_SWITCHER,
    name: "Open external media",
    icon: "play",
    callback: () => openMediaSwitcher(plugin),
  });
  plugin.addRibbonIcon("square-play", "Open external media", () =>
    openMediaSwitcher(plugin),
  );
}

export function openMediaSwitcher(plugin: MxPlugin) {
  new MediaSwitcherModal(plugin).open();
}
