import { Keymap, Notice, Platform, SuggestModal } from "obsidian";
import type MxPlugin from "@/mx-main";
import type {
  DirectUrlInfo,
  MediaInfoSrc,
  UrlMediaInfo,
} from "@/def/media-info";
import { toURL } from "@mx/shared/utils/to-url";
import { isMediaFile } from "@/def/media-type";
import { fileNameFrom } from "@/def/file-info";
import { requireFs, requirePath, requireUrl, toFileURL } from "@/lib/node";
import { parseUrl } from "@/def/url-parse";
import { pickMediaFile } from "@/lib/picker";
import { validateFileUri } from "@/link/utils/file-uri";

const avId = /^av(?<id>\d+)$/i;
/**
 * @see https://github.com/SocialSisterYi/bilibili-API-collect/tree/master?tab=readme-ov-file
 */
const bvId = /^BV1(?<id>[1-9A-HJ-NP-Za-km-z]{9})$/;

const youtubeId = /^[\w-]{11}$/;

const hostnamePattern =
  /^(?:(?:[a-zA-Z\d]|[a-zA-Z\d][a-zA-Z\d-]*[a-zA-Z\d])\.)*(?:[A-Za-z\d]|[A-Za-z\d][A-Za-z\d-]*[A-Za-z\d])$/;

function toURLGuess(query: string): URL | null {
  let url: URL | null = null;
  if (Platform.isDesktopApp) {
    const path = requirePath();
    url = path.isAbsolute(query) ? toFileURL(query) : toURL(query);
  } else {
    url = toURL(query);
  }
  if (!url) return null;
  if (
    ["http:", "https:"].includes(url.protocol) &&
    !hostnamePattern.test(url.hostname)
  ) {
    // invalid hostname
    return null;
  }
  return url;
}

export class MediaSwitcherModal extends SuggestModal<
  MediaInfoSrc<UrlMediaInfo>
> {
  constructor(public plugin: MxPlugin) {
    super(plugin.app);

    this.setPlaceholder(
      "Enter file path, URL or media id, or drop a media file here",
    );
    registerFileDropHandler(this.inputEl, (evt, [input]) => {
      evt.preventDefault();
      if (!input) return;
      this.inputEl.value = input.url.href;
      this.inputEl.dispatchEvent(new Event("input"));
    });
    this.setInstructions([
      { command: "↑↓", purpose: "to navigate" },
      { command: "↵", purpose: "to open url" },
      {
        command: Platform.isMacOS ? "⌘ ↵" : "ctrl ↵",
        purpose: "to open in new tab",
      },
      {
        command: Platform.isMacOS ? "⌘ ⌥ ↵" : "ctrl alt ↵",
        purpose: "to open to the right",
      },
      { command: "esc", purpose: "to dismiss" },
    ]);
    // this.scope.register(["Mod"], "Enter", (e) => {
    //   this.selectSuggestion(null as any, e);
    //   return false;
    // });
    // this.scope.register(["Mod", "Alt"], "Enter", (e) => {
    //   this.selectSuggestion(null as any, e);
    //   return false;
    // });
    this.scope.register(null as any, "Enter", (e) => {
      // @ts-ignore
      this.chooser.useSelectedItem(e);
      return false;
    });
  }

  getSuggestions(query: string): MediaInfoSrc<UrlMediaInfo>[] {
    const url = toURLGuess(query);
    const guess: URL[] = [];
    if (!url) {
      let match: RegExpMatchArray | null = null;
      match = query.match(avId);
      if (match) {
        guess.push(
          new URL(`https://www.bilibili.com/video/av${match.groups!.id}`),
        );
      }
      match = query.match(bvId);
      if (match) {
        guess.push(new URL(`https://www.bilibili.com/video/${query}`));
      }
      match = query.match(youtubeId);
      if (match) {
        guess.push(new URL(`https://www.youtube.com/watch?v=${query}`));
      }
      if (!match) {
        const fixProtocol = toURLGuess(`https://${query}`);
        if (fixProtocol) {
          guess.push(fixProtocol);
        }
      }
    }
    const guessInfo = guess.map((u) => parseUrl(u)).filter((x) => x !== null);
    const urlInfo = parseUrl(url?.href ?? "");
    if (urlInfo) {
      return [urlInfo, ...guessInfo];
    }
    return guessInfo;
  }

  onNoSuggestion(): void {
    super.onNoSuggestion();
    if (Platform.isDesktopApp) {
      // @ts-ignore
      this.chooser.setSuggestions(["file-picker"]);
    }
  }
  renderSuggestion(
    value: MediaInfoSrc<UrlMediaInfo> | "file-picker",
    el: HTMLElement,
  ) {
    if (value === "file-picker") {
      el.setText("Open local file");
    } else {
      el.setText(decodeURI(value.info.url.href));
    }
  }
  async onChooseSuggestion(
    item: MediaInfoSrc<UrlMediaInfo> | "file-picker",
    evt: MouseEvent | KeyboardEvent,
  ) {
    let mediaInfo: MediaInfoSrc<UrlMediaInfo>;
    if (item === "file-picker") {
      const mediaUri = await pickMediaFile();
      if (!mediaUri) {
        new Notice("Failed to pick media file");
        return;
      }
      const resolved = parseUrl(mediaUri);
      if (!resolved) {
        new Notice(`Failed to resolve file: ${mediaUri}`);
        return;
      }
      mediaInfo = resolved;
    } else {
      mediaInfo = item;
    }
    if (
      mediaInfo.info.type === "url:direct" &&
      mediaInfo.info.url.protocol === "file:"
    ) {
      const checked = validateFileUri(mediaInfo.info.url, {
        vault: this.plugin.app.vault,
      });
      if (checked.type === "error") {
        new Notice(checked.message);
        return;
      }
      const fs = requireFs();
      const pathname = mediaInfo.info.url.pathname;
      try {
        const s = await fs.stat(mediaInfo.info.url);
        if (!s.isFile()) {
          new Notice(`Not a file: ${pathname}`);
          return;
        }
      } catch (e) {
        const err = e as NodeJS.ErrnoException;
        if (err.code === "ENOENT") {
          new Notice(`File not found: ${pathname}`);
        } else if (err.code === "EACCES") {
          new Notice(`Permission denied: ${pathname}`);
        } else {
          console.error("Failed to access file", pathname, err);
          new Notice(`Failed to access file (${err.code}): ${pathname}`);
        }
        return;
      }
    }

    console.debug("media selected", mediaInfo);

    if (Keymap.isModifier(evt, "Mod") && Keymap.isModifier(evt, "Alt")) {
      this.plugin.link.openMedia(mediaInfo, {
        newLeaf: "split",
        direction: "vertical",
      });
    } else if (Keymap.isModifier(evt, "Mod")) {
      this.plugin.link.openMedia(mediaInfo, {
        newLeaf: "tab",
      });
    } else {
      this.plugin.link.openMedia(mediaInfo, {
        newLeaf: false,
      });
    }
  }
}

function registerFileDropHandler(
  el: HTMLElement,
  onDrop: (evt: DragEvent, inputs: DirectUrlInfo[]) => void,
): Disposable {
  using stack = new DisposableStack();
  if (!Platform.isDesktopApp) return stack;
  const electron = (window as any).electron as
    | typeof import("electron/renderer")
    | undefined;
  const { pathToFileURL } = requireUrl();
  if (!electron) return stack;
  const { webUtils } = electron;
  const handleDrop = (evt: DragEvent) => {
    if (!evt.dataTransfer) return;
    if (evt.dataTransfer.files.length === 0) return;
    const mediaFiles = [...evt.dataTransfer.files].filter((f) => {
      const file = fileNameFrom(f);
      if (!file) return false;
      return isMediaFile(file);
    });
    const mediaInfos = mediaFiles
      .map((f) => parseUrl(pathToFileURL(webUtils.getPathForFile(f)))?.info)
      .filter((x) => x !== undefined && x.type === "url:direct");
    if (mediaInfos.length === 0) {
      new Notice("None of the dropped files are supported media files");
      return;
    }
    onDrop(evt, mediaInfos);
  };
  el.addEventListener("drop", handleDrop);
  stack.defer(() => el.removeEventListener("drop", handleDrop));
  return stack.move();
}
