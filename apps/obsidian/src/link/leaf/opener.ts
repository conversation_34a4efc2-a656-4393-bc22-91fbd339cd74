import type { ViewState, WorkspaceLeaf } from "obsidian";
import type { MediaInfoSrc } from "@/def/media-info";
import type { MediaView } from "@/def/view-type";
import {
  MEDIA_URL_VIEW_TYPE,
  type MediaUrlViewState,
} from "@/media-view/url-view";
import { assertNever } from "@std/assert/unstable-never";
import { isBrowserMediaInfo, isEmbedMediaInfo } from "@/def/media-info";
import type MxSettings from "@/settings/registry";
import type { OpenTarget } from "../types";
import {
  MEDIA_REMOTE_VIEW_TYPE,
  type MediaRemoteViewState,
} from "@/media-view/remote-view";
import { serialize } from "@/media-view/_state";
import { CodeError } from "@mx/shared/rpc";

export async function openMediaInLeaf(
  src: MediaInfoSrc,
  leaf: WorkspaceLeaf,
  ctx: { settings: MxSettings; target?: OpenTarget; supportsRemote: boolean },
): Promise<MediaView> {
  const eState: { subpath?: string } = {
    subpath: src.hash?._input,
  };
  if (src.info.type === "file") {
    await leaf.openFile(src.info.file, {
      eState,
      active: true,
    });
    return leaf.view as MediaView;
  }
  if (src.info.type === "url:hosted") {
    const settings = await ctx.settings.loaded;
    const preferBrowser =
      settings["link.hosted-prefer"] === "browser" || ctx.target === "browser";

    let viewState: ViewState;
    if (
      isBrowserMediaInfo(src.info) &&
      ctx.supportsRemote &&
      // if only supported in browser, or prefer browser
      (!isEmbedMediaInfo(src.info) || preferBrowser)
    ) {
      viewState = {
        type: MEDIA_REMOTE_VIEW_TYPE,
        state: serialize<MediaRemoteViewState>({ media: src.info }),
      };
    } else if (isEmbedMediaInfo(src.info)) {
      viewState = {
        type: MEDIA_URL_VIEW_TYPE,
        state: serialize<MediaUrlViewState>({ media: src.info }),
      };
    } else {
      throw new CodeError("fallback");
    }

    await leaf.setViewState({ ...viewState, active: true }, eState);
    return leaf.view as MediaView;
  }
  if (src.info.type === "url:direct") {
    const viewState: ViewState = {
      type: MEDIA_URL_VIEW_TYPE,
      state: serialize<MediaUrlViewState>({ media: src.info }),
    };
    await leaf.setViewState({ ...viewState, active: true }, eState);
    return leaf.view as MediaView;
  }
  assertNever(src.info);
}
