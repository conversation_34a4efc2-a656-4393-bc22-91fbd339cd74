import type {
  App,
  MarkdownView,
  TFile,
  Workspace,
  WorkspaceLeaf,
} from "obsidian";
import type { MediaLeafSnapshot } from "@/def/view-type";
import { isMediaInfoEqual, type MediaInfo } from "@/def/media-info";
import MediaFileView, { MEDIA_FILE_VIEW_TYPE } from "@/media-view/file-view";
import MediaUrlView, {
  MEDIA_URL_VIEW_TYPE,
  type MediaUrlViewState,
} from "@/media-view/url-view";
import type {
  FileMediaLeafSnapshot,
  MediaView,
  RemoteMediaLeafSnapshot,
  UrlMediaLeafSnapshot,
} from "@/def/view-type";
import { by, recentlyActiveFirst } from "@/lib/leaf-sort";
import { assertNever } from "@std/assert/unstable-never";
import MediaRemoteView, {
  MEDIA_REMOTE_VIEW_TYPE,
  type MediaRemoteViewState,
} from "@/media-view/remote-view";
import { parseFile } from "@/def/file-parse";
import { deserialize } from "@/media-view/_state";
import type { TextTrackInfo } from "@/def/track-info";
import type MxPlugin from "@/mx-main";

export function getPinnedMediaLeaves(
  ctx: Pick<App, "workspace" | "vault">,
): MediaLeafSnapshot[] {
  return getAllMediaLeaves(ctx).filter((leaf) => leaf.pinned);
}

export function getActiveMediaView(workspace: Workspace): MediaView | null {
  return (
    workspace.getActiveViewOfType(MediaFileView) ??
    workspace.getActiveViewOfType(MediaUrlView) ??
    workspace.getActiveViewOfType(MediaRemoteView) ??
    null
  );
}

/**
 * @returns all media leaves in the workspace, sorted by active time
 */
export function getAllMediaLeaves({
  workspace,
  vault,
}: Pick<App, "workspace" | "vault">): MediaLeafSnapshot[] {
  return [
    ...getFileMediaLeaves({ workspace, vault }),
    ...getUrlMediaLeaves({ workspace }),
    ...getRemoteMediaLeaves({ workspace }),
  ];
}

export function findMediaLeavesWithSameMedia(
  info: MediaInfo,
  ctx: Pick<App, "workspace" | "vault">,
): MediaLeafSnapshot[] {
  let leaves: MediaLeafSnapshot[] = [];
  if (info.type === "file") {
    leaves = getFileMediaLeaves(ctx);
  } else if (info.type === "url:hosted" || info.type === "url:direct") {
    leaves = [...getUrlMediaLeaves(ctx), ...getRemoteMediaLeaves(ctx)];
  } else {
    assertNever(info);
  }
  return leaves.filter((leaf) => isMediaInfoEqual(leaf.state.media, info));
}

export function getMostRecentEditorLeaf(workspace: Workspace) {
  const markdownLeaves = workspace
    .getLeavesOfType("markdown")
    .filter(
      (l): l is WorkspaceLeaf & { view: MarkdownView & { file: TFile } } => {
        const view = l.view as MarkdownView;
        return !!view.file && view.getMode() === "source";
      },
    );
  markdownLeaves.sort(recentlyActiveFirst);

  return markdownLeaves[0] ?? null;
}

function getFileMediaLeaves({
  workspace,
  vault,
}: Pick<App, "workspace" | "vault">): FileMediaLeafSnapshot[] {
  return workspace
    .getLeavesOfType(MEDIA_FILE_VIEW_TYPE)
    .map((leaf): FileMediaLeafSnapshot | null => {
      const viewState = leaf.getViewState();
      if (typeof viewState.state?.file !== "string") return null;
      const file = vault.getFileByPath(viewState.state.file);
      if (!file) return null;
      return {
        type: MEDIA_FILE_VIEW_TYPE,
        leaf,
        view: leaf.view instanceof MediaFileView ? leaf.view : undefined,
        pinned: viewState.pinned,
        active: viewState.active,
        activeTime: leaf.activeTime,
        state: { media: parseFile(file) },
      };
    })
    .filter((leaf) => leaf !== null);
}

function getUrlMediaLeaves({
  workspace,
}: Pick<App, "workspace">): UrlMediaLeafSnapshot[] {
  return workspace
    .getLeavesOfType(MEDIA_URL_VIEW_TYPE)
    .map((leaf): UrlMediaLeafSnapshot | null => {
      const viewState = leaf.getViewState();
      if (!viewState.state) return null;
      const state = deserialize<MediaUrlViewState>(viewState.state);
      if (!state) return null;
      return {
        type: MEDIA_URL_VIEW_TYPE,
        leaf,
        view: leaf.view instanceof MediaUrlView ? leaf.view : undefined,
        pinned: viewState.pinned,
        active: viewState.active,
        activeTime: leaf.activeTime,
        state,
      };
    })
    .filter((leaf) => leaf !== null);
}

function getRemoteMediaLeaves({
  workspace,
}: Pick<App, "workspace">): RemoteMediaLeafSnapshot[] {
  return workspace
    .getLeavesOfType(MEDIA_REMOTE_VIEW_TYPE)
    .map((leaf): RemoteMediaLeafSnapshot | null => {
      const viewState = leaf.getViewState();
      if (!viewState.state) return null;
      const state = deserialize<MediaRemoteViewState>(viewState.state);
      if (!state) return null;
      return {
        type: MEDIA_REMOTE_VIEW_TYPE,
        leaf,
        view: leaf.view instanceof MediaRemoteView ? leaf.view : undefined,
        pinned: viewState.pinned,
        active: viewState.active,
        activeTime: leaf.activeTime,
        state,
      };
    })
    .filter((leaf) => leaf !== null);
}

export async function findLinkedMediaLeaf(
  trackInfo: TextTrackInfo,
  plugin: MxPlugin,
) {
  const media = (await plugin.transcriptLoader.getLinkedMedia(trackInfo)).at(0);
  if (!media) return;
  return findMediaLeavesWithSameMedia(media, plugin.app)
    .filter((leaf) => leaf.type !== MEDIA_REMOTE_VIEW_TYPE && leaf.view)
    .sort(by(recentlyActiveFirst))
    .at(0);
}
