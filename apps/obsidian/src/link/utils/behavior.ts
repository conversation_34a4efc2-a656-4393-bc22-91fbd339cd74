import type { PaneType } from "obsidian";
import type { NewPanelBehavior } from "@/settings/migrations/1";
import { normNewPanelType } from "@/patch/mod-evt";

export function getNewPanelBehavior(
  newLeaf: PaneType | boolean | undefined,
  fromUser: boolean,
  pref: {
    click: NewPanelBehavior;
    alt: NewPanelBehavior;
  },
): NonNullable<NewPanelBehavior> {
  // if invoked by plugin, respect the newLeaf behavior
  if (!fromUser) {
    return normNewPanelType(newLeaf) ?? false;
  }
  const action = normNewPanelType(newLeaf);
  if (pref.click === null) {
    return action ?? false;
  }
  if (action === undefined || action === false) {
    return pref.click;
  }
  // alt only works as a replacement of original click behavior
  // eg, if click is set to "split", original split action (cmd+alt)
  // is replaced by behavior set in alt
  if (pref.alt !== null && action === pref.click) {
    return pref.alt;
  }
  return action;
}
