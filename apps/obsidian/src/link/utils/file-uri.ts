import { toFileURL } from "@/lib/node";
import {
  FileSystemAdapter,
  normalizePath,
  Platform,
  type Vault,
} from "obsidian";

export function isFileInVault(fileUri: URL, vaultFolderUri: URL): boolean {
  const filePath = normalizePath(fileUri.pathname);
  const vaultFolderPath = normalizePath(vaultFolderUri.pathname);
  return filePath.startsWith(vaultFolderPath);
}

export function validateFileUri(
  fileUri: URL,
  ctx: { vault: Vault },
):
  | { type: "success" }
  | {
      type: "error";
      code: "not-supported" | "in-vault" | "network-path";
      message: string;
    } {
  if (fileUri.protocol !== "file:") {
    throw new Error("Not a file uri");
  }
  if (
    !Platform.isDesktopApp ||
    !(ctx.vault.adapter instanceof FileSystemAdapter)
  ) {
    return {
      type: "error",
      code: "not-supported",
      message: "File Uri not supported on mobile",
    };
  }
  const vaultPath = ctx.vault.adapter.getFullPath("/");
  const vaultPathUri = toFileURL(vaultPath);
  if (!vaultPathUri || isFileInVault(fileUri, vaultPathUri)) {
    return {
      type: "error",
      code: "in-vault",
      message:
        "The file you are trying to open is inside the vault, please use internal link instead",
    };
  }
  if (fileUri.hostname) {
    return {
      type: "error",
      code: "network-path",
      message:
        "Network path is not supported in obsidian, you need to map it to a local path",
    };
  }
  return { type: "success" };
}
