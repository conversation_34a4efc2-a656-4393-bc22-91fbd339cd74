import type { PaneType, SplitDirection } from "obsidian";
import type { MediaUrlViewType } from "@/media-view/url-view";

// Re-export interface extensions for Obsidian
declare module "obsidian" {
  interface WorkspaceLeaf {
    activeTime: number;
    tabHeaderEl: HTMLDivElement;
    pinned: boolean;
    togglePinned(): void;
  }
  interface WorkspaceTabGroup {
    children: WorkspaceLeaf[];
  }
  interface Workspace {
    activeTabGroup: WorkspaceTabGroup | null;
  }
  interface FileManager {
    createNewFile(
      folder: TFolder,
      name: string,
      ext: string,
      content?: string,
    ): Promise<TFile>;
  }
}

export type OpenTarget = "browser" | MediaUrlViewType;

export interface OpenMediaOptions {
  /** if undefined, use default behavior */
  newLeaf?: PaneType | false;
  target?: OpenTarget;
  direction?: SplitDirection;
  /**
   * if true, treat newLeaf as click action
   * and apply preference behavior
   */
  fromUser?: boolean;
}
