import type MxPlugin from "@/mx-main";
import { type App, Component, Notice, parseLinktext } from "obsidian";

import type { LinkEvent } from "@/patch/event";
import patchEditorClick from "@/patch/link.editor";
import patchLinktextOpen from "@/patch/link.internal";
import patchPreview<PERSON>lick from "@/patch/link.preview";
import { parseUrl } from "@/def/url-parse";
import { parseHashProps } from "@mx/shared/utils/hash-prop";
import { CodeError } from "@mx/shared/rpc";
import { parseFile } from "@/def/file-parse";
import {
  isMediaInfoEqual,
  type MediaInfoSrc,
  type UrlMediaInfo,
} from "@/def/media-info";
import { isMediaFileUrl } from "@/def/media-type";
import { validateFileUri } from "./utils/file-uri";
import { getNewPanelBehavior } from "./utils/behavior";
import {
  findMediaLeavesWithSameMedia,
  getPinnedMediaLeaves,
} from "./leaf/finder";
import { openMediaInLeaf } from "./leaf/opener";
import type { OpenMediaOptions } from "./types";
import { assertNever } from "@std/assert/unstable-never";
import type { MediaView } from "@/def/view-type";
import { deadline } from "@std/async";
import type { LatestPluginSettings } from "@/settings/registry/atom";
import {
  by,
  recentlyActiveFirst,
  nonDeferredFirst,
  remoteMediaViewFirst,
} from "@/lib/leaf-sort";

export default class LinkRegistry extends Component {
  plugin: MxPlugin;
  app: App;

  constructor(plugin: MxPlugin) {
    super();
    this.plugin = plugin;
    this.app = plugin.app;
  }

  shouldHandleLinkLogic(
    src: MediaInfoSrc<UrlMediaInfo>,
    settings: LatestPluginSettings,
  ): boolean {
    const { info, hash } = src;
    if (info.type === "url:hosted") {
      return settings["link.handle-hosted"];
    }

    if (info.type === "url:direct") {
      if (!settings["link.handle-direct-url"]) return false;
      // use whitelisting since url:direct is a fallback option for url parsing
      const isInLibrary = !!this.plugin.mediaLib.findNoteByMedia(info);
      const isTimestamp = !!hash?.tempFragment;
      const isMediaFile = () => {
        if (!isMediaFileUrl(info.url)) return false;
        if (info.url.protocol === "file:") {
          return (
            validateFileUri(info.url, { vault: this.app.vault }).type ===
            "success"
          );
        }
        return true;
      };
      return isInLibrary || isTimestamp || isMediaFile();
    }
    assertNever(info);
  }

  async shouldHandleLink(src: MediaInfoSrc<UrlMediaInfo>): Promise<boolean> {
    const settings = await this.plugin.settings.loaded;
    return this.shouldHandleLinkLogic(src, settings);
  }

  #onInternalLinkClick: LinkEvent<this>["onInternalLinkClick"] = async (
    linktext,
    sourcePath,
    newLeaf,
    fallback,
  ) => {
    const { path: linkpath, subpath: hash } = parseLinktext(linktext);
    const linkFile = parseFile(
      this.app.metadataCache.getFirstLinkpathDest(linkpath, sourcePath),
    );
    if (!linkFile) {
      fallback();
      return;
    }
    await this.openMedia(
      { info: linkFile, hash: parseHashProps(hash) },
      { newLeaf, fromUser: true },
    );
  };
  #onLinktextOpen: LinkEvent<this>["onInternalLinkClick"] = async (
    linktext,
    sourcePath,
    newLeaf,
    fallback,
  ) => {
    const { path: linkpath, subpath: hash } = parseLinktext(linktext);
    const linkFile = parseFile(
      this.app.metadataCache.getFirstLinkpathDest(linkpath, sourcePath),
    );
    if (!linkFile) {
      fallback();
      return;
    }
    await this.openMedia(
      { info: linkFile, hash: parseHashProps(hash) },
      { newLeaf, fromUser: false },
    );
  };

  #onExternalLinkClick: LinkEvent<this>["onExternalLinkClick"] = async (
    link,
    newLeaf,
    fallback,
  ) => {
    const src = parseUrl(link);
    try {
      if (!src || !(await this.shouldHandleLink(src))) {
        throw new CodeError("fallback");
      }
      if (src.info.type === "url:direct" && src.info.url.protocol === "file:") {
        const checked = validateFileUri(src.info.url, {
          vault: this.app.vault,
        });
        if (checked.type === "error") {
          throw new Error(checked.message);
        }
      }
      await this.openMedia(src, { newLeaf, fromUser: true });
    } catch (e) {
      if (e instanceof Error) {
        if (e instanceof CodeError) {
          if (e.code === "no-browser-session") {
            new Notice(
              "No browser session connected to obsidian, go to browser companion to connect",
            );
            return;
          }
          if (e.code === "fallback") {
            if (e.message) {
              console.error("open external link fallback error", e);
              new Notice(`Failed to open media link: ${e.message}`);
            }
            fallback();
            return;
          }
          if (e.code === "browser-na") {
            // new Notice("Browser control not available, fallback to default");
            fallback();
            return;
          }
        }
        console.error("open external link error", e, CodeError);
        new Notice(`Failed to open media link: ${e.message}`);
      }
      throw e;
    }
  };

  onload(): void {
    patchEditorClick(this, {
      onExternalLinkClick: this.#onExternalLinkClick,
      onInternalLinkClick: this.#onInternalLinkClick,
    });
    patchPreviewClick(this, {
      onExternalLinkClick: this.#onExternalLinkClick,
      onInternalLinkClick: this.#onInternalLinkClick,
    });
    patchLinktextOpen(this, {
      onInternalLinkClick: this.#onLinktextOpen,
    });
    // patchInlineUrl(this, {
    //   onExternalLinkClick: (e, url, newLeaf) => {
    //     e.stopImmediatePropagation();
    //     this.#onExternalLinkClick(url, newLeaf, () => {});
    //   },
    // });
  }

  async #isRemoteSupported() {
    try {
      return await deadline(this.plugin.isRemoteSupported(), 1000);
    } catch (e) {
      if (e instanceof DOMException && e.name === "TimeoutError") {
        console.warn("isRemoteSupported timeout");
      } else {
        console.error("isRemoteSupported error", e);
      }
      return false;
    }
  }

  async openMedia(
    src: MediaInfoSrc,
    options: OpenMediaOptions = {},
  ): Promise<{ view?: MediaView }> {
    if (!options.newLeaf) {
      const pinned = getPinnedMediaLeaves(this.app)
        .sort(by(nonDeferredFirst, recentlyActiveFirst))
        .filter((leaf) => leaf.view);

      // attempt to open in existing pane
      if (pinned.length > 0) {
        const sameMedia = pinned.filter((leaf) =>
          isMediaInfoEqual(leaf.state.media, src.info),
        );
        const view = await openMediaInLeaf(
          src,
          (sameMedia[0] ?? pinned[0]!).leaf,
          {
            settings: this.plugin.settings,
            target: options.target,
            supportsRemote: await this.#isRemoteSupported(),
          },
        );
        return { view };
      }
      const existing = findMediaLeavesWithSameMedia(src.info, this.app)
        .sort(by(nonDeferredFirst, remoteMediaViewFirst, recentlyActiveFirst))
        .at(0);
      if (existing) {
        if (!existing.view) {
          await this.app.workspace.revealLeaf(existing.leaf);
        }
        // update hash
        existing.leaf.setEphemeralState({
          subpath: src.hash?._input,
        } satisfies {
          subpath?: string;
        });
        // now it's guaranteed to be loaded
        return { view: existing.leaf.view as MediaView };
      }
    }
    // fallback to open new leaf
    const settings = await this.plugin.settings.loaded;
    const newPanelBehavior = getNewPanelBehavior(
      options.newLeaf,
      options.fromUser ?? false,
      {
        click: settings["link.click-behavior"],
        alt: settings["link.altclick-behavior"],
      },
    );
    const view = await openMediaInLeaf(
      src,
      newPanelBehavior === "split-horizontal"
        ? this.app.workspace.getLeaf("split", options.direction ?? "horizontal")
        : this.app.workspace.getLeaf(
            newPanelBehavior as "split",
            options.direction,
          ),
      {
        settings: this.plugin.settings,
        target: options.target,
        supportsRemote: await this.#isRemoteSupported(),
      },
    );
    return { view };
  }
}
