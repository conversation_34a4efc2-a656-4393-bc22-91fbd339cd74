import type { MediaView } from "@/def/view-type";
import { MarkdownView, type Vault, type Workspace } from "obsidian";
import {
  findMediaLeavesWithSameMedia,
  getActiveMediaView,
  getAllMediaLeaves,
} from "./leaf/finder";
import type { MediaLibraryIndex } from "@/media-lib/indexer";
import {
  by,
  nonDeferredFirst,
  recentlyActiveFirst,
  remoteMediaViewFirst,
} from "@/lib/leaf-sort";

/**
 * Get the active media view, based on current workspace state like active tab group, pinned player, etc.
 */
export function getWorkspaceMediaView(ctx: {
  workspace: Workspace;
  vault: Vault;
  mediaLib: MediaLibraryIndex;
}): MediaView | null {
  const active = getActiveMediaView(ctx.workspace);
  if (active) return active;
  const activeMd = ctx.workspace.getActiveViewOfType(MarkdownView);
  // special handling of media lib item note
  if (activeMd?.file) {
    const media = ctx.mediaLib.findMediaByNote(activeMd.file);
    if (media) {
      // if the note is a media note, only accept corresponding media leaves
      // don't use the latest media leaf as fallback
      const leaf = findMediaLeavesWithSameMedia(media.src, ctx)
        .filter((leaf) => !leaf.view)
        .sort(by(remoteMediaViewFirst, recentlyActiveFirst))
        .at(0);
      return leaf?.view ?? null;
    }
  }
  const leaves = getAllMediaLeaves(ctx);

  const pinned = leaves
    .filter((leaf) => !leaf.view && leaf.pinned)
    .sort(by(remoteMediaViewFirst, recentlyActiveFirst))
    .at(0);
  if (pinned) return pinned.view!;

  // fallback to the latest  media leaf
  const { activeTabGroup } = ctx.workspace;
  const activeVisibleLeaves = !activeTabGroup
    ? leaves
    : leaves.filter(
        (snapshot) => !activeTabGroup.children.includes(snapshot.leaf),
      );
  activeVisibleLeaves.sort(
    by(nonDeferredFirst, remoteMediaViewFirst, recentlyActiveFirst),
  );
  return activeVisibleLeaves.at(0)?.view ?? null;
}
