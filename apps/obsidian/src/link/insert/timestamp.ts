import type { Editor } from "obsidian";
import { Notice, type TFile } from "obsidian";

import type { PlayerComponent } from "@/def/player-comp";
import { insertTimestamp, timestampLinkFactory } from "./template";

export async function insertTimestampLink(
  playerComponent: PlayerComponent,
  ctx: { file: TFile; editor: Editor },
): Promise<boolean> {
  const { file: newNote, editor } = ctx;

  const mediaInfo = playerComponent.getMediaInfo();
  if (!mediaInfo) {
    new Notice("No media is opened");
    return false;
  }

  if (!playerComponent.takeTimestamp) {
    new Notice("Timestamping not supported for this media");
    return false;
  }

  const settings = await playerComponent.plugin.settings.loaded;
  const { currentTime } = await playerComponent.takeTimestamp();

  if (!currentTime) {
    new Notice("Playback not started");
    return false;
  }

  const timestampLink = timestampLinkFactory(
    {
      currentTime,
      src: mediaInfo,
    },
    {
      fileManager: playerComponent.plugin.app.fileManager,
      timestampOffset: settings["note.template.timestamp-offset"],
    },
  )(newNote.path);

  try {
    insertTimestamp(
      { timestamp: timestampLink },
      {
        editor,
        template: settings["note.template.timestamp"],
        insertBefore: settings["note.template.insert-at"] === "before-cursor",
      },
    );
    return true;
  } catch (e) {
    new Notice("Failed to insert screenshot, see console for details");
    console.error("Failed to insert screenshot", e);
    return false;
  }
}
