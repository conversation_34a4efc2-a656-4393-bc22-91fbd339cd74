import { isVideoProvider } from "@vidstack/react";
import type { Editor, Vault } from "obsidian";
import { ButtonComponent, normalizePath, Notice, TFile } from "obsidian";

import type { PlayerComponent } from "@/def/player-comp";
import { player<PERSON><PERSON> } from "@mx/shared/hooks/use-player";
import { extension } from "@mx/shared/dom/screenshot";
import { toDurationISOString } from "@mx/shared/time/format";
import { getAttachmentFolder } from "@/lib/save-folder";
import {
  insertTimestamp,
  screenshotEmbedFactory,
  timestampLinkFactory,
} from "./template";
import type { MediaNoteMeta } from "@/media-lib/parse";
import { ImageClipDialog } from "@/image-edit/dialog";

async function takeScreenshot(
  playerComponent: PlayerComponent,
  options?: {
    format?: string;
    quality?: number;
    clip?: boolean;
  },
) {
  const settings = await playerComponent.plugin.settings.loaded;
  if (!playerComponent.captureScreenshot) {
    throw new Error("Capture screenshot not implemented");
  }
  const player = playerComponent.getPlayer();
  const spec = {
    format: options?.format ?? settings["playback.screenshot.format"],
    quality: options?.quality ?? settings["playback.screenshot.quality"],
  };
  const screenshot = await playerComponent.captureScreenshot(spec);
  if (options?.clip) {
    const prevPaused = player ? player.paused : null;
    if (player && !prevPaused) {
      player.pause();
    }
    const edited = await ImageClipDialog.open(
      playerComponent.plugin,
      screenshot.blob,
      spec,
    );
    if (!edited) {
      throw new Error("Screenshot editing cancelled");
    }
    const blob = await edited.result.blob;
    if (!blob) {
      throw new Error("Failed to clip screenshot");
    }
    if (player && !prevPaused) {
      await player.play();
    }
    return {
      ...screenshot,
      blob,
      size: edited.result.size,
    };
  }
  return screenshot;
}

async function saveScreenshotFile(
  playerComponent: PlayerComponent,
  ctx: { file: TFile; clip?: boolean },
): Promise<{
  file: TFile;
  timestamp: number;
  media: { uid: string; meta: MediaNoteMeta };
}> {
  const { file: newNote } = ctx;
  const player = playerComponent.store.get(playerAtom);
  if (!player) {
    throw new Error("Player not initialized");
  }
  const mediaInfo = playerComponent.getMediaInfo();
  if (!mediaInfo) {
    throw new Error("No media is opened");
  }
  if (!player?.provider || !isVideoProvider(player.provider)) {
    throw new Error("Screenshot is not supported for this media");
  }

  const [{ uid, meta }, settings, { blob, timestamp }] = await Promise.all([
    playerComponent.plugin.mediaItemFor(mediaInfo),
    playerComponent.plugin.settings.loaded,
    takeScreenshot(playerComponent, { clip: ctx.clip }),
  ]);

  const imageDir = await getAttachmentFolder({
    folderPath: settings["playback.screenshot.folder-path"],
    app: playerComponent.plugin.app,
    sourcePath: newNote.path,
  });
  const isoDuration = toDurationISOString(timestamp)
    .toLowerCase()
    .replaceAll(".", "_");
  const imageName = `mx-img-${uid}-${isoDuration}.${extension(blob)}`;

  const file = await saveScreenshotToFile(
    normalizePath(`${imageDir.path}/${imageName}`),
    blob,
    { vault: playerComponent.plugin.app.vault },
  );

  return { file, timestamp, media: { uid, meta } };
}

async function insertScreenshotEmbed<T extends PlayerComponent>(
  playerComponent: T,
  screenshot: {
    file: TFile;
    timestamp: number;
    media: { uid: string; meta: MediaNoteMeta };
  },
  ctx: { file: TFile; editor: Editor },
): Promise<boolean> {
  const { file: newNote, editor } = ctx;
  const { file, timestamp, media } = screenshot;
  const mediaInfo = playerComponent.getMediaInfo();
  if (!mediaInfo) {
    new Notice("No media is opened");
    return false;
  }

  const settings = await playerComponent.plugin.settings.loaded;

  const timestampLink = timestampLinkFactory(
    {
      currentTime: timestamp,
      src: mediaInfo,
    },
    {
      fileManager: playerComponent.plugin.app.fileManager,
      timestampOffset: settings["note.template.timestamp-offset"],
    },
  )(newNote.path);
  const screenshotEmbed = screenshotEmbedFactory(
    {
      file,
      timestamp,
      meta: { uid: media.uid, title: media.meta.title },
    },
    {
      fileManager: playerComponent.plugin.app.fileManager,
      template: settings["note.template.screenshot-embed"],
    },
  )(newNote.path);

  try {
    insertTimestamp(
      {
        timestamp: timestampLink,
        screenshot: screenshotEmbed,
      },
      {
        editor,
        template: settings["note.template.screenshot"],
        insertBefore: settings["note.template.insert-at"] === "before-cursor",
      },
    );
    return true;
  } catch (e) {
    new Notice("Failed to insert screenshot, see console for details");
    console.error("Failed to insert screenshot", e);
    return false;
  }
}

export async function saveScreenshot(
  playerComponent: PlayerComponent,
  ctx: { file: TFile; editor: Editor; clip?: boolean },
): Promise<boolean> {
  const screenshot = await saveScreenshotFile(playerComponent, {
    file: ctx.file,
    clip: ctx.clip,
  });
  if (!screenshot) return false;
  return insertScreenshotEmbed(playerComponent, screenshot, ctx);
}

export function handleCORSError(e: Error) {
  if (
    e instanceof DOMException &&
    e.name === "SecurityError" &&
    e.message.match(/\btainted\b|\bcanvas/i)
  ) {
    new Notice(
      createFragment((frag) => {
        const container = frag.createDiv();
        container.appendText(
          "Cannot capture screenshot from this media due to CORS security policy, ",
        );
        container.createEl("br");
        container.appendText(
          "See the troubleshooting guide for possible solutions",
        );

        new ButtonComponent(
          container.createDiv({ attr: { style: "margin-top: 10px" } }),
        )
          .setButtonText("Open guide")
          .onClick(() => {
            window.open("https://mx.pkmer.net/docs/faq/cors", "_blank");
          });
      }),
    );
    return true;
  }
  return false;
}

// TBD: copy html with timestamp link & screenshot encoded in base64
export async function copyScreenshot(
  player: PlayerComponent,
  options: { clip?: boolean } = {},
): Promise<boolean> {
  try {
    // only png is supported for clipboard
    const screenshot = await takeScreenshot(player, {
      format: "image/png",
      clip: options.clip,
    });
    await copyBlob(screenshot.blob);
    return true;
  } catch (e) {
    new Notice("Failed to copy screenshot, see console for details");
    console.error("Failed to copy screenshot", e);
    return false;
  }
}

export async function copyBlob(blob: Blob) {
  await navigator.clipboard.write([new ClipboardItem({ [blob.type]: blob })]);
}

async function saveScreenshotToFile(
  path: string,
  blob: Blob,
  ctx: { vault: Vault },
): Promise<TFile> {
  const existingFile = ctx.vault.getAbstractFileByPath(path);
  const arrayBuffer = await blob.arrayBuffer();
  if (existingFile instanceof TFile) {
    try {
      await ctx.vault.modifyBinary(existingFile, arrayBuffer);
      new Notice(`Screenshot updated in ${path}`);
      return existingFile;
    } catch (e) {
      new Notice(
        `Failed to save screenshot to ${path}: ${e instanceof Error ? e.message : e}`,
      );
      throw e;
    }
  }
  if (existingFile !== null) {
    new Notice(`Screenshot file occupied by a folder: ${path}`);
    throw new Error(`Screenshot file occupied by a folder: ${path}`);
  }
  try {
    const newFile = await ctx.vault.createBinary(path, arrayBuffer);
    new Notice(`Screenshot created in ${path}`);
    return newFile;
  } catch (e) {
    new Notice(
      `Failed to create screenshot in ${path}: ${e instanceof Error ? e.message : e}`,
    );
    throw e;
  }
}
