import { Notice } from "obsidian";
import type { Editor, FileManager, TFile } from "obsidian";
import { insertBeforeCursor, insertToCursor } from "@/lib/editor";
import type { MediaInfo } from "@/def/media-info";
import { formatDuration, toTempFragString } from "@mx/shared/time/format";

export function insertTimestamp(
  { timestamp, screenshot }: { timestamp: string; screenshot?: string },
  {
    template,
    editor,
    insertBefore,
  }: { template: string; editor: Editor; insertBefore?: boolean },
) {
  // console.debug("insert timestamp", { timestamp, screenshot, template });
  let toInsert = template.replace("{{TIMESTAMP}}", timestamp);
  if (screenshot) {
    toInsert = toInsert.replace("{{SCREENSHOT}}", screenshot);
  }
  // console.debug("content to insert", toInsert);

  try {
    // console.debug(
    //   `inserting timestamp ${insertBefore ? "before" : "after"} cursor`,
    // );
    if (insertBefore) {
      insertBeforeCursor(toInsert, editor);
    } else {
      insertToCursor(toInsert, editor);
    }
  } catch (error) {
    new Notice("Failed to insert timestamp, see console for details");
    console.error("Failed to insert timestamp", error);
  }
}

/**
 * @param time in seconds
 */
export function timestampLinkFactory(
  inputs: { currentTime: number; src: MediaInfo },
  ctx: { fileManager: FileManager; timestampOffset: number },
): (newNotePath: string) => string {
  const timestamp = Math.max(0, inputs.currentTime + ctx.timestampOffset);
  const formatted = formatDuration(timestamp);
  const frag = toTempFragString({ start: timestamp, end: -1 });
  const hash = frag ? `#${frag}` : "";

  if (inputs.src.type === "file") {
    const { file } = inputs.src;
    return (newNotePath: string) =>
      ctx.fileManager
        .generateMarkdownLink(file, newNotePath, hash, formatted)
        .replace(/^!/, "");
  }
  const sourceUrl = inputs.src.url;
  return () => `[${formatted}](${sourceUrl}${hash})`;
}

export function screenshotEmbedFactory(
  inputs: {
    file: TFile;
    timestamp: number;
    meta: { uid: string; title?: string };
  },
  ctx: { fileManager: FileManager; template: string },
): (newNotePath: string) => string {
  const { title, uid } = inputs.meta;
  const humanizedDuration =
    inputs.timestamp > 0 ? ` - ${formatDuration(inputs.timestamp)}` : "";
  return (newNotePath) =>
    ctx.fileManager
      .generateMarkdownLink(
        inputs.file,
        newNotePath,
        "",
        ctx.template
          .replaceAll("{{TITLE}}", title || uid)
          .replaceAll("{{DURATION}}", humanizedDuration),
      )
      .replace(/^!/, "");
}
