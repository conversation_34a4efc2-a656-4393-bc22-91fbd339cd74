formulas:
  titles: if(!title.isEmpty(), title, if(!video.isEmpty(), video, if(!audio.isEmpty(), audio, if(!media.isEmpty(), media, ""))))
  media: ' if(!video.isEmpty(), video, if(!audio.isEmpty(), audio, if(!media.isEmpty(), media, "")))'
views:
  - type: table
    name: Table
    filters:
      and:
        - '!note["mx-uid"].isEmpty()'
    order:
      - title
      - video
      - formula.titles
      - file.name
    sort: []
    columnSize:
      note.title: 198
  - type: cards
    name: View
    filters:
      and:
        - '!note["mx-uid"].isEmpty()'
    order:
      - formula.titles
      - formula.media
    image: cover
    imageAspectRatio: 0.55
