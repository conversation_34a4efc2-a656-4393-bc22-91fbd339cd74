# Media Extended for Obsidian

Code for the Media Extended plugin for Obsidian is managed inside a monorepo including:
- `@mx/ui`: Shared UI components
- `@mx/shared`: Shared logic and utilities library

Simple documentation about the code structure can be found in the `.cursor/rules` folder.

## WebSocket Server

The plugin communicates with browser extensions through a WebSocket server running in Electron's main process, allowing connectivity across multiple vaults.

Security is enforced through a custom RPC protocol that restricts communication to a limited set of trusted commands.

Logic for this is not yet enabled, but will be implemented when it's fully tested. 

## UI Library

The plugin uses a custom UI library for building the player UI, along with other components.

We're using React and Tailwind CSS to build the UI components, and shadow DOM to encapsulate the styles.
