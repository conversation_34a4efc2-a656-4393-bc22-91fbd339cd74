"""
Utility functions for the YouTube Video Info Extractor.
"""

import re
from typing import Optional, Dict, Any


def validate_youtube_url(url: str) -> bool:
    """
    Validate if the provided URL is a valid YouTube URL.
    
    Args:
        url: The URL to validate
        
    Returns:
        True if valid YouTube URL, False otherwise
    """
    if not url:
        return False
    
    youtube_patterns = [
        r'^https?://(www\.)?youtube\.com/watch\?v=[\w-]+',
        r'^https?://(www\.)?youtu\.be/[\w-]+',
        r'^https?://(www\.)?youtube\.com/embed/[\w-]+',
        r'^https?://(www\.)?youtube\.com/v/[\w-]+',
    ]
    
    return any(re.match(pattern, url) for pattern in youtube_patterns)


def extract_video_id(url: str) -> Optional[str]:
    """
    Extract video ID from various YouTube URL formats.
    
    Args:
        url: YouTube URL
        
    Returns:
        Video ID if found, None otherwise
    """
    if not url:
        return None
    
    # Handle different YouTube URL formats
    patterns = [
        r'(?:youtube\.com/watch\?v=|youtu\.be/|youtube\.com/embed/|youtube\.com/v/)([^&\n?#]+)',
        r'youtube\.com/watch\?.*v=([^&\n?#]+)',
    ]
    
    for pattern in patterns:
        match = re.search(pattern, url)
        if match:
            return match.group(1)
    
    return None


def validate_input_data(input_data: Dict[str, Any]) -> Dict[str, str]:
    """
    Validate input data and return validation errors.
    
    Args:
        input_data: Input data to validate
        
    Returns:
        Dictionary of validation errors (empty if no errors)
    """
    errors = {}
    
    # Validate YouTube URL
    youtube_url = input_data.get('youtube_url', '')
    if not youtube_url:
        errors['youtube_url'] = 'YouTube URL is required'
    elif not validate_youtube_url(youtube_url):
        errors['youtube_url'] = 'Invalid YouTube URL format'
    
    # Validate proxy configuration (flattened)
    use_apify_proxy = input_data.get('use_apify_proxy', True)
    custom_proxy = input_data.get('custom_proxy', '')
    
    if not use_apify_proxy and not custom_proxy:
        errors['proxy_configuration'] = 'Either use_apify_proxy must be true or custom_proxy must be provided'
    
    if custom_proxy and not custom_proxy.startswith(('http://', 'https://')):
        errors['custom_proxy'] = 'Custom proxy URL must start with http:// or https://'
    
    # Validate metadata (optional string)
    metadata = input_data.get('metadata', '')
    if metadata is not None and not isinstance(metadata, str):
        errors['metadata'] = 'Metadata must be a string'
    
    return errors 