#!/usr/bin/env python3
"""
YouTube Video Info Extractor Apify Actor

This actor extracts video information and metadata from YouTube videos using yt-dlp.
"""

import json
import os
from typing import Dict, Any, Optional

import yt_dlp
from apify import Actor
from .utils import validate_input_data, extract_video_id


class YouTubeInfoExtractor:
    """Main class for extracting YouTube video information."""
    
    def __init__(self, input_data: Dict[str, Any]):
        """Initialize the extractor with input configuration."""
        self.input_data = input_data
        self.youtube_url = input_data.get('youtube_url', '')
        self.metadata = input_data.get('metadata', '')
        self.proxy_configuration = None
        
    def _get_ydl_opts(self, proxy_url: Optional[str] = None) -> Dict[str, Any]:
        """Get yt-dlp options for metadata extraction only."""
        opts: Dict[str, Any] = {
            'ignoreerrors': False,  # Let yt-dlp raise exceptions instead of silently failing
        }
        
        # Add proxy configuration if provided
        if proxy_url:
            opts['proxy'] = proxy_url
            Actor.log.info(f'Using proxy for metadata extraction: {proxy_url.split("@")[0]}@***')
        else:
            Actor.log.info('No proxy configured for metadata extraction')
        
        return opts

    async def _get_proxy_url(self) -> Optional[str]:
        """Get a new proxy URL."""
        # Check environment variables (case-insensitive)
        proxy_url = None
        env_vars = ['HTTPS_PROXY', 'HTTP_PROXY', 'ALL_PROXY', 'https_proxy', 'http_proxy', 'all_proxy']
        for var in env_vars:
            proxy_url = os.getenv(var)
            if proxy_url:
                break
        
        if proxy_url:
            return proxy_url
        
        # Check user-provided proxy configuration (flattened)
        custom_proxy = self.input_data.get('custom_proxy', '')
        use_apify_proxy = self.input_data.get('use_apify_proxy', True)
        
        if custom_proxy:
            return custom_proxy
        elif not use_apify_proxy:
            return None
        
        try:
            # Initialize proxy configuration if not already done
            if not self.proxy_configuration:
                self.proxy_configuration = await Actor.create_proxy_configuration(
                    groups=['RESIDENTIAL'],
                )
            
            if self.proxy_configuration:
                # Get a new proxy URL
                proxy_url = await self.proxy_configuration.new_url()
                Actor.log.info('Generated new proxy URL')
                return proxy_url
            else:
                Actor.log.warning('No proxy configuration available')
                return None
                
        except Exception as e:
            Actor.log.error(f'Error getting proxy URL: {str(e)}')
            return None

    async def _extract_info_with_retry(self, max_retries: int = 5) -> Dict[str, Any]:
        """Extract video info with retry logic and proxy switching."""
        video_id = extract_video_id(self.youtube_url)
        if not video_id:
            raise ValueError(f'Invalid YouTube URL: {self.youtube_url}')
        
        Actor.log.info(f'Extracting video info for video ID: {video_id}')
        Actor.log.info(f'Video URL: {self.youtube_url}')
        Actor.log.info(f'Max retries: {max_retries}')
        
        last_error = None
        
        for attempt in range(max_retries):
            try:
                Actor.log.info(f'Attempt {attempt + 1}/{max_retries}')
                
                # Get proxy URL for this attempt
                proxy_url = await self._get_proxy_url()
                
                opts = self._get_ydl_opts(proxy_url)
                
                with yt_dlp.YoutubeDL(opts) as ydl:
                    Actor.log.info('Extracting video information...')
                    info = ydl.extract_info(self.youtube_url, download=False)
                    
                    Actor.log.info(f'Successfully extracted video info on attempt {attempt + 1}')
                    
                    return {
                        'video_id': video_id,
                        'video_url': self.youtube_url,
                        'info': info,
                        'metadata': self.metadata,
                        'extraction_success': True,
                        'attempts_made': attempt + 1,
                        'proxy_used': bool(proxy_url)
                    }
                        
            except yt_dlp.DownloadError as e:
                last_error = e
                Actor.log.error(f'Download error on attempt {attempt + 1}: {str(e)}')
                
                if attempt < max_retries - 1:
                    Actor.log.info(f'Retrying with new proxy... ({max_retries - attempt - 1} attempts remaining)')
                else:
                    Actor.log.error('All retries exhausted due to download errors')
                    
            except Exception as e:
                # Non-recoverable errors - don't retry
                last_error = e
                Actor.log.error(f'Non-recoverable error on attempt {attempt + 1}: {str(e)}')
                Actor.log.error('Stopping retries due to non-recoverable error')
                break
        
        # All retries exhausted
        Actor.log.error(f'All {max_retries} attempts failed')
        return {
            'video_id': video_id,
            'video_url': self.youtube_url,
            'error': str(last_error) if last_error else 'Unknown error after all retries',
            'metadata': self.metadata,
            'extraction_success': False,
            'attempts_made': max_retries
        }
    
    async def extract_info(self) -> Dict[str, Any]:
        """Extract video information from YouTube video with retry logic."""
        return await self._extract_info_with_retry(max_retries=5)


async def main():
    """Main actor function."""
    async with Actor:
        # Get input data
        input_data = await Actor.get_input() or {}
        
        Actor.log.info('YouTube Video Info Extractor started')
        Actor.log.info(f'Input: {json.dumps(input_data, indent=2)}')
        
        # Validate input data
        validation_errors = validate_input_data(input_data)
        if validation_errors:
            error_msg = 'Input validation failed: ' + ', '.join([f'{k}: {v}' for k, v in validation_errors.items()])
            Actor.log.error(error_msg)
            await Actor.fail(status_message=error_msg)
            return
        
        try:
            # Initialize extractor
            extractor = YouTubeInfoExtractor(input_data)
            
            # Extract video info
            result = await extractor.extract_info()
            
            # Push result to dataset
            await Actor.push_data(result)
            
            if result.get('extraction_success'):
                Actor.log.info('Video info extraction completed successfully')
            else:
                Actor.log.error(f'Video info extraction failed: {result.get("error")}')
                await Actor.fail(status_message=f'Video info extraction failed: {result.get("error")}')
                
        except Exception as e:
            Actor.log.error(f'Actor failed with error: {str(e)}')
            await Actor.fail(status_message=f'Actor failed: {str(e)}')


if __name__ == '__main__':
    import asyncio
    asyncio.run(main())
