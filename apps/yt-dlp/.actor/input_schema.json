{"title": "YouTube Video Info Extractor Input", "type": "object", "schemaVersion": 1, "properties": {"youtube_url": {"title": "YouTube URL", "type": "string", "description": "The YouTube video URL to extract information from", "pattern": "^https?://(www\\.)?(youtube\\.com/watch\\?v=|youtu\\.be/)[a-zA-Z0-9_-]+", "example": "https://www.youtube.com/watch?v=dQw4w9WgXcQ", "editor": "textfield"}, "use_apify_proxy": {"title": "Use Apify Proxy", "type": "boolean", "description": "Whether to use Apify residential proxy", "default": true, "editor": "checkbox"}, "custom_proxy": {"title": "Custom Proxy URL", "type": "string", "description": "Custom proxy URL (http:// or https://). If provided, this will be used instead of Apify proxy", "example": "http://proxy.example.com:8080", "editor": "textfield"}, "metadata": {"title": "Custom Metadata", "type": "string", "description": "Optional custom metadata string to include in the output", "example": "Custom tracking info or notes", "editor": "textfield"}}, "required": ["youtube_url"]}