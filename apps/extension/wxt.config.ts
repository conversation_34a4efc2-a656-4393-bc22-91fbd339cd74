import { defineConfig } from "wxt";
import tailwindcss from "@tailwindcss/vite";

const devPermissions =
  process.env.NODE_ENV === "development"
    ? ["declarativeNetRequestFeedback"]
    : [];

// See https://wxt.dev/api/config.html
export default defineConfig({
  extensionApi: "chrome",
  modules: ["@wxt-dev/module-react"],
  vite: () => ({
    plugins: [tailwindcss()],
    build: {
      target: "es2022",
    },
  }),
  manifest: () => ({
    key: process.env.CRX_PUBLIC_KEY,
    externally_connectable: {
      matches: [
        `${process.env.NODE_ENV === "development" ? "http://localhost:3781" : import.meta.env.SITE_ORIGIN}/app/*`,
      ],
      accepts_tls_channel_id: true,
    },
    // optional_permissions: ["webRequest", "webRequestAuthProvider"],
    // dnr cannot be optional
    permissions: [
      "declarativeNetRequest",
      "storage",
      "scripting",
      ...devPermissions,
    ],
    optional_host_permissions: ["*://*/*"],
    // where webRequestAuthProvider is available
    minimum_chrome_version: "119",
    cross_origin_embedder_policy: {
      value:
        process.env.NODE_ENV === "development"
          ? "credentialless"
          : "require-corp",
    },
    cross_origin_opener_policy: {
      value: "same-origin",
    },
  }),
});
