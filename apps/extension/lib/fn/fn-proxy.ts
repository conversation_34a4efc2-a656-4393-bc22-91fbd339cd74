import type { Host } from "@mx/shared/url-parse/const";
import type { TabRegistry } from "@/lib/tab-proxy";
import {
  proxyFnName,
  type ProxyFnAction,
  type ProxyFns,
  type ProxyTarget,
} from "@/protocol/proxy-fn-def";

import findBilibiliVideoTab from "@/lib/find-tab/bilibili";
import findCourseraLectureTab from "@/lib/find-tab/coursera";
import findBaiduPanVideoTab from "@/lib/find-tab/baidu-pan";
import findYoutubeTab from "@/lib/find-tab/youtube";

type FindTabFn<T> = (target: T) => Promise<chrome.tabs.Tab | null>;

interface HostConfig {
  findTab: FindTabFn<any>;
}

/**
 * Maps host types to their corresponding tab finders
 */
const HOST_CONFIG = {
  bilibili: {
    findTab: findBilibiliVideoTab,
  },
  coursera: {
    findTab: findCourseraLectureTab,
  },
  "baidu-pan": {
    findTab: findBaiduPanVideoTab,
  },
  youtube: {
    findTab: findYoutubeTab,
  },
} satisfies Record<ProxyTarget, HostConfig>;

function getConfig(host: Host): HostConfig {
  const config = HOST_CONFIG[host as keyof typeof HOST_CONFIG];
  if (!config) {
    throw new Error(`Unsupported host: ${host}`);
  }
  return config;
}

export class TabNotFoundError extends Error {
  constructor(target: { host: Host }) {
    super(`Failed to find opened tab for ${JSON.stringify(target)}`);
  }
}

/**
 * Factory function that creates and returns a remote function for the given target
 */
export async function createProxyFn<
  T extends { host: Host },
  A extends ProxyFnAction,
>(
  target: T,
  action: A,
  ctx: {
    tabProxy: TabRegistry;
  },
) {
  const host = target.host;
  const config = getConfig(host);

  const targetTab = await config.findTab(target);

  if (!targetTab?.id) {
    throw new TabNotFoundError(target);
  }

  const conn = await ctx.tabProxy.get(targetTab.id);
  return conn.consumer.createRemoteFn<ProxyFns[A]>(proxyFnName(action, host));
}
