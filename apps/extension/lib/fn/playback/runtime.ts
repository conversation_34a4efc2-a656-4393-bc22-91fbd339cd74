import { proxyFnName, type ProxyFns } from "@/protocol/proxy-fn-def";
import type { Host } from "@mx/shared/url-parse/const";
import type { FnDef } from "@mx/shared/rpc";
export default function buildPlaybackFn(
  host: Host,
  findPlayer: () => HTMLMediaElement | null | Promise<HTMLMediaElement | null>,
): FnDef<ProxyFns["playback"]> {
  return {
    name: proxyFnName("playback", host),
    async fn(action) {
      const player = await findPlayer();
      if (!player) {
        throw new Error("Player not found");
      }
      await handlePlaybackAction(player, action);
    },
  };
}

import type { PlaybackAction } from "@mx/ext-fn/playback-actions";
import { assertNever } from "@std/assert/unstable-never";

export async function handlePlaybackAction(
  media: HTMLMediaElement,
  action: PlaybackAction,
) {
  switch (action.type) {
    case "play-pause":
      if (action.paused === null) {
        media.paused ? await media.play() : media.pause();
      } else if (action.paused) {
        media.pause();
      } else {
        await media.play();
      }
      break;
    case "set-volume":
      media.volume = action.volume;
      break;
    case "set-muted":
      media.muted = action.muted;
      break;
    case "set-playback-rate":
      media.playbackRate = action.playbackRate;
      break;
    case "seek":
      media.currentTime = action.time;
      break;
    default:
      assertNever(action);
  }
}
