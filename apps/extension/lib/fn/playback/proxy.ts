import { type Fn, name as fnName } from "@mx/ext-fn/app2ext/playback";
import type { TabRegistry } from "@/lib/tab-proxy";
import { createProxyFn } from "../fn-proxy";
import type { FnDef } from "@mx/shared/rpc";

export default function initPlaybackProxy(tabProxy: TabRegistry): FnDef<Fn> {
  const ctx = { tabProxy };
  return {
    name: fnName,
    async fn(target, action) {
      const playbackControl = await createProxyFn(target, "playback", ctx);
      return await playbackControl(action);
    },
  };
}
