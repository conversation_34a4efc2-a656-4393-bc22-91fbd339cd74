import { equal } from "@std/assert";
import { toPermissionOrigin, toDnrUrlFilterPattern } from "@/lib/url-filters";
import {
  type CorsBypassConfig,
  corsBypassConfigsSchema,
} from "@mx/ext-fn/app2ext/cors-bypass.def";
import { type Fn, name } from "@mx/ext-fn/app2ext/cors-bypass";

type RuleProfile = {
  config: CorsBypassConfig;
  ruleId: number;
};

export class CorsRuleManager implements AsyncDisposable {
  // id space 1-100
  private activeRules: RuleProfile[] = [];

  static fnName = name;
  register: Fn = async (configsInput) => {
    const configs = corsBypassConfigsSchema.assert(configsInput);
    // Create rules for declarativeNetRequest
    const rules: {
      rule: chrome.declarativeNetRequest.Rule;
      config: CorsBypassConfig;
    }[] = configs.map((config, idx) => {
      const urlFilter = toDnrUrlFilterPattern(config.url);
      const initiatorDomains = config.initiatorDomains ?? [];
      return {
        rule: {
          id: idx + 1,
          priority: 1,
          action: {
            type: chrome.declarativeNetRequest.RuleActionType.MODIFY_HEADERS,
            responseHeaders: [
              {
                header: "Access-Control-Allow-Origin",
                operation: chrome.declarativeNetRequest.HeaderOperation.SET,
                value: "*",
              },
              {
                // required by COEP
                header: "cross-origin-resource-policy",
                operation: chrome.declarativeNetRequest.HeaderOperation.SET,
                value: "cross-origin",
              },
            ],
          },
          condition: {
            urlFilter,
            resourceTypes: [chrome.declarativeNetRequest.ResourceType.MEDIA],
            initiatorDomains:
              initiatorDomains.length > 0 ? initiatorDomains : undefined,
          },
        },
        config,
      };
    });

    // TBD: raise error if permission to origin is not granted

    const nowProfile = this.activeRules;
    const nextProfile = rules.map<RuleProfile>(({ rule, config }) => ({
      ruleId: rule.id,
      config,
    }));
    if (equal(nowProfile, nextProfile)) {
      console.log("CORS rules are already set, skipping");
      return;
    }

    const requiredOrigins = configs.flatMap(({ url }) => [
      toPermissionOrigin(url),
    ]);
    if (
      !(await chrome.permissions
        .contains({
          origins: requiredOrigins,
          permissions: ["declarativeNetRequest"],
        })
        .catch((e) => {
          console.error("failed to check dnr rules permission", configs, e);
          return false;
        }))
    ) {
      throw new Error(
        `Permission to target origin is not granted: ${requiredOrigins}`,
      );
    }

    const removeRuleIds = Array.from({ length: 100 }, (_, idx) => idx + 1);
    // Update the session rules, credentials will be cleared when the extension is disabled
    await chrome.declarativeNetRequest.updateSessionRules({
      addRules: rules.map(({ rule }) => rule),
      removeRuleIds,
    });
    this.activeRules = nextProfile;
  };

  async [Symbol.asyncDispose](): Promise<void> {
    if (this.activeRules.length === 0) return;

    await chrome.declarativeNetRequest.updateSessionRules({
      removeRuleIds: this.activeRules.map(({ ruleId }) => ruleId),
    });
    this.activeRules = [];
  }
}
