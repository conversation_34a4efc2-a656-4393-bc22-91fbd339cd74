import type SingletonWebSocketConnection from "@/lib/bg-rpc/ws";
import type { WebSocketConnStatus } from "@mx/shared/utils/ws/state";

export type PopupContext = {
  ws: SingletonWebSocketConnection;
};

export const PopupFnName = {
  getWsConnStatus: "ws-status",
  reconnectWs: "ws-reconnect",
} as const;

export type PopupFn = {
  getWsConnStatus: () => WebSocketConnStatus;
  reconnectWs: () => Promise<void>;
};

export interface PopupEventMap {
  "ws-status-changed": WebSocketConnStatus;
}
