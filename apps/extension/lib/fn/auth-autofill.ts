import { equal } from "@std/assert";
import { toPermissionOrigin, toUrlMatchPatterns } from "@/lib/url-filters";
import {
  type AuthAutofillConfig,
  authAutofillConfigsSchema,
} from "@mx/ext-fn/app2ext/auth-autofill.def";
import { type Fn, name } from "@mx/ext-fn/app2ext/auth-autofill";

type AuthHandler = (
  details: chrome.webRequest.WebAuthenticationChallengeDetails,
  callback?: (response: chrome.webRequest.BlockingResponse) => void,
) => void;

export class AuthAutofillManager {
  #authHandlers: {
    handler: AuthHandler;
    config: AuthAutofillConfig;
  }[] = [];

  static fnName = name;
  register: Fn = async (configsInput) => {
    const configs = authAutofillConfigsSchema.assert(configsInput);

    const previousConfigs = this.#authHandlers.map(({ config }) => config);
    if (equal(previousConfigs, configs)) {
      console.log("Auth autofill rules are already set, skipping");
      return;
    }

    this.dispose();

    const requiredOrigins = configs.flatMap(
      ({ urls, initiatorOrigins = [] }) => [
        ...urls.map((url) => toPermissionOrigin(url)),
        ...initiatorOrigins.map((origin) => origin.replace(/\/*$/, "/")),
      ],
    );
    if (
      !(await chrome.permissions.contains({
        origins: requiredOrigins,
        permissions: ["webRequest", "webRequestAuthProvider"],
      }))
    ) {
      throw new Error(
        `Permission to target origin is not granted: ${requiredOrigins}`,
      );
    }

    for (const config of configs) {
      const { initiatorOrigins, urls, username, password } = config;
      const initiatorSet = new Set(initiatorOrigins);
      const handler: AuthHandler = (details, callback) => {
        if (!callback) return;
        if (initiatorSet.has(details.initiator!)) {
          callback({ authCredentials: { username, password } });
        } else {
          // remember: always call callback, since it's a blocking request
          callback({});
        }
      };
      this.#authHandlers.push({ handler, config });
      // this won't work in crossOrigin="anonymous" videos since it will not trigger auth dialog
      chrome.webRequest.onAuthRequired.addListener(
        handler,
        {
          urls: urls.map(toUrlMatchPatterns),
          types: ["media"],
        },
        ["asyncBlocking"],
      );
    }
  };

  dispose(): void {
    for (const { handler } of this.#authHandlers) {
      console.log("remove onAuthRequired", handler);
      chrome.webRequest.onAuthRequired.removeListener(handler);
    }
    this.#authHandlers = [];
  }
}
