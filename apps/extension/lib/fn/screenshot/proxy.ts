import { type Fn, name as fnName } from "@mx/ext-fn/app2ext/screenshot";
import type { TabRegistry } from "@/lib/tab-proxy";
import { createProxyFn } from "../fn-proxy";
import type { FnDef } from "@mx/shared/rpc";

export default function initScreenshotProxy(tabProxy: TabRegistry): FnDef<Fn> {
  const ctx = { tabProxy };
  return {
    name: fnName,
    async fn(target, ...args) {
      const captureScreenshot = await createProxyFn(target, "screenshot", ctx);
      return await captureScreenshot(...args);
    },
  };
}
