import { proxyFnName, type ProxyFns } from "@/protocol/proxy-fn-def";
import { captureScreenshotToJson } from "@mx/shared/dom/screenshot";
import type { FnDef } from "@mx/shared/rpc";
import type { Host } from "@mx/shared/url-parse/const";

export default function buildScreenshotFn(
  host: Host,
  findPlayer: () => HTMLVideoElement | null | Promise<HTMLVideoElement | null>,
): FnDef<ProxyFns["screenshot"]> {
  return {
    name: proxyFnName("screenshot", host),
    async fn(options) {
      const player = await findPlayer();
      if (!player) {
        throw new Error("Player not found");
      }
      return await captureScreenshotTo<PERSON><PERSON>(player, options);
    },
  };
}
