import { type Fn, name as fnName } from "@mx/ext-fn/app2ext/link-open";
import type { TabRegistry } from "@/lib/tab-proxy";
import { createProxyFn, TabNotFoundError } from "../fn-proxy";
import type { FnDef } from "@mx/shared/rpc";
import type { BrowserVid } from "@mx/ext-fn/app2ext/playback";
import { normalizeYouTubeUrl } from "@mx/shared/url-parse/youtube";
import { normalizeBilibiliUrl } from "@mx/shared/url-parse/bilibili";
import { normalizeCourseraUrl } from "@mx/shared/url-parse/coursera";
import { normalizeBaiduPanUrl } from "@mx/shared/url-parse/baidu-pan";
import { assertNever } from "@std/assert/unstable-never";

export default function initLinkOpenProxy(tabProxy: TabRegistry): FnDef<Fn> {
  return {
    name: fnName,
    async fn(target, hash) {
      try {
        const fn = await createProxyFn(target, "link-open", {
          tabProxy,
        });
        return await fn(hash);
      } catch (err) {
        if (err instanceof TabNotFoundError) {
          const url = normalizeUrl(target);
          if (hash) {
            url.hash = hash._input;
          }
          await chrome.tabs.create({ url: url.href });
        } else {
          throw err;
        }
      }
    },
  };
}

function normalizeUrl(target: BrowserVid) {
  if (target.host === "youtube") {
    return normalizeYouTubeUrl(target);
  }
  if (target.host === "bilibili") {
    return normalizeBilibiliUrl(target);
  }
  if (target.host === "coursera") {
    return normalizeCourseraUrl(target);
  }
  if (target.host === "baidu-pan") {
    return normalizeBaiduPanUrl(target);
  }
  assertNever(target);
}
