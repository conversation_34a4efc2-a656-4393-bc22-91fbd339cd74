import { proxyFnName, type ProxyFns } from "@/protocol/proxy-fn-def";
import type { Host } from "@mx/shared/url-parse/const";
import type { FnDef } from "@mx/shared/rpc";
import { isTimestamp } from "@mx/shared/time/temporal-frag";

export default function buildLinkOpenFn(
  host: Host,
  findPlayer: () => HTMLMediaElement | null | Promise<HTMLMediaElement | null>,
): FnDef<ProxyFns["link-open"]> {
  return {
    name: proxyFnName("link-open", host),
    async fn(hash) {
      const player = await findPlayer();
      if (!player) {
        throw new Error("Player not found");
      }
      if (hash && isTimestamp(hash.tempFragment)) {
        await player.play();
        player.currentTime = hash.tempFragment.start;
      }
    },
  };
}
