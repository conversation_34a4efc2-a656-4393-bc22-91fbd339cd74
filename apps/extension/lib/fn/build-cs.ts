import "@mx/shared/polyfills/disposable";

import ContentScriptAdapter, {
  type ContentScriptMessageSendOptions,
} from "@mx/shared/rpc/adapter/ext-cs";
import { Provider } from "@mx/shared/rpc";
import type { ContentScriptContext } from "wxt/client";

const EXT_ORIGIN = `chrome-extension://${chrome.runtime.id}`;

export default function buildContentScript(
  register: (ctx: {
    cs: ContentScriptContext;
    provider: Provider<ContentScriptMessageSendOptions>;
    stack: DisposableStack;
    // consumer: Consumer<ContentScriptMessageSendOptions>;
  }) => Promise<void>,
) {
  return async (ctx: ContentScriptContext) => {
    using stack = new DisposableStack();

    const adapter = new ContentScriptAdapter({
      filterSender: (sender) => !sender.url && sender.origin === EXT_ORIGIN,
    });
    const provider = stack.use(Provider.init(adapter));

    await register({ cs: ctx, provider, stack });

    // if failed during loading, it will be disposed automatically (`await using stack`)
    // by moving the stack to the context, it will live until the context is invalidated
    const disposable = stack.move();

    ctx.onInvalidated(() => {
      disposable.dispose();
    });
  };
}
