import type { Consumer } from "@mx/shared/rpc";
import BrowserTabAdapter, {
  type TabMessageSendOptions,
} from "@mx/shared/rpc/adapter/ext-tab";
import { LRUCache } from "lru-cache";
import { defineConsumer } from "@mx/shared/rpc";

export interface TabConnection {
  tabId: number;
  consumer: Consumer<TabMessageSendOptions>;
}

async function initTabConnection(tabId: number): Promise<TabConnection> {
  // using stack = new DisposableStack();
  const adapter = new BrowserTabAdapter(tabId);
  const consumer = defineConsumer(adapter);
  return {
    tabId,
    consumer,
  };
}

export class TabRegistry implements Disposable {
  #registry = new LRUCache<number, TabConnection>({
    max: 50,
    // When a tab client is evicted from the cache, dispose it to clean up resources
    // disposeAfter: (value) => {
    //   value[Symbol.dispose]();
    // },
  });
  #stack = new DisposableStack();

  async get(tabId: number): Promise<TabConnection> {
    if (this.#registry.has(tabId)) {
      return this.#registry.get(tabId)!;
    }
    const backend = await initTabConnection(tabId);
    this.#registry.set(tabId, backend);
    return backend;
  }

  [Symbol.dispose]() {
    this.#stack.dispose();
    this.#registry.clear();
  }
}
