import { assertNever } from "@std/assert/unstable-never";
import URLParser from "@mx/shared/url-parse/parser";
import coursera, { type CourseraVid } from "@mx/shared/url-parse/coursera";
import { distinct } from "@std/collections";

const parser = new URLParser(coursera);

export default async function findCourseraLectureTab(
  target: CourseraVid,
): Promise<chrome.tabs.Tab | null> {
  const tabs = await chrome.tabs.query({
    url: distinct(coursera.flatMap((p) => p.chromeTabQuery)),
  });
  if (!tabs.length) {
    return null;
  }

  const [targetTab] = tabs
    .filter((t) => {
      if (!t.url) return false;
      const info = parser.parse(t.url);
      if (!info) return false;
      const { vid } = info;
      if (target.type === "lecture") {
        return (
          vid.type === "lecture" &&
          target.lectureId === vid.lectureId &&
          target.courseId === vid.courseId
        );
      }
      assertNever(target.type);
    })
    .sort((a, b) => (b.lastAccessed ?? 0) - (a.lastAccessed ?? 0));

  if (!targetTab) {
    return null;
  }

  return targetTab;
}
