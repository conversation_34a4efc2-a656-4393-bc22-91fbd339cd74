import URLParser from "@mx/shared/url-parse/parser";
import youtube, {
  type YouTubeUrlResult,
  type YouTubeVid,
} from "@mx/shared/url-parse/youtube";
import { distinct } from "@std/collections";

const variantRank = {
  watch: 3,
  pathname: 2,
  "short-uri": 1,
};

const parser = new URLParser(youtube);

export default async function findYoutubeTab(
  target: YouTubeVid,
): Promise<chrome.tabs.Tab | null> {
  const tabs = await chrome.tabs.query({
    url: distinct(youtube.flatMap((p) => p.chromeTabQuery)),
  });
  if (!tabs.length) {
    return null;
  }

  const [targetTab] = tabs
    .map((tab) => ({ tab, info: tab.url ? parser.parse(tab.url) : null }))
    .filter((t) => {
      if (!t.info) return false;
      const { vid } = t.info;
      return target.vid === vid.vid;
    })
    .sort((a, b) => {
      // Rank by variant type (watch > pathname > short-uri)
      const aRank = variantRank[a.info!.variant];
      const bRank = variantRank[b.info!.variant];
      if (aRank !== bRank) {
        return bRank - aRank; // Higher rank first
      }
      // Same variant, sort by last accessed time
      return (b.tab.lastAccessed ?? 0) - (a.tab.lastAccessed ?? 0);
    });

  if (!targetTab) {
    return null;
  }

  return targetTab.tab;
}
