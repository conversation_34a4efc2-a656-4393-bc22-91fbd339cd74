import URLParser from "@mx/shared/url-parse/parser";
import bilibili from "@mx/shared/url-parse/bilibili";
import type {
  BiliAid,
  BiliBvid,
  BiliEpid,
  BiliSsid,
} from "@mx/shared/url-parse/bilibili";
import { distinct } from "@std/collections";
import { assertNever } from "@std/assert/unstable-never";

const parser = new URLParser(bilibili);
export default async function findBilibiliVideoTab(
  target: BiliAid | BiliBvid | BiliEpid | BiliSsid,
): Promise<chrome.tabs.Tab | null> {
  const tabs = await chrome.tabs.query({
    url: distinct(
      bilibili
        .filter((p) => p.variant !== "short-uri")
        .flatMap((p) => p.chromeTabQuery),
    ),
  });
  if (!tabs.length) {
    return null;
  }

  const [targetTab] = tabs
    .filter((t) => {
      if (!t.url) return false;
      const info = parser.parse(t.url);
      if (!info) return false;
      const { vid } = info;
      if (target.type === "aid") {
        return vid.type === "aid" && target.aid === vid.aid;
      }
      if (target.type === "bvid") {
        return vid.type === "bvid" && target.bvid === vid.bvid;
      }
      if (target.type === "epid") {
        return vid.type === "epid" && target.epid === vid.epid;
      }
      if (target.type === "ssid") {
        return vid.type === "ssid" && target.ssid === vid.ssid;
      }
      assertNever(target);
    })
    .sort((a, b) => (b.lastAccessed ?? 0) - (a.lastAccessed ?? 0));

  if (!targetTab) {
    return null;
  }

  return targetTab;
}
