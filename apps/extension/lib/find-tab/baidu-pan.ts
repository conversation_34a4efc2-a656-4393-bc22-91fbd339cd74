import { assertNever } from "@std/assert/unstable-never";
import URLParser from "@mx/shared/url-parse/parser";
import panBaidu, { type BaiduPanVid } from "@mx/shared/url-parse/baidu-pan";
import { distinct } from "@std/collections";

const parser = new URLParser(panBaidu);
export default async function findBaiduPanVideoTab(
  target: BaiduPanVid,
): Promise<chrome.tabs.Tab | null> {
  const tabs = await chrome.tabs.query({
    url: distinct(panBaidu.flatMap((p) => p.chromeTabQuery)),
  });
  if (!tabs.length) {
    return null;
  }

  const [targetTab] = tabs
    .filter((t) => {
      if (!t.url) return false;
      const info = parser.parse(t.url);
      if (!info) return false;
      const { vid } = info;
      if (target.type === "video") {
        return vid.type === "video" && target.path === vid.path;
      }
      assertNever(target.type);
    })
    .sort((a, b) => (b.lastAccessed ?? 0) - (a.lastAccessed ?? 0));

  if (!targetTab) {
    return null;
  }

  return targetTab;
}
