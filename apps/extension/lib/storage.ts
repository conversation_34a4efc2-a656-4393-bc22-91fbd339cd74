import { nanoid } from "nanoid";

/**
 * key for message port initialization protocol
 */
export const connInitKey = storage.defineItem("local:yt-iframe-conn-init", {
  init: () => nanoid(),
});

export const profile = storage.defineItem("local:profile", {
  init: () => ({
    id: nanoid(),
    name: "Profile",
  }),
});

export const remoteWsPortPref = storage.defineItem<number>(
  "local:preference:remote-ws:port",
  { fallback: 10573 },
);
export type ThemePref = "light" | "dark" | "system";
export const themePref = storage.defineItem<ThemePref>(
  "local:preference:theme",
  { fallback: "system" },
);
