import type { UrlTarget } from "@mx/ext-fn/url-target";

export function toPermissionOrigin({
  host,
  pathname,
  allowSubdomains,
  exact,
  protocol,
}: UrlTarget) {
  const pathPattern = `${pathname}${exact ? "" : "*"}`;
  if (allowSubdomains) {
    return `${protocol}://*.${host}${pathPattern}`;
  }
  return `${protocol}://${host}${pathPattern}`;
}

// https://developer.mozilla.org/en-US/docs/Mozilla/Add-ons/WebExtensions/API/declarativeNetRequest/RuleCondition#urlfilter
// https://developer.chrome.com/docs/extensions/reference/api/declarativeNetRequest#url_filter_syntax
export function toDnrUrlFilterPattern({
  host,
  pathname,
  allowSubdomains,
  exact,
  protocol,
}: UrlTarget) {
  const pathPattern = `${pathname}${exact ? "" : "*"}`;
  if (allowSubdomains) {
    return `||${host}${pathPattern}`;
  }
  return `|${protocol}://${host}${pathPattern}`;
}

// https://developer.mozilla.org/en-US/docs/Mozilla/Add-ons/WebExtensions/Match_patterns
export function toUrlMatchPatterns({
  host,
  pathname,
  exact,
  protocol,
  allowSubdomains,
}: UrlTarget): string {
  if (!pathname.startsWith("/")) {
    throw new Error("Invalid pathname");
  }
  const pathPattern = `${pathname}${exact ? "" : "*"}`;
  if (allowSubdomains) {
    return `${protocol}://*.${host}${pathPattern}`;
  }
  return `${protocol}://${host}${pathPattern}`;
}
