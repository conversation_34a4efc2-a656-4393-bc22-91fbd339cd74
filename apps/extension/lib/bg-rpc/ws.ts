import { ipPort } from "@mx/shared/validator/common";

import WebSocketConnAdapter from "@mx/shared/rpc/adapter/ws-conn";
import { Provider, type AbortSignalOptions } from "@mx/shared/rpc";
import type { IPCContext } from "./ctx";
import initScreenshotProxy from "@/lib/fn/screenshot/proxy";
import initPlaybackProxy from "@/lib/fn/playback/proxy";
import initLinkOpenProxy from "@/lib/fn/link-open/proxy";
import { remoteWsPortPref, profile as profileStore } from "../storage";
import {
  WebSocketConnection,
  type WebSocketConnectionEvents,
} from "@mx/shared/utils/ws";
import { createEventEmitter } from "@mx/shared/utils/event";

export default class SingletonWebSocketConnection implements Disposable {
  #ws: WebSocketConnection | null = null;

  readonly #ctx: IPCContext;
  constructor(ctx: IPCContext) {
    this.#ctx = ctx;
    // this.on("message", (message) => {
    //   console.log("ws message", message);
    // });
  }

  async #connect({ signal }: AbortSignalOptions): Promise<WebSocketConnection> {
    const url = await this.#getTargetUrl();
    if (this.#ws?.url === url) {
      await this.#ws.connect({ signal });
      return this.#ws;
    }
    this.#ws?.close();
    this.#emitter.emit("status-changed", "disconnected");
    this.#ws = new WebSocketConnection(url);
    this.#ws.on("status-changed", (status) => {
      this.#emitter.emit("status-changed", status);
    });
    this.#ws.on("message", (message) => {
      this.#emitter.emit("message", message);
    });
    await this.#ws.connect({ signal });
    return this.#ws;
  }

  async #getTargetUrl(): Promise<string> {
    const profile = await profileStore.getValue();
    const port = await remoteWsPortPref.getValue();
    ipPort.assert(port);
    const query = new URLSearchParams({
      name: profile.name,
      browser: import.meta.env.BROWSER,
    });
    return `ws://localhost:${port}/ws/${profile.id}?${query.toString()}`;
  }

  get status() {
    return this.#ws?.status ?? "disconnected";
  }

  #evtDisposables = new DisposableStack();
  #emitter = createEventEmitter<WebSocketConnectionEvents>();

  on<E extends keyof WebSocketConnectionEvents>(
    event: E,
    callback: WebSocketConnectionEvents[E],
  ): Disposable {
    return this.#evtDisposables.use({
      [Symbol.dispose]: this.#emitter.on(event, callback),
    });
  }

  close() {
    this.#ws?.close();
    this.#previousConnect?.dispose();
    this.#evtDisposables.dispose();
  }
  [Symbol.dispose](): void {
    this.close();
  }

  #previousConnect?: DisposableStack;
  async connect({ signal }: AbortSignalOptions = {}): Promise<void> {
    this.#previousConnect?.dispose();
    const ws = await this.#connect({ signal });
    const adapter = new WebSocketConnAdapter(ws);
    using stack = new DisposableStack();
    const provider = stack.use(Provider.init(adapter));
    provider.addFnHandler(initScreenshotProxy(this.#ctx.tabProxy));
    provider.addFnHandler(initPlaybackProxy(this.#ctx.tabProxy));
    provider.addFnHandler(initLinkOpenProxy(this.#ctx.tabProxy));
    this.#previousConnect = stack.move();
  }
}
