import ExtensionInternalAdapter from "@mx/shared/rpc/adapter/ext-internal";

import { Provider } from "@mx/shared/rpc";
import {
  type PopupFn,
  PopupFnName,
  type PopupContext,
  type PopupEventMap,
} from "../fn/popup/def";
import { defineEventEmitter } from "@mx/shared/rpc";

const EXT_POPUP_URL = chrome.runtime.getURL("popup.html");

export default function initPopupConnection(ctx: PopupContext): Disposable {
  using stack = new DisposableStack();

  const adapter = new ExtensionInternalAdapter({
    filterSender: (sender) => {
      return sender.url === EXT_POPUP_URL;
    },
  });
  const provider = stack.use(Provider.init(adapter));
  const emitter = defineEventEmitter<PopupEventMap>()(adapter);

  provider.addFnHandler<PopupFn["getWsConnStatus"]>({
    fn: () => ctx.ws.status,
    name: PopupFnName.getWsConnStatus,
  });
  provider.addFnHandler<PopupFn["reconnectWs"]>({
    fnCreator:
      ({ signal }) =>
      async () => {
        await ctx.ws.connect({ signal });
      },
    name: PopupFnName.reconnectWs,
  });
  stack.use(
    ctx.ws.on("status-changed", (status) => {
      emitter.emit("ws-status-changed", status).catch((e) => {
        if (
          e instanceof Error &&
          e.message.includes("Receiving end does not exist")
        ) {
          // we don't care if the popup is opened or not, just dispatch the event
          return;
        }
        throw e;
      });
    }),
  );

  const disposables = stack.move();
  return {
    [Symbol.dispose]: () => disposables.dispose(),
  };
}
