import ExtensionExternalAdapter from "@mx/shared/rpc/adapter/ext-external";

import pingHandler from "@/lib/fn/ping";
import { CorsRuleManager } from "@/lib/fn/cors-bypass";
import portInitKeyHandler from "@/lib/fn/port-init-key";

import initScreenshotProxy from "@/lib/fn/screenshot/proxy";
import initPlaybackProxy from "@/lib/fn/playback/proxy";
import initLinkOpenProxy from "@/lib/fn/link-open/proxy";
import { Provider } from "@mx/shared/rpc";
import type { IPCContext } from "./ctx";

export default async function initPWAConnection(
  config: PWAConnectionOptions,
  ctx: IPCContext,
): Promise<AsyncDisposable> {
  await using stack = new AsyncDisposableStack();

  const allowedOrigins = new Set(config.allowedOrigins);

  const adapter = new ExtensionExternalAdapter({
    verifyTlsChannel: true,
    filterSender: (sender) => {
      return !!sender.origin && allowedOrigins.has(sender.origin);
    },
  });
  const provider = stack.use(Provider.init(adapter));
  const corsBypass = stack.use(new CorsRuleManager());

  provider.addFnHandler(pingHandler);
  provider.addFnHandler({
    fn: corsBypass.register,
    name: CorsRuleManager.fnName,
  });
  provider.addFnHandler(portInitKeyHandler);
  provider.addFnHandler(initScreenshotProxy(ctx.tabProxy));
  provider.addFnHandler(initPlaybackProxy(ctx.tabProxy));
  provider.addFnHandler(initLinkOpenProxy(ctx.tabProxy));

  const disposable = stack.move();
  return {
    [Symbol.asyncDispose]: () => disposable.disposeAsync(),
  };
}

export interface PWAConnectionOptions {
  allowedOrigins: string[];
}
