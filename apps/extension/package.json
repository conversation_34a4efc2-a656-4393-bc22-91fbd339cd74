{"name": "wxt-react-starter", "description": "manifest.json description", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "wxt", "dev:firefox": "wxt -b firefox", "build": "wxt build", "build:firefox": "wxt build -b firefox", "zip": "wxt zip", "zip:firefox": "wxt zip -b firefox", "compile": "tsc --noEmit", "postinstall": "wxt prepare", "typecheck": "tsc --noEmit"}, "dependencies": {"@mx/ext-fn": "workspace:*", "@mx/shared": "workspace:*", "@radix-ui/react-slot": "^1.2.0", "@std/assert": "npm:@jsr/std__assert@^1.0.11", "@std/collections": "npm:@jsr/std__collections@^1.0.10", "@std/encoding": "npm:@jsr/std__encoding@^1.0.7", "@tailwindcss/vite": "^4.0.0", "arktype": "2.1.10", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lru-cache": "^11.0.2", "lucide-react": "^0.475.0", "nanoevents": "^9.1.0", "nanoid": "^5.0.9", "react": "19.1.0", "react-dom": "19.1.0", "sonner": "^1.7.4", "swr": "^2.3.3", "tailwind-merge": "^3.2.0", "tailwindcss": "^4.1.4", "tw-animate-css": "^1.2.4"}, "devDependencies": {"@types/chrome": "^0.0.280", "@types/react": "19.1.7", "@types/react-dom": "19.1.6", "@wxt-dev/module-react": "^1.1.2", "typescript": "~5.8.2", "wxt": "^0.19.29"}}