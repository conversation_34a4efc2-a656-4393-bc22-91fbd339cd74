import { themePref, type ThemePref } from "@/lib/storage";
import {
  createContext,
  startTransition,
  use,
  useCallback,
  useState,
} from "react";

const ThemeContext = createContext<{
  theme: "light" | "dark" | "system";
}>({} as any);

const ThemeDispatchContext = createContext<{
  setTheme: (theme: ThemePref) => void;
}>({} as any);

export function ThemeProvider({
  initialTheme,
  children,
}: { children: React.ReactNode; initialTheme: ThemePref }) {
  const [theme, _setTheme] = useState<ThemePref>(initialTheme);
  useEffect(() => {
    return themePref.watch((val) => _setTheme(val));
  }, []);
  const setTheme = useCallback(
    (theme: ThemePref) =>
      startTransition(async () => {
        _setTheme(theme);
        await themePref.setValue(theme);
      }),
    [],
  );
  return (
    <ThemeContext.Provider value={{ theme }}>
      <ThemeDispatchContext.Provider value={{ setTheme }}>
        {children}
      </ThemeDispatchContext.Provider>
    </ThemeContext.Provider>
  );
}

export function useTheme() {
  const { theme } = use(ThemeContext);
  const { setTheme } = use(ThemeDispatchContext);
  if (!theme || !setTheme) {
    throw new Error("useTheme must be used within a ThemeProvider");
  }
  return [theme, setTheme] as const;
}
