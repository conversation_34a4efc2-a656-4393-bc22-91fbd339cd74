import { defineRunnerConfig } from "wxt";

export default defineRunnerConfig({
  startUrls: ["http://localhost:3781/app/ext-test"],
  keepProfileChanges: true,
  chromiumProfile: "./.wxt/chrome-data",
  chromiumPref: {
    extensions: {
      // not working now: https://github.com/wxt-dev/wxt/issues/137
      // ui: { developer_mode: true },
      pinned_extensions: ["palfkdjannehhpmigeljbanlmfhibbbh"],
    },
    devtools: {
      synced_preferences_sync_disabled: {
        // and log locations show up properly, see:
        // https://github.com/wxt-dev/wxt/issues/236#issuecomment-1915364520
        skipContentScripts: false,
        // Was renamed at some point, see:
        // https://github.com/wxt-dev/wxt/issues/912#issuecomment-2284288171
        "skip-content-scripts": false,
      },
    },
  },
});
