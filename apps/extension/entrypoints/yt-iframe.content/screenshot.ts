import { captureScreenshot } from "@mx/shared/dom/screenshot";
import { type Fn, name } from "@mx/ext-fn/app2iframe/screenshot";
import type { FnDefWithOptions } from "@mx/shared/rpc";
import type { MessagePortMessageSendOptions } from "@mx/shared/rpc/adapter/msg-port";
import findYoutubePlayer from "./find-player";

const youtubeEmbedScreenshotHandler: FnDefWithOptions<
  Fn,
  MessagePortMessageSendOptions
> = {
  name,
  respWithOpts: true,
  async fn(options) {
    const video = findYoutubePlayer();
    if (!video) {
      throw new Error("Video element not found");
    }
    const payload = await captureScreenshot(video, options);
    return {
      payload,
      options: { transfer: [payload.blob.arrayBuffer] },
    };
  },
};

export default youtubeEmbedScreenshotHandler;
