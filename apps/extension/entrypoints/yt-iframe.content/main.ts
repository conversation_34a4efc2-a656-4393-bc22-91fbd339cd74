import { connInit<PERSON><PERSON> } from "@/lib/storage";
import captureScreenshot from "@/entrypoints/yt-iframe.content/screenshot";
import MessagePortAdapter from "@mx/shared/rpc/adapter/msg-port";
import {
  buildReceiverMessage,
  parseInitiatorMessage,
} from "@mx/ext-fn/app2iframe/port-init";
import "@mx/shared/polyfills/disposable";
import { Provider } from "@mx/shared/rpc";
import type { ContentScriptContext } from "wxt/client";

async function initConnection(port: MessagePort) {
  using stack = new DisposableStack();
  const adapter = new MessagePortAdapter(port);
  const provider = stack.use(Provider.init(adapter));
  provider.addFnHandler(captureScreenshot);
  return stack.move();
}

const allowedOrigins = new Set(["http://localhost:3781"]);

export default async function main(ctx: ContentScriptContext) {
  const protocolKey = await connInitKey.getValue();
  const stack = new AsyncDisposableStack();

  // init connection
  ctx.addEventListener(window, "message", async (event) => {
    if (!allowedOrigins.has(event.origin) || !event.source) return;
    const reqId = parseInitiatorMessage(event.data, protocolKey);
    if (!reqId) return;
    const channel = new MessageChannel();
    stack.use(await initConnection(channel.port1));
    // there is no "close" event for MessagePort for now
    // https://chromestatus.com/feature/5086799659794432
    const respMsg = buildReceiverMessage(reqId);
    event.source.postMessage(respMsg, {
      targetOrigin: event.origin,
      transfer: [channel.port2],
    });
  });

  ctx.onInvalidated(() => {
    stack.disposeAsync();
  });
}
