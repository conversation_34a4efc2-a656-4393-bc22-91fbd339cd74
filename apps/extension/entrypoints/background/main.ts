import "@mx/shared/polyfills/disposable";

import { connInitKey } from "@/lib/storage";
import { TabRegistry } from "@/lib/tab-proxy";
import initPWAConnection from "@/lib/bg-rpc/pwa";
import type { IPCContext } from "@/lib/bg-rpc/ctx";
import initPopupConnection from "@/lib/bg-rpc/popup";
import SingletonWebSocketConnection from "@/lib/bg-rpc/ws";

import {
  WebSocketConnection,
  type WebSocketConnectionEvents,
} from "@mx/shared/utils/ws";

export default function main() {
  if (process.env.NODE_ENV === "development") {
    chrome.declarativeNetRequest.onRuleMatchedDebug?.addListener((details) => {
      console.log("dnt", details.request.url, details);
    });
  }

  // init with a random key
  connInitKey.getValue();

  const tabProxy = new TabRegistry();
  const ctx: IPCContext = { tabProxy };

  const pwaConnection = initPWAConnection(
    { allowedOrigins: ["http://localhost:3781"] },
    ctx,
  );

  const wsConnection = new SingletonWebSocketConnection(ctx);
  wsConnection.connect();

  const popupConnection = initPopupConnection({
    ws: wsConnection,
  });

  // manifest v3 does not support onSuspend
  // https://groups.google.com/a/chromium.org/g/chromium-extensions/c/gX8RVBQpyEM
  // chrome.runtime.onSuspend.addListener(() => {
  //   ds.then((b) => b[Symbol.asyncDispose]());
  // });
}
