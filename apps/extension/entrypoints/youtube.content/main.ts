import buildContentScript from "@/lib/fn/build-cs";
import buildScreenshotFn from "@/lib/fn/screenshot/runtime";
import buildPlaybackFn from "@/lib/fn/playback/runtime";
import findYoutubePlayer from "./find-player";
import buildLinkOpenFn from "@/lib/fn/link-open/runtime";

const main = buildContentScript(async ({ provider }) => {
  provider.addFnHandler(buildScreenshotFn("youtube", findYoutubePlayer));
  provider.addFnHandler(buildPlaybackFn("youtube", findYoutubePlayer));
  provider.addFnHandler(buildLinkOpenFn("youtube", findYoutubePlayer));
  console.log("youtube content script inited");
});

export default main;
