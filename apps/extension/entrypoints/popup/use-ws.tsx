import {
  type PopupEventMap,
  type PopupFn,
  PopupFnName,
} from "@/lib/fn/popup/def";
import { defineConsumer, defineEventEmitter } from "@mx/shared/rpc";
import ExtensionInternalAdapter from "@mx/shared/rpc/adapter/ext-internal";
import type { WebSocketConnStatus } from "@mx/shared/utils/ws/state";

const EXT_BG_URL = chrome.runtime.getURL("background.js");

const adapter = new ExtensionInternalAdapter({
  filterSender: (sender) => sender.url === EXT_BG_URL,
});

const getWsConnStatus = defineConsumer(adapter).createRemoteFn<
  PopupFn["getWsConnStatus"]
>(PopupFnName.getWsConnStatus);

const reconnectWs = defineConsumer(adapter).createRemoteFn<
  PopupFn["reconnectWs"]
>(PopupFnName.reconnectWs);

export function useWebSocketConnection() {
  const [status, setStatus] = useState<WebSocketConnStatus | null>(null);
  useEffect(() => {
    getWsConnStatus().then(setStatus);
    return defineEventEmitter<PopupEventMap>()(adapter).addEventListener(
      "ws-status-changed",
      setStatus,
    );
  });

  return {
    status,
    reconnect: () => reconnectWs(),
  };
}
