#root {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
}

/* WebSocket Connection Status UI */
.connection-status-container {
  width: 300px;
  padding: 20px;
  margin: 0 auto;
  background-color: #f5f5f5;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.connection-status-container h2 {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 18px;
  color: #333;
}

.status-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
}

.status-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 8px;
}

.status-connected {
  background-color: #4caf50; /* Green */
}

.status-connecting {
  background-color: #ff9800; /* Orange */
}

.status-disconnected {
  background-color: #9e9e9e; /* Gray */
}

.status-failed {
  background-color: #f44336; /* Red */
}

.status-unknown {
  background-color: #9e9e9e; /* <PERSON> */
}

.status-text {
  font-size: 14px;
  font-weight: 500;
  text-transform: capitalize;
}

.connection-buttons {
  display: flex;
  gap: 10px;
  justify-content: center;
}

.action-button {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.action-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.reconnect-button {
  background-color: #2196f3; /* Blue */
  color: white;
}

.reconnect-button:hover:not(:disabled) {
  background-color: #0b7dda;
}

.refresh-button {
  background-color: #e0e0e0;
  color: #333;
}

.refresh-button:hover:not(:disabled) {
  background-color: #d0d0d0;
}

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #54bc4ae0);
}
.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}
