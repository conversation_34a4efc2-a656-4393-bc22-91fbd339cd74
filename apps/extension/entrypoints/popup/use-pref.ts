import useSWR from "swr";
import useSWRMutation from "swr/mutation";
import { remoteWsPortPref } from "@/lib/storage";

export function useWsPortPref() {
  const { data, isLoading, error } = useSWR<number>(
    "pref:remote-ws:port",
    async () => await remoteWsPortPref.getValue(),
  );
  const { trigger, isMutating } = useSWRMutation(
    "pref:remote-ws:port",
    async (_url, { arg }: { arg: number }) => {
      await remoteWsPortPref.setValue(arg);
    },
  );

  return {
    value: data,
    isLoading,
    error,
    trigger,
    isUpdating: isMutating,
  };
}
