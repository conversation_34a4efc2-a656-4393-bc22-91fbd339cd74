import type { WebSocketConnStatus } from "@mx/shared/utils/ws";

import { Loader2, Wifi, WifiOff, Settings2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useWebSocketConnection } from "@/entrypoints/popup/use-ws";
import { useWsPortPref } from "@/entrypoints/popup/use-pref";
import { Input } from "@/components/ui/input";
import { useState } from "react";
import { type } from "arktype";
import { toast } from "sonner";
import { assertNever } from "@std/assert/unstable-never";
import { ipPort } from "@mx/shared/validator/common";

const StatusLabel: Record<WebSocketConnStatus, string> = {
  disconnected: "Disconnected",
  connecting: "Connecting",
  connected: "Connected",
  waiting_to_retry: "Retrying",
  disconnecting: "Disconnecting",
};

function getStatusLabel(status: WebSocketConnStatus) {
  if (!(status in StatusLabel)) return "Unknown";
  return StatusLabel[status];
}

function App() {
  const { status, reconnect } = useWebSocketConnection();
  const {
    value: port,
    trigger: updatePort,
    isLoading: isPortLoading,
    isUpdating: isPortUpdating,
  } = useWsPortPref();
  const [showSettings, setShowSettings] = useState(false);
  const [portValue, setPortValue] = useState<string>("");
  const isLoading = status === "connecting" || status === "waiting_to_retry";

  const getStatusVariant = (status: WebSocketConnStatus | null) => {
    if (status === null) return "default";
    if (status === "connected") return "success";
    if (status === "connecting" || status === "waiting_to_retry")
      return "secondary";
    if (status === "disconnected" || status === "disconnecting")
      return "destructive";
    assertNever(status);
  };

  const handlePortChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setPortValue(e.target.value);
  };

  const handlePortUpdate = async () => {
    const port = ipPort(Number.parseInt(portValue));
    if (port instanceof type.errors) {
      toast.error("Invalid port number", {
        description: port.summary,
      });
      return;
    }
    await updatePort(port);
    reconnect();
    setShowSettings(false);
  };

  return (
    <main className="p-4 min-w-[320px]">
      <Card>
        <CardHeader className="pb-2">
          <div className="flex justify-between items-center">
            <CardTitle className="text-lg font-medium">
              WebSocket Connection
            </CardTitle>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => {
                setShowSettings(!showSettings);
                setPortValue(port?.toString() || "");
              }}
            >
              <Settings2 className="size-4" />
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {showSettings ? (
            <div className="space-y-2">
              <div className="text-sm font-medium">WebSocket Port</div>
              <div className="flex gap-2">
                <Input
                  type="number"
                  value={portValue}
                  onChange={handlePortChange}
                  placeholder="Port number"
                  min="1"
                  max="65534"
                />
                <Button
                  size="sm"
                  onClick={handlePortUpdate}
                  disabled={isPortLoading || isPortUpdating}
                >
                  Save
                </Button>
              </div>
              <div className="text-xs text-muted-foreground">
                Current port: {isPortLoading ? "Loading..." : port}
              </div>
            </div>
          ) : (
            <>
              <div className="flex items-center justify-center gap-2">
                {status === "connected" ? (
                  <Wifi className="size-4 text-green-500" />
                ) : (
                  <WifiOff className="size-4 text-gray-500" />
                )}
                <Badge
                  variant={getStatusVariant(status)}
                  className="font-normal"
                >
                  {status && getStatusLabel(status)}
                </Badge>
              </div>

              <div className="flex justify-center gap-2">
                <Button
                  variant="default"
                  onClick={reconnect}
                  disabled={isLoading}
                  size="sm"
                >
                  {isLoading && (
                    <Loader2 className="animate-spin mr-2 size-4" />
                  )}
                  Connect
                </Button>
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </main>
  );
}

export default App;
