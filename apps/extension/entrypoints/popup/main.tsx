import "@mx/shared/polyfills/disposable";
import "@/assets/tailwind.css";

import React from "react";
import ReactDOM from "react-dom/client";
import App from "./App.tsx";
import { Toaster } from "sonner";
import { themePref } from "@/lib/storage.ts";
import { ThemeProvider } from "@/hooks/use-theme.tsx";

themePref.getValue().then((theme) => {
  ReactDOM.createRoot(document.getElementById("root")!).render(
    <React.StrictMode>
      <ThemeProvider initialTheme={theme}>
        <App />
        <Toaster />
      </ThemeProvider>
    </React.StrictMode>,
  );
});
