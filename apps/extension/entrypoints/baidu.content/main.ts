import buildContentScript from "@/lib/fn/build-cs";
import buildScreenshotFn from "@/lib/fn/screenshot/runtime";
import buildPlaybackFn from "@/lib/fn/playback/runtime";
import findBaiduPanPlayer from "./find-player";
import buildLinkOpenFn from "@/lib/fn/link-open/runtime";

const main = buildContentScript(async ({ provider }) => {
  provider.addFnHandler(buildScreenshotFn("baidu-pan", findBaiduPanPlayer));
  provider.addFnHandler(buildPlaybackFn("baidu-pan", findBaiduPanPlayer));
  provider.addFnHandler(buildLinkOpenFn("baidu-pan", findBaiduPanPlayer));
  console.log("baidu pan content script inited");
});

export default main;
