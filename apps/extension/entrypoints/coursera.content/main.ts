import buildContentScript from "@/lib/fn/build-cs";
import buildScreenshotFn from "@/lib/fn/screenshot/runtime";
import buildPlaybackFn from "@/lib/fn/playback/runtime";
import findCourseraPlayer from "./find-player";
import buildLinkOpenFn from "@/lib/fn/link-open/runtime";

const main = buildContentScript(async ({ provider }) => {
  provider.addFnHandler(buildScreenshotFn("coursera", findCourseraPlayer));
  provider.addFnHandler(buildPlaybackFn("coursera", findCourseraPlayer));
  provider.addFnHandler(buildLinkOpenFn("coursera", findCourseraPlayer));
  console.log("coursera content script inited");
});

export default main;
