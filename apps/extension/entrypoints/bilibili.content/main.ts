import buildContentScript from "@/lib/fn/build-cs";
import buildScreenshotFn from "@/lib/fn/screenshot/runtime";
import buildPlaybackFn from "@/lib/fn/playback/runtime";
import findBilibiliPlayer from "./find-player";
import buildLinkOpenFn from "@/lib/fn/link-open/runtime";

const main = buildContentScript(async ({ provider }) => {
  provider.addFnHandler(buildScreenshotFn("bilibili", findBilibiliPlayer));
  provider.addFnHandler(buildPlaybackFn("bilibili", findBilibiliPlayer));
  provider.addFnHandler(buildLinkOpenFn("bilibili", findBilibiliPlayer));
  console.log("bilibili content script inited");
});

export default main;
