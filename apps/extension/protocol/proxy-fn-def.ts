import type { BrowserVid } from "@mx/ext-fn/app2ext/playback";
import type { ScreenshotTarget } from "@mx/ext-fn/app2ext/screenshot";
import type { PlaybackAction } from "@mx/ext-fn/playback-actions";
import type { ScreenshotResponseJSON } from "@mx/shared/dom/screenshot";
import type { ScreenshotOptions } from "@mx/shared/dom/screenshot";
import type { Host } from "@mx/shared/url-parse/const";
import type { HashProps } from "@mx/shared/utils/hash-prop";

export type ProxyFns = {
  screenshot: (options?: ScreenshotOptions) => Promise<ScreenshotResponseJSON>;
  playback: (action: PlaybackAction) => Promise<void>;
  "link-open": (hash?: HashProps) => Promise<void>;
};

export type ProxyFnTarget = {
  screenshot: ScreenshotTarget;
  playback: BrowserVid;
  "link-open": BrowserVid;
};

export type ProxyFnAction = keyof ProxyFns;
export type ProxyTarget = ProxyFnTarget[ProxyFnAction]["host"];

// Function name pattern: `proxy:${action}:${host}`
export function proxyFnName(
  action: ProxyFnAction,
  host: Host,
): `proxy:${ProxyFnAction}:${Host}` {
  return `proxy:${action}:${host}`;
}

// export const proxyFnActions: {
//   [K in ProxyFnAction]: ProxyFnTarget[K][];
// } = {
//   screenshot: ["bilibili", "coursera", "baidu-pan"],
//   playback: ["bilibili", "coursera", "baidu-pan"],
// };
// import { enumerate } from "@mx/shared/utils/must-include";
// const a = enumerate<ProxyFnTarget["playback"]["host"]>()(
//   "bilibili",
//   "coursera",
//   "baidu-pan",
//   "youtube",
// );
