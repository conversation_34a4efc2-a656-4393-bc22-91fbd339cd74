/**
 * Calculate the greatest common divisor of two numbers using <PERSON><PERSON><PERSON>'s algorithm
 */
function gcd(a: number, b: number): number {
  let x = a;
  let y = b;
  while (y !== 0) {
    const temp = y;
    y = x % y;
    x = temp;
  }
  return x;
}

/**
 * Reduce aspect ratio to lowest terms
 * @param width - Width value
 * @param height - Height value
 * @returns Aspect ratio in lowest terms as "width / height" string
 *
 * @example
 * reduceAspectRatio(1920, 1080) // returns "16 / 9"
 * reduceAspectRatio(1280, 720) // returns "16 / 9"
 * reduceAspectRatio(800, 600) // returns "4 / 3"
 */
export function reduceAspectRatio(width: number, height: number): string {
  if (width <= 0 || height <= 0) {
    return `${width} / ${height}`;
  }

  const divisor = gcd(width, height);
  const reducedWidth = width / divisor;
  const reducedHeight = height / divisor;

  return `${reducedWidth} / ${reducedHeight}`;
}
