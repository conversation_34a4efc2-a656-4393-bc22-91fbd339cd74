import {
  configure,
  getConsoleSink,
  getJson<PERSON>inesFormatter,
  getTextFormatter,
  type LogLevel,
} from "@logtape/logtape";
import { AsyncLocalStorage } from "node:async_hooks";

export interface LoggingConfig {
  environment: "development" | "testing" | "production";
  logLevel: LogLevel;
  enableConsole: boolean;
  enableStructuredOutput: boolean;
  nonBlocking: boolean;
}

import { env } from "cloudflare:workers";

function getEnvironment(): LoggingConfig["environment"] {
  const environment = env.WORKER_ENV;
  if (environment === "production") return "production";
  return "development";
}

function createLoggingConfig(env: LoggingConfig["environment"]): LoggingConfig {
  switch (env) {
    case "development":
      return {
        environment: env,
        logLevel: "trace",
        enableConsole: true,
        enableStructuredOutput: false,
        nonBlocking: false,
      };
    case "testing":
      return {
        environment: env,
        logLevel: "error",
        enableConsole: false,
        enableStructuredOutput: false,
        nonBlocking: true,
      };
    case "production":
      return {
        environment: env,
        logLevel: "info",
        enableConsole: true,
        // cloudflare logging only supports structured output
        enableStructuredOutput: true,
        // cloudflare have trouble with non-blocking logging
        nonBlocking: false,
      };
  }
}

export async function configureLogging(): Promise<void> {
  const environment = getEnvironment();
  const config = createLoggingConfig(environment);
  await configure({
    sinks: {
      console: getConsoleSink({
        nonBlocking: config.nonBlocking,
        formatter: config.enableStructuredOutput
          ? getJsonLinesFormatter()
          : getTextFormatter(),
      }),
    },
    loggers: [
      {
        category: ["logtape", "meta"],
        lowestLevel: "warning",
        sinks: config.enableConsole ? ["console"] : [],
      },
      {
        category: ["mx-api"],
        lowestLevel: config.logLevel,
        sinks: config.enableConsole ? ["console"] : [],
      },
    ],
    contextLocalStorage: new AsyncLocalStorage(),
  });
}

export function redactSensitiveData(
  obj: Record<string, any>,
): Record<string, any> {
  const sensitiveKeys = [
    "authorization",
    "token",
    "api_key",
    "apikey",
    "secret",
    "password",
    "auth",
    "bearer",
    "key",
  ];

  const redacted = { ...obj };

  for (const [key, value] of Object.entries(redacted)) {
    if (
      sensitiveKeys.some((sensitive) => key.toLowerCase().includes(sensitive))
    ) {
      redacted[key] = "[REDACTED]";
    } else if (typeof value === "object" && value !== null) {
      redacted[key] = redactSensitiveData(value);
    }
  }

  return redacted;
}
