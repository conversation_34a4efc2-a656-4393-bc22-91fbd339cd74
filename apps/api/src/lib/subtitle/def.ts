export function formatTime(seconds: number): string {
  const pad = (num: number): string => num.toString().padStart(2, "0");
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  const ms = Math.floor((seconds % 1) * 1000);
  const base = `${pad(minutes)}:${pad(secs)}.${ms.toString().padStart(3, "0")}`;
  if (hours === 0) {
    return base;
  }
  return `${pad(hours)}:${base}`;
}

export interface Transcript {
  language?: string;
  duration: number;
  text: string;
  segments: Segment[];
}

export interface Segment {
  id: number;
  text: string;
  start: number;
  end: number;
  language?: string;
  speaker: string;
  words?: Word[];
}

export interface Word {
  word: string;
  start: number;
  end: number;
}

export function timeEq(a: number | undefined, b: number) {
  if (a === undefined) return false;
  return Math.abs(a - b) < 0.1;
}
