# Schema for YouTube internal json3 caption

1. The file contains styling and positioning information (`pens`, `wsWinStyles`, `wpWinPositions`)
2. The actual transcript content is in the `events` array
3. Events can be of different types:
   - Window setup events (with `id`, `wpWinPosId`, `wsWinStyleId`)
   - Text content events (with `segs` containing the actual transcript)
   - Line break events (with `aAppend` flag and newline character)
4. Each text segment (`segs`) contains:
   - The actual text (`utf8`)
   - Timing information (`tOffsetMs`)
   - Confidence scores (`acAsrConf`)
5. All timing is in milliseconds

It's YouTube's internal caption format that includes not just the text and timing, but also styling and positioning information for the captions.

Specific schema defined in typescript

```typescript
interface TranscriptFile {
  wireMagic: string;  // Appears to be a constant "pb3"
  pens: Array<object>; // Array of styling objects
  wsWinStyles: Array<{
    mhModeHint?: number;    // Window mode hint
    juJustifCode?: number;  // Justification code
    sdScrollDir?: number;   // Scroll direction
  }>;
  wpWinPositions: Array<{
    apPoint?: number;       // Anchor point
    ahHorPos?: number;      // Horizontal position
    avVerPos?: number;      // Vertical position
    rcRows?: number;        // Number of rows
    ccCols?: number;        // Number of columns
  }>;
  events: Array<TranscriptEvent>;
}

interface TranscriptEvent {
  tStartMs: number;         // Start time in milliseconds
  dDurationMs?: number;     // Duration in milliseconds
  wWinId?: number;         // Window ID
  id?: number;             // Event ID
  wpWinPosId?: number;     // Window position ID
  wsWinStyleId?: number;   // Window style ID
  aAppend?: number;        // Append flag (1 = append)
  segs?: Array<{
    utf8: string;          // The actual text content
    tOffsetMs?: number;    // Time offset from start in milliseconds
    acAsrConf?: number;    // ASR confidence score
  }>;
}
```

Example file:

```json
{
  "wireMagic": "pb3",
  "pens": [
    {}
  ],
  "wsWinStyles": [
    {},
    {
      "mhModeHint": 2,
      "juJustifCode": 0,
      "sdScrollDir": 3
    }
  ],
  "wpWinPositions": [
    {},
    {
      "apPoint": 6,
      "ahHorPos": 20,
      "avVerPos": 100,
      "rcRows": 2,
      "ccCols": 40
    }
  ],
  "events": [
    {
      "tStartMs": 0,
      "dDurationMs": 214000,
      "id": 1,
      "wpWinPosId": 1,
      "wsWinStyleId": 1
    },
    {
      "tStartMs": 0,
      "dDurationMs": 14650,
      "wWinId": 1,
      "segs": [
        {
          "utf8": "[Music]"
        }
      ]
    },
    {
      "tStartMs": 18790,
      "wWinId": 1,
      "aAppend": 1,
      "segs": [
        {
          "utf8": "\n"
        }
      ]
    },
    {
      "tStartMs": 18800,
      "dDurationMs": 7239,
      "wWinId": 1,
      "segs": [
        {
          "utf8": "we're",
          "acAsrConf": 0
        },
        {
          "utf8": " no",
          "tOffsetMs": 239,
          "acAsrConf": 0
        },
        {
          "utf8": " strangers",
          "tOffsetMs": 479,
          "acAsrConf": 0
        },
        {
          "utf8": " to",
          "tOffsetMs": 1000,
          "acAsrConf": 0
        }
      ]
    },
    {
      "tStartMs": 21790,
      "dDurationMs": 4249,
      "wWinId": 1,
      "aAppend": 1,
      "segs": [
        {
          "utf8": "\n"
        }
      ]
    },
    {
      "tStartMs": 21800,
      "dDurationMs": 7840,
      "wWinId": 1,
      "segs": [
        {
          "utf8": "love",
          "acAsrConf": 0
        },
        {
          "utf8": " you",
          "tOffsetMs": 1000,
          "acAsrConf": 0
        },
        {
          "utf8": " know",
          "tOffsetMs": 1200,
          "acAsrConf": 0
        },
        {
          "utf8": " the",
          "tOffsetMs": 1520,
          "acAsrConf": 0
        },
        {
          "utf8": " rules",
          "tOffsetMs": 1719,
          "acAsrConf": 0
        },
        {
          "utf8": " and",
          "tOffsetMs": 2600,
          "acAsrConf": 0
        },
        {
          "utf8": " so",
          "tOffsetMs": 2760,
          "acAsrConf": 0
        },
        {
          "utf8": " do",
          "tOffsetMs": 3360,
          "acAsrConf": 0
        }
      ]
    }
  ]
}
```
