import { type Transcript, formatTime, type Word, timeEq } from "./def";

export function lemonfoxJsonToWebVTT(transcript: Transcript): string {
  let output = "WEBVTT\n";

  if (transcript.language) {
    output += `Language: ${transcript.language}\n`;
  }
  output += "\n";

  transcript.segments.forEach((segment, index) => {
    const startTime = formatTime(segment.start);
    const endTime = formatTime(segment.end);
    output += `${index + 1}\n`;
    output += `${startTime} --> ${endTime}\n`;

    let currentContent = "";
    if (segment.words) {
      // 1. aggregating words in the segment with same start time,
      const aggregatedWords = segment.words.reduce<Word[]>((acc, word) => {
        const lastWord = acc[acc.length - 1];
        if (!lastWord) {
          acc.push(word);
          return acc;
        }
        if (timeEq(lastWord.start, word.start)) {
          lastWord.word += ` ${word.word}`;
          lastWord.end = word.end;
        } else {
          acc.push(word);
        }
        return acc;
      }, []);

      // insert timestamp around each word, avoid duplicate timestamp next to each other, only lookahead
      // avoid leading and trailing timestamp that is same as the start and end of the segment
      aggregatedWords.forEach((word, idx, arr) => {
        const prevWord = arr[idx - 1];

        // only add end timestamp if it's not the same as the next word start
        if (prevWord && !timeEq(prevWord.end, word.start)) {
          currentContent += `<${formatTime(prevWord.end)}>`;
        }
        // avoid leading timestamp same as segment start
        if (prevWord || !timeEq(word.start, segment.start)) {
          currentContent += `<${formatTime(word.start)}>`;
        }
        currentContent += `${word.word} `;
      });
      // trailing timestamp
      const lastWord = aggregatedWords[aggregatedWords.length - 1];
      if (lastWord && !timeEq(lastWord.end, segment.end)) {
        currentContent += `<${formatTime(lastWord.end)}>`;
      }
    } else {
      currentContent = segment.text;
    }
    if (segment.speaker) {
      currentContent = `<v ${segment.speaker}>${currentContent}</v>`;
    }

    output += `${currentContent.trim()}\n\n`;
  });

  return output.trim();
}
