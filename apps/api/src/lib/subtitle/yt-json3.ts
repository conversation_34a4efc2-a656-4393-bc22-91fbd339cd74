import type { Segment, Transcript, Word } from "./def";

interface Json3Transcript {
  wireMagic: string;
  events: Array<{
    tStartMs: number;
    dDurationMs?: number;
    segs?: Array<{
      utf8: string;
      tOffsetMs?: number;
      acAsrConf?: number;
    }>;
  }>;
}

export function parseYouTubeAutoSub(
  json3: Json3Transcript,
  language?: string,
): Transcript {
  const segments: Segment[] = [];
  let currentSegmentId = 0;

  // Process each event that has text segments
  json3.events.forEach((event, index, array) => {
    if (!event.segs || event.segs.length === 0) return;

    const startTimeMs = event.tStartMs;
    const durationMs = event.dDurationMs || 0;
    const nextStartTimeMs =
      array[index + 1]?.tStartMs || Number.POSITIVE_INFINITY;
    // prevent overlapping cues
    const endTimeMs = Math.min(startTimeMs + durationMs, nextStartTimeMs);

    // Collect words with confidence scores
    const words: Word[] = event.segs
      .map((seg) => ({
        word: seg.utf8.trim(),
        start: (startTimeMs + (seg.tOffsetMs || 0)) / 1000, // Convert to seconds only at the end
        end: -1, // Temporary value, will be set correctly below
        score: Number.NaN,
      }))
      .filter((word) => word.word !== "");

    // Set end times using next word's start time
    words.forEach((word, idx) => {
      if (idx === words.length - 1) {
        // For the last word, use the event's end time
        word.end = endTimeMs / 1000;
      } else {
        // For other words, use next word's start time
        word.end = words[idx + 1]!.start;
      }
    });

    // Combine all text segments
    const text = event.segs
      .map((seg) => seg.utf8)
      .join("")
      .trim();

    if (text) {
      segments.push({
        id: currentSegmentId++,
        text,
        start: startTimeMs / 1000,
        end: endTimeMs / 1000,
        words: words.length > 0 ? words : undefined,
        speaker: "",
        language,
      });
    }
  });

  return {
    language,
    duration: Number.NaN, // externally provided
    text: json3.events
      .flatMap((event) => event.segs?.map((seg) => seg.utf8) ?? [])
      .join(""),
    segments,
  };
}
