import { retry } from "@std/async";
import { HTTPException } from "hono/http-exception";
import { parseYouTubeAutoSub } from "./yt-json3";
import { lemonfoxJsonToWebVTT } from "./lemonfox";
import { getLogger } from "@logtape/logtape";
import { fetchWithRetry } from "../fetch";

const logger = getLogger(["mx-api", "subtitle", "fetch-json3"]);

export async function processYouTubeJson3Subtitle(
  subtitleUrl: string,
  { videoId, subtitleId }: { videoId: string; subtitleId: string },
) {
  logger.debug("Fetching subtitle from URL", {
    videoId,
    subtitleId,
  });

  const resp = await fetchWithRetry(subtitleUrl);

  if (!resp.ok) {
    logger.error("Failed to fetch subtitle", {
      videoId,
      subtitleId,
      status: resp.status,
      statusText: resp.statusText,
    });
    throw new HTTPException();
  }

  try {
    const rawData = await resp.json();
    logger.debug("Parsing YouTube auto-generated subtitle", {
      videoId,
      subtitleId,
    });

    const transcript = parseYouTubeAutoSub(rawData as any);
    const vtt = lemonfoxJsonToWebVTT(transcript);

    logger.debug("Subtitle processing completed", {
      videoId,
      subtitleId,
      vttLength: vtt.length,
    });

    return vtt;
  } catch (e) {
    logger.error("Failed to process subtitle content", {
      videoId,
      subtitleId,
      error: e instanceof Error ? e.message : String(e),
    });
    throw new HTTPException();
  }
}
