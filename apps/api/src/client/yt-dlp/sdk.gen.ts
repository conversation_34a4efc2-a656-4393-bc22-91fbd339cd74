// This file is auto-generated by @hey-api/openapi-ts

import type { Options as ClientOptions, TDataShape, Client } from './client';
import type { RunSyncGetDatasetItemsAidenlxYtDlpData, RunSyncGetDatasetItemsAidenlxYtDlpResponses, RunsSyncAidenlxYtDlpData, RunsSyncAidenlxYtDlpResponses, RunSyncAidenlxYtDlpData, RunSyncAidenlxYtDlpResponses } from './types.gen';
import { client as _heyApiClient } from './client.gen';

export type Options<TData extends TDataShape = TDataShape, ThrowOnError extends boolean = boolean> = ClientOptions<TData, ThrowOnError> & {
    /**
     * You can provide a client instance returned by `createClient()` instead of
     * individual options. This might be also useful if you want to implement a
     * custom client.
     */
    client?: Client;
    /**
     * You can pass arbitrary values through the `meta` object. This can be
     * used to access values that aren't defined as part of the SDK function.
     */
    meta?: Record<string, unknown>;
};

/**
 * Executes an Actor, waits for its completion, and returns Actor's dataset items in response.
 */
export const runSyncGetDatasetItemsAidenlxYtDlp = <ThrowOnError extends boolean = false>(options: Options<RunSyncGetDatasetItemsAidenlxYtDlpData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<RunSyncGetDatasetItemsAidenlxYtDlpResponses, unknown, ThrowOnError>({
        url: '/acts/aidenlx~yt-dlp/run-sync-get-dataset-items',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Executes an Actor and returns information about the initiated run in response.
 */
export const runsSyncAidenlxYtDlp = <ThrowOnError extends boolean = false>(options: Options<RunsSyncAidenlxYtDlpData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<RunsSyncAidenlxYtDlpResponses, unknown, ThrowOnError>({
        url: '/acts/aidenlx~yt-dlp/runs',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Executes an Actor, waits for completion, and returns the OUTPUT from Key-value store in response.
 */
export const runSyncAidenlxYtDlp = <ThrowOnError extends boolean = false>(options: Options<RunSyncAidenlxYtDlpData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<RunSyncAidenlxYtDlpResponses, unknown, ThrowOnError>({
        url: '/acts/aidenlx~yt-dlp/run-sync',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};