/* eslint-disable */
/**
 * This file was automatically generated by json-schema-to-typescript.
 * DO NOT MODIFY IT BY HAND. Instead, modify the source JSONSchema file,
 * and run json-schema-to-typescript to regenerate this file.
 */

/**
 * Comprehensive schema for yt-dlp video information JSON output
 */
export interface YtDlpInfoJSONSchema {
  /**
   * Video ID
   */
  id: string;
  /**
   * Video title
   */
  title: string;
  /**
   * Video description
   */
  description?: string | null;
  /**
   * Format description
   */
  format?: string;
  /**
   * Format sorting fields
   */
  _format_sort_fields?: string[];
  /**
   * Whether content has DRM
   */
  _has_drm?: boolean | null;
  /**
   * Audio bitrate
   */
  abr?: number | null;
  /**
   * Audio codec
   */
  acodec?: string | null;
  /**
   * Age restriction limit
   */
  age_limit?: number | null;
  /**
   * Aspect ratio
   */
  aspect_ratio?: number | null;
  /**
   * Audio sample rate
   */
  asr?: number | null;
  /**
   * Number of audio channels
   */
  audio_channels?: number | null;
  /**
   * Audio file extension
   */
  audio_ext?: string;
  /**
   * Available automatic captions by language code
   */
  automatic_captions?: {
    /**
     * This interface was referenced by `undefined`'s JSON-Schema definition
     * via the `patternProperty` "^[a-z]{2}(-[A-Z]{2})?(-orig)?$".
     */
    [k: string]: {
      __yt_dlp_client?: string | null;
      ext: string;
      protocol?: string;
      url: string;
      name?: string;
      [k: string]: unknown;
    }[];
  };
  /**
   * Video availability status
   */
  availability?:
    | "public"
    | "unlisted"
    | "private"
    | "premium_only"
    | "subscriber_only"
    | "needs_auth"
    | "not_live"
    | null;
  /**
   * Average user rating
   */
  average_rating?: number | null;
  /**
   * Video categories
   */
  categories?: string[];
  /**
   * Channel name
   */
  channel?: string | null;
  /**
   * Channel follower count
   */
  channel_follower_count?: number | null;
  /**
   * Channel ID
   */
  channel_id?: string | null;
  /**
   * Channel URL
   */
  channel_url?: string | null;
  /**
   * Video chapters
   */
  chapters?: {
    title: string;
    end_time: number;
    start_time: number;
    [k: string]: unknown;
  }[];
  /**
   * Number of comments
   */
  comment_count?: number | null;
  /**
   * Display ID
   */
  display_id?: string;
  /**
   * Downloader options
   */
  downloader_options?: {
    [k: string]: unknown;
  } | null;
  /**
   * Video duration in seconds
   */
  duration?: number | null;
  /**
   * Duration as string (HH:MM:SS format)
   */
  duration_string?: string | null;
  /**
   * Dynamic range (SDR/HDR)
   */
  dynamic_range?: "SDR" | "HDR10" | "HDR10+" | "DV" | null;
  /**
   * Epoch timestamp
   */
  epoch?: number | null;
  /**
   * File extension
   */
  ext?: string;
  /**
   * Extractor name
   */
  extractor: string;
  /**
   * Extractor key
   */
  extractor_key: string;
  /**
   * File size in bytes
   */
  filesize?: number | null;
  /**
   * Approximate file size
   */
  filesize_approx?: number | null;
  /**
   * Selected format ID
   */
  format_id?: string;
  /**
   * Format note
   */
  format_note?: string | null;
  /**
   * Available video/audio formats
   */
  formats?: {
    format?: string;
    abr?: number | null;
    acodec?: string | null;
    aspect_ratio?: number | null;
    audio_ext?: string;
    columns?: number | null;
    ext?: string;
    filesize_approx?: number | null;
    format_id: string;
    format_note?: string | null;
    fps?: number | null;
    fragments?:
      | {
          duration?: number;
          url?: string;
          [k: string]: unknown;
        }[]
      | null;
    height?: number | null;
    http_headers?: {
      [k: string]: string;
    };
    protocol?: string;
    resolution?: string | null;
    rows?: number | null;
    tbr?: number | null;
    url: string;
    vbr?: number | null;
    vcodec?: string | null;
    video_ext?: string;
    width?: number | null;
    [k: string]: unknown;
  }[];
  /**
   * Frames per second
   */
  fps?: number | null;
  /**
   * Full title
   */
  fulltitle?: string;
  /**
   * Whether content has DRM
   */
  has_drm?: boolean | null;
  /**
   * Video heatmap data
   */
  heatmap?:
    | {
        end_time: number;
        start_time: number;
        value: number;
        [k: string]: unknown;
      }[]
    | null;
  /**
   * Video height
   */
  height?: number | null;
  /**
   * HTTP headers for download
   */
  http_headers?: {
    [k: string]: string;
  } | null;
  /**
   * Whether this is a live stream
   */
  is_live?: boolean | null;
  /**
   * Language code
   */
  language?: string | null;
  /**
   * Language preference
   */
  language_preference?: number | null;
  /**
   * Number of likes
   */
  like_count?: number | null;
  /**
   * Live stream status
   */
  live_status?: "not_live" | "is_live" | "is_upcoming" | "was_live" | null;
  /**
   * Media type
   */
  media_type?: string | null;
  /**
   * Original URL
   */
  original_url?: string;
  /**
   * Whether video can be embedded
   */
  playable_in_embed?: boolean | null;
  /**
   * Playlist name
   */
  playlist?: string | null;
  /**
   * Index in playlist
   */
  playlist_index?: number | null;
  /**
   * General preference
   */
  preference?: number | null;
  /**
   * Download protocol
   */
  protocol?: string;
  /**
   * Quality rating
   */
  quality?: number | null;
  /**
   * Release timestamp
   */
  release_timestamp?: number | null;
  /**
   * Release year
   */
  release_year?: number | null;
  /**
   * Requested subtitles
   */
  requested_subtitles?: {
    [k: string]: unknown;
  } | null;
  /**
   * Video resolution
   */
  resolution?: string | null;
  /**
   * Source preference
   */
  source_preference?: number | null;
  /**
   * Available subtitles by language code
   */
  subtitles?: {
    /**
     * This interface was referenced by `undefined`'s JSON-Schema definition
     * via the `patternProperty` "^[a-z]{2}(-[A-Z]{2})?$".
     */
    [k: string]: {
      ext: string;
      protocol?: string;
      url: string;
      name?: string;
      [k: string]: unknown;
    }[];
  };
  /**
   * Video tags
   */
  tags?: string[];
  /**
   * Total bitrate
   */
  tbr?: number | null;
  /**
   * Default thumbnail URL
   */
  thumbnail?: string | null;
  /**
   * Available thumbnail images
   */
  thumbnails?: {
    id?: string;
    height?: number | null;
    preference?: number | null;
    url: string;
    width?: number | null;
    [k: string]: unknown;
  }[];
  /**
   * Upload timestamp
   */
  timestamp?: number | null;
  /**
   * Upload date (YYYYMMDD format)
   */
  upload_date?: string | null;
  /**
   * Uploader name
   */
  uploader?: string | null;
  /**
   * Uploader ID
   */
  uploader_id?: string | null;
  /**
   * Uploader URL
   */
  uploader_url?: string | null;
  /**
   * Download URL
   */
  url?: string;
  /**
   * Video bitrate
   */
  vbr?: number | null;
  /**
   * Video codec
   */
  vcodec?: string | null;
  /**
   * Video file extension
   */
  video_ext?: string;
  /**
   * Number of views
   */
  view_count?: number | null;
  /**
   * Whether this was a live stream
   */
  was_live?: boolean | null;
  /**
   * Original webpage URL
   */
  webpage_url: string;
  /**
   * Webpage URL basename
   */
  webpage_url_basename?: string;
  /**
   * Webpage URL domain
   */
  webpage_url_domain?: string;
  /**
   * Video width
   */
  width?: number | null;
  [k: string]: unknown;
}
