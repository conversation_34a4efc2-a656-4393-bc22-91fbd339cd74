// This file is auto-generated by @hey-api/openapi-ts

export type InputSchema = {
    /**
     * YouTube URL
     * The YouTube video URL to extract information from
     */
    youtube_url: string;
    /**
     * Use Apify Proxy
     * Whether to use Apify residential proxy
     */
    use_apify_proxy?: boolean;
    /**
     * Custom Proxy URL
     * Custom proxy URL (http:// or https://). If provided, this will be used instead of Apify proxy
     */
    custom_proxy?: string;
};

export type RunsResponseSchema = {
    data?: {
        id?: string;
        actId?: string;
        userId?: string;
        startedAt?: string;
        finishedAt?: string;
        status?: string;
        meta?: {
            origin?: string;
            userAgent?: string;
        };
        stats?: {
            inputBodyLen?: number;
            rebootCount?: number;
            restartCount?: number;
            resurrectCount?: number;
            computeUnits?: number;
        };
        options?: {
            build?: string;
            timeoutSecs?: number;
            memoryMbytes?: number;
            diskMbytes?: number;
        };
        buildId?: string;
        defaultKeyValueStoreId?: string;
        defaultDatasetId?: string;
        defaultRequestQueueId?: string;
        buildNumber?: string;
        containerUrl?: string;
        usage?: {
            ACTOR_COMPUTE_UNITS?: number;
            DATASET_READS?: number;
            DATASET_WRITES?: number;
            KEY_VALUE_STORE_READS?: number;
            KEY_VALUE_STORE_WRITES?: number;
            KEY_VALUE_STORE_LISTS?: number;
            REQUEST_QUEUE_READS?: number;
            REQUEST_QUEUE_WRITES?: number;
            DATA_TRANSFER_INTERNAL_GBYTES?: number;
            DATA_TRANSFER_EXTERNAL_GBYTES?: number;
            PROXY_RESIDENTIAL_TRANSFER_GBYTES?: number;
            PROXY_SERPS?: number;
        };
        usageTotalUsd?: number;
        usageUsd?: {
            ACTOR_COMPUTE_UNITS?: number;
            DATASET_READS?: number;
            DATASET_WRITES?: number;
            KEY_VALUE_STORE_READS?: number;
            KEY_VALUE_STORE_WRITES?: number;
            KEY_VALUE_STORE_LISTS?: number;
            REQUEST_QUEUE_READS?: number;
            REQUEST_QUEUE_WRITES?: number;
            DATA_TRANSFER_INTERNAL_GBYTES?: number;
            DATA_TRANSFER_EXTERNAL_GBYTES?: number;
            PROXY_RESIDENTIAL_TRANSFER_GBYTES?: number;
            PROXY_SERPS?: number;
        };
    };
};

export type RunSyncGetDatasetItemsAidenlxYtDlpData = {
    body: InputSchema;
    path?: never;
    query: {
        /**
         * Enter your Apify token here
         */
        token: string;
    };
    url: '/acts/aidenlx~yt-dlp/run-sync-get-dataset-items';
};

export type RunSyncGetDatasetItemsAidenlxYtDlpResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type RunsSyncAidenlxYtDlpData = {
    body: InputSchema;
    path?: never;
    query: {
        /**
         * Enter your Apify token here
         */
        token: string;
    };
    url: '/acts/aidenlx~yt-dlp/runs';
};

export type RunsSyncAidenlxYtDlpResponses = {
    /**
     * OK
     */
    200: RunsResponseSchema;
};

export type RunsSyncAidenlxYtDlpResponse = RunsSyncAidenlxYtDlpResponses[keyof RunsSyncAidenlxYtDlpResponses];

export type RunSyncAidenlxYtDlpData = {
    body: InputSchema;
    path?: never;
    query: {
        /**
         * Enter your Apify token here
         */
        token: string;
    };
    url: '/acts/aidenlx~yt-dlp/run-sync';
};

export type RunSyncAidenlxYtDlpResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type ClientOptions = {
    baseUrl: 'https://api.apify.com/v2' | (string & {});
};