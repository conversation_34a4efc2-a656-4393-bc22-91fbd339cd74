// This file is auto-generated by @hey-api/openapi-ts

import type { Options as ClientOptions, TDataShape, Client } from './client';
import type { ActsGetData, ActsGetResponses, ActsPostData, ActsPostResponses, ActDeleteData, ActDeleteResponses, ActGetData, ActGetResponses, ActPutData, ActPutResponses, ActVersionsGetData, ActVersionsGetResponses, ActVersionsPostData, ActVersionsPostResponses, ActVersionDeleteData, ActVersionDeleteResponses, ActVersionGetData, ActVersionGetResponses, ActVersionPutData, ActVersionPutResponses, ActVersionEnvVarsGetData, ActVersionEnvVarsGetResponses, ActVersionEnvVarsPostData, ActVersionEnvVarsPostResponses, ActVersionEnvVarDeleteData, ActVersionEnvVarDeleteResponses, ActVersionEnvVarGetData, ActVersionEnvVarGetResponses, ActVersionEnvVarPutData, ActVersionEnvVarPutResponses, ActWebhooksGetData, ActWebhooksGetResponses, ActBuildsGetData, ActBuildsGetResponses, ActBuildsPostData, ActBuildsPostResponses, ActBuildDefaultGetData, ActBuildDefaultGetResponses, ActOpenapiJsonGetData, ActOpenapiJsonGetResponses, ActBuildGetData, ActBuildGetResponses, ActBuildAbortPostData, ActBuildAbortPostResponses, ActRunsGetData, ActRunsGetResponses, ActRunsPostData, ActRunsPostResponses, ActRunSyncGetData, ActRunSyncGetResponses, ActRunSyncGetErrors, ActRunSyncPostData, ActRunSyncPostResponses, ActRunSyncPostErrors, ActRunSyncGetDatasetItemsGetData, ActRunSyncGetDatasetItemsGetResponses, ActRunSyncGetDatasetItemsGetErrors, ActRunSyncGetDatasetItemsPostData, ActRunSyncGetDatasetItemsPostResponses, ActRunSyncGetDatasetItemsPostErrors, ActRunResurrectPostData, ActRunResurrectPostResponses, ActRunsLastGetData, ActRunsLastGetResponses, ActRunGetData, ActRunGetResponses, ActRunAbortPostData, ActRunAbortPostResponses, ActRunMetamorphPostData, ActRunMetamorphPostResponses, ActorTasksGetData, ActorTasksGetResponses, ActorTasksPostData, ActorTasksPostResponses, ActorTaskDeleteData, ActorTaskDeleteResponses, ActorTaskGetData, ActorTaskGetResponses, ActorTaskPutData, ActorTaskPutResponses, ActorTaskInputGetData, ActorTaskInputGetResponses, ActorTaskInputPutData, ActorTaskInputPutResponses, ActorTaskWebhooksGetData, ActorTaskWebhooksGetResponses, ActorTaskRunsGetData, ActorTaskRunsGetResponses, ActorTaskRunsPostData, ActorTaskRunsPostResponses, ActorTaskRunSyncGetData, ActorTaskRunSyncGetResponses, ActorTaskRunSyncGetErrors, ActorTaskRunSyncPostData, ActorTaskRunSyncPostResponses, ActorTaskRunSyncPostErrors, ActorTaskRunSyncGetDatasetItemsGetData, ActorTaskRunSyncGetDatasetItemsGetResponses, ActorTaskRunSyncGetDatasetItemsGetErrors, ActorTaskRunSyncGetDatasetItemsPostData, ActorTaskRunSyncGetDatasetItemsPostResponses, ActorTaskRunSyncGetDatasetItemsPostErrors, ActorTaskRunsLastGetData, ActorTaskRunsLastGetResponses, ActorRunsGetData, ActorRunsGetResponses, ActorRunDeleteData, ActorRunDeleteResponses, ActorRunGetData, ActorRunGetResponses, ActorRunPutData, ActorRunPutResponses, ActorRunAbortPostData, ActorRunAbortPostResponses, ActorRunMetamorphPostData, ActorRunMetamorphPostResponses, ActorRunRebootPostData, ActorRunRebootPostResponses, PostResurrectRunData, PostResurrectRunResponses, PostChargeRunData, PostChargeRunResponses, ActorBuildsGetData, ActorBuildsGetResponses, ActorBuildDeleteData, ActorBuildDeleteResponses, ActorBuildGetData, ActorBuildGetResponses, ActorBuildAbortPostData, ActorBuildAbortPostResponses, ActorBuildLogGetData, ActorBuildLogGetResponses, ActorBuildOpenapiJsonGetData, ActorBuildOpenapiJsonGetResponses, KeyValueStoresGetData, KeyValueStoresGetResponses, KeyValueStoresPostData, KeyValueStoresPostResponses, KeyValueStoreDeleteData, KeyValueStoreDeleteResponses, KeyValueStoreGetData, KeyValueStoreGetResponses, KeyValueStorePutData, KeyValueStorePutResponses, KeyValueStoreKeysGetData, KeyValueStoreKeysGetResponses, KeyValueStoreRecordDeleteData, KeyValueStoreRecordDeleteResponses, KeyValueStoreRecordGetData, KeyValueStoreRecordGetResponses, KeyValueStoreRecordPutData, KeyValueStoreRecordPutResponses, DatasetsGetData, DatasetsGetResponses, DatasetsPostData, DatasetsPostResponses, DatasetDeleteData, DatasetDeleteResponses, DatasetGetData, DatasetGetResponses, DatasetPutData, DatasetPutResponses, DatasetItemsGetData, DatasetItemsGetResponses, DatasetItemsPostData, DatasetItemsPostResponses, DatasetItemsPostErrors, DatasetStatisticsGetData, DatasetStatisticsGetResponses, RequestQueuesGetData, RequestQueuesGetResponses, RequestQueuesPostData, RequestQueuesPostResponses, RequestQueueDeleteData, RequestQueueDeleteResponses, RequestQueueGetData, RequestQueueGetResponses, RequestQueuePutData, RequestQueuePutResponses, RequestQueueRequestsBatchDeleteData, RequestQueueRequestsBatchDeleteResponses, RequestQueueRequestsBatchPostData, RequestQueueRequestsBatchPostResponses, RequestQueueRequestsUnlockPostData, RequestQueueRequestsUnlockPostResponses, RequestQueueRequestsGetData, RequestQueueRequestsGetResponses, RequestQueueRequestsPostData, RequestQueueRequestsPostResponses, RequestQueueRequestDeleteData, RequestQueueRequestDeleteResponses, RequestQueueRequestGetData, RequestQueueRequestGetResponses, RequestQueueRequestPutData, RequestQueueRequestPutResponses, RequestQueueHeadGetData, RequestQueueHeadGetResponses, RequestQueueHeadLockPostData, RequestQueueHeadLockPostResponses, RequestQueueRequestLockDeleteData, RequestQueueRequestLockDeleteResponses, RequestQueueRequestLockPutData, RequestQueueRequestLockPutResponses, WebhooksGetData, WebhooksGetResponses, WebhooksPostData, WebhooksPostResponses, WebhookDeleteData, WebhookDeleteResponses, WebhookGetData, WebhookGetResponses, WebhookPutData, WebhookPutResponses, WebhookTestPostData, WebhookTestPostResponses, WebhookDispatchesGetData, WebhookDispatchesGetResponses, WebhookDispatchesGet2Data, WebhookDispatchesGet2Responses, WebhookDispatchGetData, WebhookDispatchGetResponses, SchedulesGetData, SchedulesGetResponses, SchedulesPostData, SchedulesPostResponses, ScheduleDeleteData, ScheduleDeleteResponses, ScheduleGetData, ScheduleGetResponses, SchedulePutData, SchedulePutResponses, ScheduleLogGetData, ScheduleLogGetResponses, StoreGetData, StoreGetResponses, LogGetData, LogGetResponses, UserGetData, UserGetResponses, UsersMeGetData, UsersMeGetResponses, UsersMeUsageMonthlyGetData, UsersMeUsageMonthlyGetResponses, UsersMeLimitsGetData, UsersMeLimitsGetResponses, UsersMeLimitsPutData, UsersMeLimitsPutResponses } from './types.gen';
import { client as _heyApiClient } from './client.gen';

export type Options<TData extends TDataShape = TDataShape, ThrowOnError extends boolean = boolean> = ClientOptions<TData, ThrowOnError> & {
    /**
     * You can provide a client instance returned by `createClient()` instead of
     * individual options. This might be also useful if you want to implement a
     * custom client.
     */
    client?: Client;
    /**
     * You can pass arbitrary values through the `meta` object. This can be
     * used to access values that aren't defined as part of the SDK function.
     */
    meta?: Record<string, unknown>;
};

/**
 * Get list of Actors
 * Gets the list of all Actors that the user created or used. The response is a
 * list of objects, where each object contains a basic information about a single Actor.
 *
 * To only get Actors created by the user, add the `my=1` query parameter.
 *
 * The endpoint supports pagination using the `limit` and `offset` parameters
 * and it will not return more than 1000 records.
 *
 * By default, the records are sorted by the `createdAt` field in ascending
 * order, therefore you can use pagination to incrementally fetch all Actors while new
 * ones are still being created. To sort the records in descending order, use the `desc=1` parameter.
 *
 * You can also sort by `lastRunStartedAt` by using the `sortBy=lastRunStartedAt` query parameter.
 * In this case, descending order means the most recently run Actor appears first.
 *
 */
export const actsGet = <ThrowOnError extends boolean = false>(options?: Options<ActsGetData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<ActsGetResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/acts',
        ...options
    });
};

/**
 * Create Actor
 * Creates a new Actor with settings specified in an Actor object passed as
 * JSON in the POST payload.
 * The response is the full Actor object as returned by the
 * [Get Actor](#/reference/actors/actor-object/get-actor) endpoint.
 *
 * The HTTP request must have the `Content-Type: application/json` HTTP header!
 *
 * The Actor needs to define at least one version of the source code.
 * For more information, see [Version object](#/reference/actors/version-object).
 *
 * If you want to make your Actor
 * [public](https://docs.apify.com/platform/actors/publishing) using `isPublic:
 * true`, you will need to provide the Actor's `title` and the `categories`
 * under which that Actor will be classified in Apify Store. For this, it's
 * best to use the [constants from our `apify-shared-js`
 * package](https://github.com/apify/apify-shared-js/blob/2d43ebc41ece9ad31cd6525bd523fb86939bf860/packages/consts/src/consts.ts#L452-L471).
 *
 */
export const actsPost = <ThrowOnError extends boolean = false>(options: Options<ActsPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<ActsPostResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/acts',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Delete Actor
 * Deletes an Actor.
 */
export const actDelete = <ThrowOnError extends boolean = false>(options: Options<ActDeleteData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<ActDeleteResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/acts/{actorId}',
        ...options
    });
};

/**
 * Get Actor
 * Gets an object that contains all the details about a specific Actor.
 */
export const actGet = <ThrowOnError extends boolean = false>(options: Options<ActGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<ActGetResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/acts/{actorId}',
        ...options
    });
};

/**
 * Update Actor
 * Updates settings of an Actor using values specified by an Actor object
 * passed as JSON in the POST payload.
 * If the object does not define a specific property, its value will not be
 * updated.
 *
 * The response is the full Actor object as returned by the
 * [Get Actor](#/reference/actors/actor-object/get-actor) endpoint.
 *
 * The request needs to specify the `Content-Type: application/json` HTTP header!
 *
 * When providing your API authentication token, we recommend using the
 * request's `Authorization` header, rather than the URL. ([More
 * info](#/introduction/authentication)).
 *
 * If you want to make your Actor
 * [public](https://docs.apify.com/platform/actors/publishing) using `isPublic:
 * true`, you will need to provide the Actor's `title` and the `categories`
 * under which that Actor will be classified in Apify Store. For this, it's
 * best to use the [constants from our `apify-shared-js`
 * package](https://github.com/apify/apify-shared-js/blob/2d43ebc41ece9ad31cd6525bd523fb86939bf860/packages/consts/src/consts.ts#L452-L471).
 *
 */
export const actPut = <ThrowOnError extends boolean = false>(options: Options<ActPutData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<ActPutResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/acts/{actorId}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Get list of versions
 * Gets the list of versions of a specific Actor. The response is a JSON object
 * with the list of [Version objects](#/reference/actors/version-object), where each
 * contains basic information about a single version.
 *
 */
export const actVersionsGet = <ThrowOnError extends boolean = false>(options: Options<ActVersionsGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<ActVersionsGetResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/acts/{actorId}/versions',
        ...options
    });
};

/**
 * Create version
 * Creates a version of an Actor using values specified in a [Version
 * object](#/reference/actors/version-object) passed as JSON in the POST
 * payload.
 *
 * The request must specify `versionNumber` and `sourceType` parameters (as
 * strings) in the JSON payload and a `Content-Type: application/json` HTTP
 * header.
 *
 * Each `sourceType` requires its own additional properties to be passed to the
 * JSON payload object. These are outlined in the [Version
 * object](#/reference/actors/version-object) table below and in more detail in
 * the [Apify
 * documentation](https://docs.apify.com/platform/actors/development/deployment/source-types).
 *
 * For example, if an Actor's source code is stored in a [GitHub
 * repository](https://docs.apify.com/platform/actors/development/deployment/source-types#git-repository),
 * you will set the `sourceType` to `GIT_REPO` and pass the repository's URL in
 * the `gitRepoUrl` property.
 *
 * ```
 * {
 * "versionNumber": "0.1",
 * "sourceType": "GIT_REPO",
 * "gitRepoUrl": "https://github.com/my-github-account/actor-repo"
 * }
 * ```
 *
 * The response is the [Version object](#/reference/actors/version-object) as
 * returned by the [Get version](#/reference/actors/version-object/get-version) endpoint.
 *
 */
export const actVersionsPost = <ThrowOnError extends boolean = false>(options: Options<ActVersionsPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<ActVersionsPostResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/acts/{actorId}/versions',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Delete version
 * Deletes a specific version of Actor's source code.
 *
 */
export const actVersionDelete = <ThrowOnError extends boolean = false>(options: Options<ActVersionDeleteData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<ActVersionDeleteResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/acts/{actorId}/versions/{versionNumber}',
        ...options
    });
};

/**
 * Get version
 * Gets a [Version object](#/reference/actors/version-object) that contains all the details about a specific version of an Actor.
 *
 */
export const actVersionGet = <ThrowOnError extends boolean = false>(options: Options<ActVersionGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<ActVersionGetResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/acts/{actorId}/versions/{versionNumber}',
        ...options
    });
};

/**
 * Update version
 * Updates Actor version using values specified by a [Version object](#/reference/actors/version-object) passed as JSON in the POST payload.
 *
 * If the object does not define a specific property, its value will not be
 * updated.
 *
 * The request needs to specify the `Content-Type: application/json` HTTP
 * header!
 *
 * When providing your API authentication token, we recommend using the
 * request's `Authorization` header, rather than the URL. ([More
 * info](#/introduction/authentication)).
 *
 * The response is the [Version object](#/reference/actors/version-object) as
 * returned by the [Get version](#/reference/actors/version-object/get-version) endpoint.
 *
 */
export const actVersionPut = <ThrowOnError extends boolean = false>(options: Options<ActVersionPutData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<ActVersionPutResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/acts/{actorId}/versions/{versionNumber}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Get list of environment variables
 * Gets the list of environment variables for a specific version of an Actor.
 * The response is a JSON object with the list of [EnvVar objects](#/reference/actors/environment-variable-object), where each contains basic information about a single environment variable.
 *
 */
export const actVersionEnvVarsGet = <ThrowOnError extends boolean = false>(options: Options<ActVersionEnvVarsGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<ActVersionEnvVarsGetResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/acts/{actorId}/versions/{versionNumber}/env-vars',
        ...options
    });
};

/**
 * Create environment variable
 * Creates an environment variable of an Actor using values specified in a
 * [EnvVar object](#/reference/actors/environment-variable-object) passed as
 * JSON in the POST payload.
 *
 * The request must specify `name` and `value` parameters (as strings) in the
 * JSON payload and a `Content-Type: application/json` HTTP header.
 *
 * ```
 * {
 * "name": "ENV_VAR_NAME",
 * "value": "my-env-var"
 * }
 * ```
 *
 * The response is the [EnvVar
 * object](#/reference/actors/environment-variable-object) as returned by the [Get environment
 * variable](#/reference/actors/environment-variable-object/get-environment-variable)
 * endpoint.
 *
 */
export const actVersionEnvVarsPost = <ThrowOnError extends boolean = false>(options: Options<ActVersionEnvVarsPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<ActVersionEnvVarsPostResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/acts/{actorId}/versions/{versionNumber}/env-vars',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Delete environment variable
 * Deletes a specific environment variable.
 */
export const actVersionEnvVarDelete = <ThrowOnError extends boolean = false>(options: Options<ActVersionEnvVarDeleteData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<ActVersionEnvVarDeleteResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/acts/{actorId}/versions/{versionNumber}/env-vars/{envVarName}',
        ...options
    });
};

/**
 * Get environment variable
 * Gets a [EnvVar object](#/reference/actors/environment-variable-object) that
 * contains all the details about a specific environment variable of an Actor.
 *
 * If `isSecret` is set to `true`, then `value` will never be returned.
 *
 */
export const actVersionEnvVarGet = <ThrowOnError extends boolean = false>(options: Options<ActVersionEnvVarGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<ActVersionEnvVarGetResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/acts/{actorId}/versions/{versionNumber}/env-vars/{envVarName}',
        ...options
    });
};

/**
 * Update environment variable
 * Updates Actor environment variable using values specified by a [EnvVar
 * object](#/reference/actors/environment-variable-object)
 * passed as JSON in the POST payload.
 * If the object does not define a specific property, its value will not be
 * updated.
 *
 * The request needs to specify the `Content-Type: application/json` HTTP
 * header!
 *
 * When providing your API authentication token, we recommend using the
 * request's `Authorization` header, rather than the URL. ([More
 * info](#/introduction/authentication)).
 *
 * The response is the [EnvVar object](#/reference/actors/environment-variable-object) as returned by the
 * [Get environment variable](#/reference/actors/environment-variable-object/get-environment-variable)
 * endpoint.
 *
 */
export const actVersionEnvVarPut = <ThrowOnError extends boolean = false>(options: Options<ActVersionEnvVarPutData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<ActVersionEnvVarPutResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/acts/{actorId}/versions/{versionNumber}/env-vars/{envVarName}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Get list of webhooks
 * Gets the list of webhooks of a specific Actor. The response is a JSON with
 * the list of objects, where each object contains basic information about a single webhook.
 *
 * The endpoint supports pagination using the `limit` and `offset` parameters
 * and it will not return more than 1000 records.
 *
 * By default, the records are sorted by the `createdAt` field in ascending
 * order, to sort the records in descending order, use the `desc=1` parameter.
 *
 */
export const actWebhooksGet = <ThrowOnError extends boolean = false>(options: Options<ActWebhooksGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<ActWebhooksGetResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/acts/{actorId}/webhooks',
        ...options
    });
};

/**
 * Get list of builds
 * Gets the list of builds of a specific Actor. The response is a JSON with the
 * list of objects, where each object contains basic information about a single build.
 *
 * The endpoint supports pagination using the `limit` and `offset` parameters
 * and it will not return more than 1000 records.
 *
 * By default, the records are sorted by the `startedAt` field in ascending order,
 * therefore you can use pagination to incrementally fetch all builds while new
 * ones are still being started. To sort the records in descending order, use
 * the `desc=1` parameter.
 *
 */
export const actBuildsGet = <ThrowOnError extends boolean = false>(options: Options<ActBuildsGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<ActBuildsGetResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/acts/{actorId}/builds',
        ...options
    });
};

/**
 * Build Actor
 * Builds an Actor.
 * The response is the build object as returned by the
 * [Get build](#/reference/actors/build-object/get-build) endpoint.
 *
 */
export const actBuildsPost = <ThrowOnError extends boolean = false>(options: Options<ActBuildsPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<ActBuildsPostResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/acts/{actorId}/builds',
        ...options
    });
};

/**
 * Get default build
 * Get the default build for an Actor.
 *
 * Use the optional `waitForFinish` parameter to synchronously wait for the build to finish.
 * This avoids the need for periodic polling when waiting for the build to complete.
 *
 * This endpoint does not require an authentication token. Instead, calls are authenticated using the Actor's unique ID.
 * However, if you access the endpoint without a token, certain attributes (e.g., `usageUsd` and `usageTotalUsd`) will be hidden.
 *
 */
export const actBuildDefaultGet = <ThrowOnError extends boolean = false>(options: Options<ActBuildDefaultGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<ActBuildDefaultGetResponses, unknown, ThrowOnError>({
        security: [
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            },
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/v2/acts/{actorId}/builds/default',
        ...options
    });
};

/**
 * Get OpenAPI definition
 *
 * Get the OpenAPI definition for Actor builds. Two similar endpoints are available:
 *
 * - [First endpoint](/api/v2/act-openapi-json-get): Requires both `actorId` and `buildId`. Use `default` as the `buildId` to get the OpenAPI schema for the default Actor build.
 * - [Second endpoint](/api/v2/actor-build-openapi-json-get): Requires only `buildId`.
 *
 * Get the OpenAPI definition for a specific Actor build.
 *
 * To fetch the default Actor build, simply pass `default` as the `buildId`.
 * Authentication is based on the build's unique ID. No authentication token is required.
 *
 * :::note
 *
 * You can also use the [`/api/v2/actor-build-openapi-json-get`](/api/v2/actor-build-openapi-json-get) endpoint to get the OpenAPI definition for a build.
 *
 * :::
 *
 */
export const actOpenapiJsonGet = <ThrowOnError extends boolean = false>(options: Options<ActOpenapiJsonGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<ActOpenapiJsonGetResponses, unknown, ThrowOnError>({
        security: [
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            },
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/v2/acts/{actorId}/builds/{buildId}/openapi.json',
        ...options
    });
};

/**
 * Get build
 * By passing the optional `waitForFinish` parameter the API endpoint will
 * synchronously wait for the build to finish.
 * This is useful to avoid periodic polling when waiting for an Actor build to
 * finish.
 *
 * This endpoint does not require the authentication token. Instead, calls are authenticated using a hard-to-guess ID of the build. However,
 * if you access the endpoint without the token, certain attributes, such as `usageUsd` and `usageTotalUsd`, will be hidden.
 *
 * @deprecated
 */
export const actBuildGet = <ThrowOnError extends boolean = false>(options: Options<ActBuildGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<ActBuildGetResponses, unknown, ThrowOnError>({
        security: [
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            },
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/v2/acts/{actorId}/builds/{buildId}',
        ...options
    });
};

/**
 * Abort build
 * **[DEPRECATED]** API endpoints related to build of the Actor were moved
 * under new namespace [`actor-builds`](#/reference/actor-builds). Aborts an
 * Actor build and returns an object that contains all the details about the
 * build.
 *
 * Only builds that are starting or running are aborted. For builds with status
 * `FINISHED`, `FAILED`, `ABORTING` and `TIMED-OUT` this call does nothing.
 *
 * @deprecated
 */
export const actBuildAbortPost = <ThrowOnError extends boolean = false>(options: Options<ActBuildAbortPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<ActBuildAbortPostResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/acts/{actorId}/builds/{buildId}/abort',
        ...options
    });
};

/**
 * Get list of runs
 * Gets the list of runs of a specific Actor. The response is a list of
 * objects, where each object contains basic information about a single Actor run.
 *
 * The endpoint supports pagination using the `limit` and `offset` parameters
 * and it will not return more than 1000 array elements.
 *
 * By default, the records are sorted by the `startedAt` field in ascending
 * order, therefore you can use pagination to incrementally fetch all records while
 * new ones are still being created. To sort the records in descending order, use
 * `desc=1` parameter. You can also filter runs by status ([available
 * statuses](https://docs.apify.com/platform/actors/running/runs-and-builds#lifecycle)).
 *
 */
export const actRunsGet = <ThrowOnError extends boolean = false>(options: Options<ActRunsGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<ActRunsGetResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/acts/{actorId}/runs',
        ...options
    });
};

/**
 * Run Actor
 * Runs an Actor and immediately returns without waiting for the run to finish.
 *
 * The POST payload including its `Content-Type` header is passed as `INPUT` to
 * the Actor (usually `application/json`).
 *
 * The Actor is started with the default options; you can override them using
 * various URL query parameters.
 *
 * The response is the Run object as returned by the [Get
 * run](#/reference/actor-runs/run-object-and-its-storages/get-run) API
 * endpoint.
 *
 * If you want to wait for the run to finish and receive the actual output of
 * the Actor as the response, please use one of the [Run Actor
 * synchronously](#/reference/actors/run-actor-synchronously) API endpoints
 * instead.
 *
 * To fetch the Actor run results that are typically stored in the default
 * dataset, you'll need to pass the ID received in the `defaultDatasetId` field
 * received in the response JSON to the [Get items](#/reference/datasets/item-collection/get-items)
 * API endpoint.
 *
 */
export const actRunsPost = <ThrowOnError extends boolean = false>(options: Options<ActRunsPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<ActRunsPostResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/acts/{actorId}/runs',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Without input
 * Runs a specific Actor and returns its output.
 * The run must finish in 300<!-- MAX_ACTOR_JOB_SYNC_WAIT_SECS --> seconds
 * otherwise the API endpoint returns a timeout error.
 * The Actor is not passed any input.
 *
 * Beware that it might be impossible to maintain an idle HTTP connection for a
 * long period of time,
 * due to client timeout or network conditions. Make sure your HTTP client is
 * configured to have a long enough connection timeout.
 * If the connection breaks, you will not receive any information about the run
 * and its status.
 *
 * To run the Actor asynchronously, use the [Run
 * Actor](#/reference/actors/run-collection/run-actor) API endpoint instead.
 *
 */
export const actRunSyncGet = <ThrowOnError extends boolean = false>(options: Options<ActRunSyncGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<ActRunSyncGetResponses, ActRunSyncGetErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/acts/{actorId}/run-sync',
        ...options
    });
};

/**
 * Run Actor synchronously with input and return output
 * Runs a specific Actor and returns its output.
 *
 * The POST payload including its `Content-Type` header is passed as `INPUT` to
 * the Actor (usually <code>application/json</code>).
 * The HTTP response contains Actors `OUTPUT` record from its default
 * key-value store.
 *
 * The Actor is started with the default options; you can override them using
 * various URL query parameters.
 * If the Actor run exceeds 300<!-- MAX_ACTOR_JOB_SYNC_WAIT_SECS --> seconds,
 * the HTTP response will have status 408 (Request Timeout).
 *
 * Beware that it might be impossible to maintain an idle HTTP connection for a
 * long period of time, due to client timeout or network conditions. Make sure your HTTP client is
 * configured to have a long enough connection timeout.
 * If the connection breaks, you will not receive any information about the run
 * and its status.
 *
 * To run the Actor asynchronously, use the [Run
 * Actor](#/reference/actors/run-collection/run-actor) API endpoint instead.
 *
 */
export const actRunSyncPost = <ThrowOnError extends boolean = false>(options: Options<ActRunSyncPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<ActRunSyncPostResponses, ActRunSyncPostErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/acts/{actorId}/run-sync',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Run Actor synchronously without input and get dataset items
 * Runs a specific Actor and returns its dataset items.
 * The run must finish in 300<!-- MAX_ACTOR_JOB_SYNC_WAIT_SECS --> seconds
 * otherwise the API endpoint returns a timeout error.
 * The Actor is not passed any input.
 *
 * It allows to send all possible options in parameters from [Get Dataset
 * Items](#/reference/datasets/item-collection/get-items) API endpoint.
 *
 * Beware that it might be impossible to maintain an idle HTTP connection for a
 * long period of time,
 * due to client timeout or network conditions. Make sure your HTTP client is
 * configured to have a long enough connection timeout.
 * If the connection breaks, you will not receive any information about the run
 * and its status.
 *
 * To run the Actor asynchronously, use the [Run
 * Actor](#/reference/actors/run-collection/run-actor) API endpoint instead.
 *
 */
export const actRunSyncGetDatasetItemsGet = <ThrowOnError extends boolean = false>(options: Options<ActRunSyncGetDatasetItemsGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<ActRunSyncGetDatasetItemsGetResponses, ActRunSyncGetDatasetItemsGetErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/acts/{actorId}/run-sync-get-dataset-items',
        ...options
    });
};

/**
 * Run Actor synchronously with input and get dataset items
 * Runs a specific Actor and returns its dataset items.
 *
 * The POST payload including its `Content-Type` header is passed as `INPUT` to
 * the Actor (usually `application/json`).
 * The HTTP response contains the Actors dataset items, while the format of
 * items depends on specifying dataset items' `format` parameter.
 *
 * You can send all the same options in parameters as the [Get Dataset
 * Items](#/reference/datasets/item-collection/get-items) API endpoint.
 *
 * The Actor is started with the default options; you can override them using
 * URL query parameters.
 * If the Actor run exceeds 300<!-- MAX_ACTOR_JOB_SYNC_WAIT_SECS --> seconds,
 * the HTTP response will return the 408 status code (Request Timeout).
 *
 * Beware that it might be impossible to maintain an idle HTTP connection for a
 * long period of time,
 * due to client timeout or network conditions. Make sure your HTTP client is
 * configured to have a long enough connection timeout.
 * If the connection breaks, you will not receive any information about the run
 * and its status.
 *
 * To run the Actor asynchronously, use the [Run
 * Actor](#/reference/actors/run-collection/run-actor) API endpoint instead.
 *
 */
export const actRunSyncGetDatasetItemsPost = <ThrowOnError extends boolean = false>(options: Options<ActRunSyncGetDatasetItemsPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<ActRunSyncGetDatasetItemsPostResponses, ActRunSyncGetDatasetItemsPostErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/acts/{actorId}/run-sync-get-dataset-items',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Resurrect run
 * **[DEPRECATED]** API endpoints related to run of the Actor were moved under
 * new namespace [`actor-runs`](#/reference/actor-runs).Resurrects a finished
 * Actor run and returns an object that contains all the details about the
 * resurrected run.
 *
 * Only finished runs, i.e. runs with status `FINISHED`, `FAILED`, `ABORTED`
 * and `TIMED-OUT` can be resurrected.
 * Run status will be updated to RUNNING and its container will be restarted
 * with the same storages
 * (the same behaviour as when the run gets migrated to the new server).
 *
 * For more information, see the [Actor
 * docs](https://docs.apify.com/platform/actors/running/runs-and-builds#resurrection-of-finished-run).
 *
 */
export const actRunResurrectPost = <ThrowOnError extends boolean = false>(options: Options<ActRunResurrectPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<ActRunResurrectPostResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/acts/{actorId}/runs/{runId}/resurrect',
        ...options
    });
};

/**
 * Get last run
 * This is not a single endpoint, but an entire group of endpoints that lets you to
 * retrieve and manage the last run of given Actor or any of its default storages.
 * All the endpoints require an authentication token.
 *
 * The endpoints accept the same HTTP methods and query parameters as
 * the respective storage endpoints.
 * The base path represents the last Actor run object is:
 *
 * `/v2/acts/{actorId}/runs/last{?token,status}`
 *
 * Using the `status` query parameter you can ensure to only get a run with a certain status
 * (e.g. `status=SUCCEEDED`). The output of this endpoint and other query parameters
 * are the same as in the [Run object](#/reference/actors/run-object) endpoint.
 *
 * In order to access the default storages of the last Actor run, i.e. log, key-value store, dataset and request queue,
 * use the following endpoints:
 *
 * * `/v2/acts/{actorId}/runs/last/log{?token,status}`
 * * `/v2/acts/{actorId}/runs/last/key-value-store{?token,status}`
 * * `/v2/acts/{actorId}/runs/last/dataset{?token,status}`
 * * `/v2/acts/{actorId}/runs/last/request-queue{?token,status}`
 *
 * These API endpoints have the same usage as the equivalent storage endpoints.
 * For example,
 * `/v2/acts/{actorId}/runs/last/key-value-store` has the same HTTP method and parameters as the
 * [Key-value store object](#/reference/key-value-stores/store-object) endpoint.
 *
 * Additionally, each of the above API endpoints supports all sub-endpoints
 * of the original one:
 *
 * #### Key-value store
 *
 * * `/v2/acts/{actorId}/runs/last/key-value-store/keys{?token,status}` [Key collection](#/reference/key-value-stores/key-collection)
 * * `/v2/acts/{actorId}/runs/last/key-value-store/records/{recordKey}{?token,status}` [Record](#/reference/key-value-stores/record)
 *
 * #### Dataset
 *
 * * `/v2/acts/{actorId}/runs/last/dataset/items{?token,status}` [Item collection](#/reference/datasets/item-collection)
 *
 * #### Request queue
 *
 * * `/v2/acts/{actorId}/runs/last/request-queue/requests{?token,status}` [Request collection](#/reference/request-queues/request-collection)
 * * `/v2/acts/{actorId}/runs/last/request-queue/requests/{requestId}{?token,status}` [Request collection](#/reference/request-queues/request)
 * * `/v2/acts/{actorId}/runs/last/request-queue/head{?token,status}` [Queue head](#/reference/request-queues/queue-head)
 *
 * For example, to download data from a dataset of the last succeeded Actor run in XML format,
 * send HTTP GET request to the following URL:
 *
 * ```
 * https://api.apify.com/v2/acts/{actorId}/runs/last/dataset/items?token={yourApiToken}&format=xml&status=SUCCEEDED
 * ```
 *
 * In order to save new items to the dataset, send HTTP POST request with JSON payload to the same URL.
 *
 */
export const actRunsLastGet = <ThrowOnError extends boolean = false>(options: Options<ActRunsLastGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<ActRunsLastGetResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/acts/{actorId}/runs/last',
        ...options
    });
};

/**
 * Get run
 * **[DEPRECATED]** API endpoints related to run of the Actor were moved under
 * new namespace [`actor-runs`](#/reference/actor-runs).
 *
 * Gets an object that contains all the details about a specific run of an Actor.
 *
 * By passing the optional `waitForFinish` parameter the API endpoint will
 * synchronously wait for the run to finish.
 * This is useful to avoid periodic polling when waiting for Actor run to
 * complete.
 *
 * This endpoint does not require the authentication token. Instead, calls are authenticated using a hard-to-guess ID of the run. However,
 * if you access the endpoint without the token, certain attributes, such as `usageUsd` and `usageTotalUsd`, will be hidden.
 *
 * @deprecated
 */
export const actRunGet = <ThrowOnError extends boolean = false>(options: Options<ActRunGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<ActRunGetResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/acts/{actorId}/runs/{runId}',
        ...options
    });
};

/**
 * Abort run
 * **[DEPRECATED]** API endpoints related to run of the Actor were moved under
 * new namespace [`actor-runs`](#/reference/actor-runs). Aborts an Actor run and
 * returns an object that contains all the details about the run.
 *
 * Only runs that are starting or running are aborted. For runs with status
 * `FINISHED`, `FAILED`, `ABORTING` and `TIMED-OUT` this call does nothing.
 *
 * @deprecated
 */
export const actRunAbortPost = <ThrowOnError extends boolean = false>(options: Options<ActRunAbortPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<ActRunAbortPostResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/acts/{actorId}/runs/{runId}/abort',
        ...options
    });
};

/**
 * Metamorph run
 * **[DEPRECATED]** API endpoints related to run of the Actor were moved under
 * new namespace [`actor-runs`](#/reference/actor-runs).Transforms an Actor run
 * into a run of another Actor with a new input.
 *
 * This is useful if you want to use another Actor to finish the work
 * of your current Actor run, without the need to create a completely new run
 * and waiting for its finish.
 * For the users of your Actors, the metamorph operation is transparent, they
 * will just see your Actor got the work done.
 *
 * There is a limit on how many times you can metamorph a single run. You can
 * check the limit in [the Actor runtime limits](https://docs.apify.com/platform/limits#actor-limits).
 *
 * Internally, the system stops the Docker container corresponding to the Actor
 * run and starts a new container using a different Docker image.
 * All the default storages are preserved and the new input is stored under the
 * `INPUT-METAMORPH-1` key in the same default key-value store.
 *
 * For more information, see the [Actor
 * docs](https://docs.apify.com/platform/actors/development/programming-interface/metamorph).
 *
 * @deprecated
 */
export const actRunMetamorphPost = <ThrowOnError extends boolean = false>(options: Options<ActRunMetamorphPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<ActRunMetamorphPostResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/acts/{actorId}/runs/{runId}/metamorph',
        ...options
    });
};

/**
 * Get list of tasks
 * Gets the complete list of tasks that a user has created or used.
 *
 * The response is a list of objects in which each object contains essential
 * information about a single task.
 *
 * The endpoint supports pagination using the `limit` and `offset` parameters,
 * and it does not return more than a 1000 records.
 *
 * By default, the records are sorted by the `createdAt` field in ascending
 * order; therefore you can use pagination to incrementally fetch all tasks while new
 * ones are still being created. To sort the records in descending order, use
 * the `desc=1` parameter.
 *
 */
export const actorTasksGet = <ThrowOnError extends boolean = false>(options?: Options<ActorTasksGetData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<ActorTasksGetResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/actor-tasks',
        ...options
    });
};

/**
 * Create task
 * Create a new task with settings specified by the object passed as JSON in
 * the POST payload.
 *
 * The response is the full task object as returned by the
 * [Get task](#/reference/tasks/task-object/get-task) endpoint.
 *
 * The request needs to specify the `Content-Type: application/json` HTTP header!
 *
 * When providing your API authentication token, we recommend using the
 * request's `Authorization` header, rather than the URL. ([More
 * info](#/introduction/authentication)).
 *
 */
export const actorTasksPost = <ThrowOnError extends boolean = false>(options: Options<ActorTasksPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<ActorTasksPostResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/actor-tasks',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Delete task
 * Delete the task specified through the `actorTaskId` parameter.
 */
export const actorTaskDelete = <ThrowOnError extends boolean = false>(options: Options<ActorTaskDeleteData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<ActorTaskDeleteResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/actor-tasks/{actorTaskId}',
        ...options
    });
};

/**
 * Get task
 * Get an object that contains all the details about a task.
 */
export const actorTaskGet = <ThrowOnError extends boolean = false>(options: Options<ActorTaskGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<ActorTaskGetResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/actor-tasks/{actorTaskId}',
        ...options
    });
};

/**
 * Update task
 * Update settings of a task using values specified by an object passed as JSON
 * in the POST payload.
 *
 * If the object does not define a specific property, its value is not updated.
 *
 * The response is the full task object as returned by the
 * [Get task](#/reference/tasks/task-object/get-task) endpoint.
 *
 * The request needs to specify the `Content-Type: application/json` HTTP
 * header!
 *
 * When providing your API authentication token, we recommend using the
 * request's `Authorization` header, rather than the URL. ([More
 * info](#/introduction/authentication)).
 *
 */
export const actorTaskPut = <ThrowOnError extends boolean = false>(options: Options<ActorTaskPutData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<ActorTaskPutResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/actor-tasks/{actorTaskId}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Get task input
 * Returns the input of a given task.
 */
export const actorTaskInputGet = <ThrowOnError extends boolean = false>(options: Options<ActorTaskInputGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<ActorTaskInputGetResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/actor-tasks/{actorTaskId}/input',
        ...options
    });
};

/**
 * Update task input
 * Updates the input of a task using values specified by an object passed as
 * JSON in the PUT payload.
 *
 * If the object does not define a specific property, its value is not updated.
 *
 * The response is the full task input as returned by the
 * [Get task input](#/reference/tasks/task-input-object/get-task-input) endpoint.
 *
 * The request needs to specify the `Content-Type: application/json` HTTP
 * header!
 *
 * When providing your API authentication token, we recommend using the
 * request's `Authorization` header, rather than the URL. ([More
 * info](#/introduction/authentication)).
 *
 */
export const actorTaskInputPut = <ThrowOnError extends boolean = false>(options: Options<ActorTaskInputPutData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<ActorTaskInputPutResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/actor-tasks/{actorTaskId}/input',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Get list of webhooks
 * Gets the list of webhooks of a specific Actor task. The response is a JSON
 * with the list of objects, where each object contains basic information about a single webhook.
 *
 * The endpoint supports pagination using the `limit` and `offset` parameters
 * and it will not return more than 1000 records.
 *
 * By default, the records are sorted by the `createdAt` field in ascending
 * order, to sort the records in descending order, use the `desc=1` parameter.
 *
 */
export const actorTaskWebhooksGet = <ThrowOnError extends boolean = false>(options: Options<ActorTaskWebhooksGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<ActorTaskWebhooksGetResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/actor-tasks/{actorTaskId}/webhooks',
        ...options
    });
};

/**
 * Get list of task runs
 * Get a list of runs of a specific task. The response is a list of objects,
 * where each object contains essential information about a single task run.
 *
 * The endpoint supports pagination using the `limit` and `offset` parameters,
 * and it does not return more than a 1000 array elements.
 *
 * By default, the records are sorted by the `startedAt` field in ascending
 * order; therefore you can use pagination to incrementally fetch all records while
 * new ones are still being created. To sort the records in descending order, use
 * the `desc=1` parameter. You can also filter runs by status ([available
 * statuses](https://docs.apify.com/platform/actors/running/runs-and-builds#lifecycle)).
 *
 */
export const actorTaskRunsGet = <ThrowOnError extends boolean = false>(options: Options<ActorTaskRunsGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<ActorTaskRunsGetResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/actor-tasks/{actorTaskId}/runs',
        ...options
    });
};

/**
 * Run task
 * Runs an Actor task and immediately returns without waiting for the run to
 * finish.
 *
 * Optionally, you can override the Actor input configuration by passing a JSON
 * object as the POST payload and setting the `Content-Type: application/json` HTTP header.
 *
 * Note that if the object in the POST payload does not define a particular
 * input property, the Actor run uses the default value defined by the task (or Actor's input
 * schema if not defined by the task).
 *
 * The response is the Actor Run object as returned by the [Get
 * run](#/reference/actor-runs/run-object-and-its-storages/get-run) endpoint.
 *
 * If you want to wait for the run to finish and receive the actual output of
 * the Actor run as the response, use one of the [Run task
 * synchronously](#/reference/actor-tasks/run-task-synchronously) API endpoints
 * instead.
 *
 * To fetch the Actor run results that are typically stored in the default
 * dataset, you'll need to pass the ID received in the `defaultDatasetId` field
 * received in the response JSON to the
 * [Get items](#/reference/datasets/item-collection/get-items) API endpoint.
 *
 */
export const actorTaskRunsPost = <ThrowOnError extends boolean = false>(options: Options<ActorTaskRunsPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<ActorTaskRunsPostResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/actor-tasks/{actorTaskId}/runs',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Run task synchronously
 * Run a specific task and return its output.
 *
 * The run must finish in 300<!-- MAX_ACTOR_JOB_SYNC_WAIT_SECS --> seconds
 * otherwise the HTTP request fails with a timeout error (this won't abort
 * the run itself).
 *
 * Beware that it might be impossible to maintain an idle HTTP connection for
 * an extended period, due to client timeout or network conditions. Make sure your HTTP client is
 * configured to have a long enough connection timeout.
 *
 * If the connection breaks, you will not receive any information about the run
 * and its status.
 *
 * To run the Task asynchronously, use the
 * [Run task asynchronously](#/reference/actor-tasks/run-collection/run-task)
 * endpoint instead.
 *
 */
export const actorTaskRunSyncGet = <ThrowOnError extends boolean = false>(options: Options<ActorTaskRunSyncGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<ActorTaskRunSyncGetResponses, ActorTaskRunSyncGetErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/actor-tasks/{actorTaskId}/run-sync',
        ...options
    });
};

/**
 * Run task synchronously
 * Runs an Actor task and synchronously returns its output.
 *
 * The run must finish in 300<!-- MAX_ACTOR_JOB_SYNC_WAIT_SECS --> seconds
 * otherwise the HTTP request fails with a timeout error (this won't abort
 * the run itself).
 *
 * Optionally, you can override the Actor input configuration by passing a JSON
 * object as the POST payload and setting the `Content-Type: application/json` HTTP header.
 *
 * Note that if the object in the POST payload does not define a particular
 * input property, the Actor run uses the default value defined by the task (or Actor's input
 * schema if not defined by the task).
 *
 * Beware that it might be impossible to maintain an idle HTTP connection for
 * an extended period, due to client timeout or network conditions. Make sure your HTTP client is
 * configured to have a long enough connection timeout.
 *
 * If the connection breaks, you will not receive any information about the run
 * and its status.
 *
 * Input fields from Actor task configuration can be overloaded with values
 * passed as the POST payload.
 *
 * Just make sure to specify `Content-Type` header to be `application/json` and
 * input to be an object.
 *
 * To run the task asynchronously, use the [Run
 * task](#/reference/actor-tasks/run-collection/run-task) API endpoint instead.
 *
 */
export const actorTaskRunSyncPost = <ThrowOnError extends boolean = false>(options: Options<ActorTaskRunSyncPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<ActorTaskRunSyncPostResponses, ActorTaskRunSyncPostErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/actor-tasks/{actorTaskId}/run-sync',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Run task synchronously and get dataset items
 * Run a specific task and return its dataset items.
 *
 * The run must finish in 300<!-- MAX_ACTOR_JOB_SYNC_WAIT_SECS --> seconds
 * otherwise the HTTP request fails with a timeout error (this won't abort
 * the run itself).
 *
 * You can send all the same options in parameters as the [Get Dataset
 * Items](#/reference/datasets/item-collection/get-items) API endpoint.
 *
 * Beware that it might be impossible to maintain an idle HTTP connection for
 * an extended period, due to client timeout or network conditions. Make sure your HTTP client is
 * configured to have a long enough connection timeout.
 *
 * If the connection breaks, you will not receive any information about the run
 * and its status.
 *
 * To run the Task asynchronously, use the [Run task
 * asynchronously](#/reference/actor-tasks/run-collection/run-task) endpoint
 * instead.
 *
 */
export const actorTaskRunSyncGetDatasetItemsGet = <ThrowOnError extends boolean = false>(options: Options<ActorTaskRunSyncGetDatasetItemsGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<ActorTaskRunSyncGetDatasetItemsGetResponses, ActorTaskRunSyncGetDatasetItemsGetErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/actor-tasks/{actorTaskId}/run-sync-get-dataset-items',
        ...options
    });
};

/**
 * Run task synchronously and get dataset items
 * Runs an Actor task and synchronously returns its dataset items.
 *
 * The run must finish in 300<!-- MAX_ACTOR_JOB_SYNC_WAIT_SECS --> seconds
 * otherwise the HTTP request fails with a timeout error (this won't abort
 * the run itself).
 *
 * Optionally, you can override the Actor input configuration by passing a JSON
 * object as the POST payload and setting the `Content-Type: application/json` HTTP header.
 *
 * Note that if the object in the POST payload does not define a particular
 * input property, the Actor run uses the default value defined by the task (or the Actor's
 * input schema if not defined by the task).
 *
 * You can send all the same options in parameters as the [Get Dataset
 * Items](#/reference/datasets/item-collection/get-items) API endpoint.
 *
 * Beware that it might be impossible to maintain an idle HTTP connection for
 * an extended period, due to client timeout or network conditions. Make sure your HTTP client is
 * configured to have a long enough connection timeout.
 *
 * If the connection breaks, you will not receive any information about the run
 * and its status.
 *
 * Input fields from Actor task configuration can be overloaded with values
 * passed as the POST payload.
 *
 * Just make sure to specify the `Content-Type` header as `application/json`
 * and that the input is an object.
 *
 * To run the task asynchronously, use the [Run
 * task](#/reference/actor-tasks/run-collection/run-task) API endpoint instead.
 *
 */
export const actorTaskRunSyncGetDatasetItemsPost = <ThrowOnError extends boolean = false>(options: Options<ActorTaskRunSyncGetDatasetItemsPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<ActorTaskRunSyncGetDatasetItemsPostResponses, ActorTaskRunSyncGetDatasetItemsPostErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/actor-tasks/{actorTaskId}/run-sync-get-dataset-items',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Get last run
 * This is not a single endpoint, but an entire group of endpoints that lets you to
 * retrieve and manage the last run of given actor task or any of its default storages.
 * All the endpoints require an authentication token.
 *
 * The endpoints accept the same HTTP methods and query parameters as
 * the respective storage endpoints.
 * The base path represents the last actor task run object is:
 *
 * `/v2/actor-tasks/{actorTaskId}/runs/last{?token,status}`
 *
 * Using the `status` query parameter you can ensure to only get a run with a certain status
 * (e.g. `status=SUCCEEDED`). The output of this endpoint and other query parameters
 * are the same as in the [Run object](/api/v2/actor-run-get) endpoint.
 *
 * In order to access the default storages of the last actor task run, i.e. log, key-value store, dataset and request queue,
 * use the following endpoints:
 *
 * * `/v2/actor-tasks/{actorTaskId}/runs/last/log{?token,status}`
 * * `/v2/actor-tasks/{actorTaskId}/runs/last/key-value-store{?token,status}`
 * * `/v2/actor-tasks/{actorTaskId}/runs/last/dataset{?token,status}`
 * * `/v2/actor-tasks/{actorTaskId}/runs/last/request-queue{?token,status}`
 *
 * These API endpoints have the same usage as the equivalent storage endpoints.
 * For example,
 * `/v2/actor-tasks/{actorTaskId}/runs/last/key-value-store` has the same HTTP method and parameters as the
 * [Key-value store object](/api/v2/storage-key-value-stores) endpoint.
 *
 * Additionally, each of the above API endpoints supports all sub-endpoints
 * of the original one:
 *
 * ##### Storage endpoints
 *
 * * [Dataset - introduction](/api/v2/storage-datasets)
 *
 * * [Key-value store - introduction](/api/v2/storage-key-value-stores)
 *
 * * [Request queue - introduction](/api/v2/storage-request-queues)
 *
 * For example, to download data from a dataset of the last succeeded actor task run in XML format,
 * send HTTP GET request to the following URL:
 *
 * ```
 * https://api.apify.com/v2/actor-tasks/{actorTaskId}/runs/last/dataset/items?token={yourApiToken}&format=xml&status=SUCCEEDED
 * ```
 *
 * In order to save new items to the dataset, send HTTP POST request with JSON payload to the same URL.
 *
 */
export const actorTaskRunsLastGet = <ThrowOnError extends boolean = false>(options: Options<ActorTaskRunsLastGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<ActorTaskRunsLastGetResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/actor-tasks/{actorTaskId}/runs/last',
        ...options
    });
};

/**
 * Get user runs list
 * Gets a list of all runs for a user. The response is a list of objects, where
 * each object contains basic information about a single Actor run.
 *
 * The endpoint supports pagination using the `limit` and `offset` parameters
 * and it will not return more than 1000 array elements.
 *
 * By default, the records are sorted by the `startedAt` field in ascending
 * order. Therefore, you can use pagination to incrementally fetch all records while
 * new ones are still being created. To sort the records in descending order, use
 * `desc=1` parameter. You can also filter runs by status ([available
 * statuses](https://docs.apify.com/platform/actors/running/runs-and-builds#lifecycle)).
 *
 */
export const actorRunsGet = <ThrowOnError extends boolean = false>(options?: Options<ActorRunsGetData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<ActorRunsGetResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/actor-runs',
        ...options
    });
};

/**
 * Delete run
 * Delete the run. Only finished runs can be deleted. Only the person or
 * organization that initiated the run can delete it.
 *
 */
export const actorRunDelete = <ThrowOnError extends boolean = false>(options: Options<ActorRunDeleteData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<ActorRunDeleteResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/actor-runs/{runId}',
        ...options
    });
};

/**
 * Get run
 * This is not a single endpoint, but an entire group of endpoints that lets
 * you retrieve the run or any of its default storages.
 *
 * The endpoints accept the same HTTP methods and query parameters as
 * the respective storage endpoints.
 *
 * The base path that represents the Actor run object is:
 *
 * `/v2/actor-runs/{runId}{?token}`
 *
 * In order to access the default storages of the Actor run, i.e. log,
 * key-value store, dataset and request queue, use the following endpoints:
 *
 * * `/v2/actor-runs/{runId}/log{?token}`
 * * `/v2/actor-runs/{runId}/key-value-store{?token}`
 * * `/v2/actor-runs/{runId}/dataset{?token}`
 * * `/v2/actor-runs/{runId}/request-queue{?token}`
 *
 * These API endpoints have the same usage as the equivalent storage endpoints.
 *
 * For example, `/v2/actor-runs/{runId}/key-value-store` has the same HTTP method and
 * parameters as the [Key-value store object](#/reference/key-value-stores/store-object) endpoint.
 *
 * Additionally, each of the above API endpoints supports all sub-endpoints
 * of the original one:
 *
 * #### Log
 *
 * * `/v2/actor-runs/{runId}/log` [Log](#/reference/logs)
 *
 * #### Key-value store
 *
 * * `/v2/actor-runs/{runId}/key-value-store/keys{?token}` [Key
 * collection](#/reference/key-value-stores/key-collection)
 * * `/v2/actor-runs/{runId}/key-value-store/records/{recordKey}{?token}`
 * [Record](#/reference/key-value-stores/record)
 *
 * #### Dataset
 *
 * * `/v2/actor-runs/{runId}/dataset/items{?token}` [Item
 * collection](#/reference/datasets/item-collection)
 *
 * #### Request queue
 *
 * * `/v2/actor-runs/{runId}/request-queue/requests{?token}` [Request
 * collection](#/reference/request-queues/request-collection)
 * * `/v2/actor-runs/{runId}/request-queue/requests/{requestId}{?token}`
 * [Request collection](#/reference/request-queues/request)
 * * `/v2/actor-runs/{runId}/request-queue/head{?token}` [Queue
 * head](#/reference/request-queues/queue-head)
 *
 * For example, to download data from a dataset of the Actor run in XML format,
 * send HTTP GET request to the following URL:
 *
 * ```
 * https://api.apify.com/v2/actor-runs/{runId}/dataset/items?format=xml
 * ```
 *
 * In order to save new items to the dataset, send HTTP POST request with JSON
 * payload to the same URL.
 *
 * Gets an object that contains all the details about a
 * specific run of an Actor.
 *
 * By passing the optional `waitForFinish` parameter the API endpoint will synchronously wait
 * for the run to finish. This is useful to avoid periodic polling when waiting for Actor run to complete.
 *
 * This endpoint does not require the authentication token. Instead, calls are authenticated using a hard-to-guess ID of the run. However,
 * if you access the endpoint without the token, certain attributes, such as `usageUsd` and `usageTotalUsd`, will be hidden.
 *
 */
export const actorRunGet = <ThrowOnError extends boolean = false>(options: Options<ActorRunGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<ActorRunGetResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/actor-runs/{runId}',
        ...options
    });
};

/**
 * Update status message
 * You can set a single status message on your run that will be displayed in
 * the Apify Console UI. During an Actor run, you will typically do this in order
 * to inform users of your Actor about the Actor's progress.
 *
 * The request body must contain `runId` and `statusMessage` properties. The
 * `isStatusMessageTerminal` property is optional and it indicates if the
 * status message is the very last one. In the absence of a status message, the
 * platform will try to substitute sensible defaults.
 *
 */
export const actorRunPut = <ThrowOnError extends boolean = false>(options: Options<ActorRunPutData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<ActorRunPutResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/actor-runs/{runId}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Abort run
 * Aborts an Actor run and returns an object that contains all the details
 * about the run.
 *
 * Only runs that are starting or running are aborted. For runs with status
 * `FINISHED`, `FAILED`, `ABORTING` and `TIMED-OUT` this call does nothing.
 *
 */
export const actorRunAbortPost = <ThrowOnError extends boolean = false>(options: Options<ActorRunAbortPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<ActorRunAbortPostResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/actor-runs/{runId}/abort',
        ...options
    });
};

/**
 * Metamorph run
 * Transforms an Actor run into a run of another Actor with a new input.
 *
 * This is useful if you want to use another Actor to finish the work
 * of your current Actor run, without the need to create a completely new run
 * and waiting for its finish.
 *
 * For the users of your Actors, the metamorph operation is transparent, they
 * will just see your Actor got the work done.
 *
 * Internally, the system stops the Docker container corresponding to the Actor
 * run and starts a new container using a different Docker image.
 *
 * All the default storages are preserved and the new input is stored under the
 * `INPUT-METAMORPH-1` key in the same default key-value store.
 *
 * For more information, see the [Actor docs](https://docs.apify.com/platform/actors/development/programming-interface/metamorph).
 *
 */
export const actorRunMetamorphPost = <ThrowOnError extends boolean = false>(options: Options<ActorRunMetamorphPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<ActorRunMetamorphPostResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/actor-runs/{runId}/metamorph',
        ...options
    });
};

/**
 * Reboot run
 * Reboots an Actor run and returns an object that contains all the details
 * about the rebooted run.
 *
 * Only runs that are running, i.e. runs with status `RUNNING` can be rebooted.
 *
 * The run's container will be restarted, so any data not persisted in the
 * key-value store, dataset, or request queue will be lost.
 *
 */
export const actorRunRebootPost = <ThrowOnError extends boolean = false>(options: Options<ActorRunRebootPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<ActorRunRebootPostResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/actor-runs/{runId}/reboot',
        ...options
    });
};

/**
 * Resurrect run
 * Resurrects a finished Actor run and returns an object that contains all the details about the resurrected run.
 * Only finished runs, i.e. runs with status `FINISHED`, `FAILED`, `ABORTED` and `TIMED-OUT` can be resurrected.
 * Run status will be updated to RUNNING and its container will be restarted with the same storages
 * (the same behaviour as when the run gets migrated to the new server).
 *
 * For more information, see the [Actor docs](https://docs.apify.com/platform/actors/running/runs-and-builds#resurrection-of-finished-run).
 *
 */
export const postResurrectRun = <ThrowOnError extends boolean = false>(options: Options<PostResurrectRunData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<PostResurrectRunResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/actor-runs/{runId}/resurrect',
        ...options
    });
};

/**
 * Charge events in run
 * Charge for events in the run of your [pay per event Actor](https://docs.apify.com/platform/actors/running/actors-in-store#pay-per-event).
 * The event you are charging for must be one of the configured events in your Actor. If the Actor is not set up as pay per event, or if the event is not configured,
 * the endpoint will return an error. The endpoint must be called from the Actor run itself, with the same API token that the run was started with.
 *
 * :::note
 *
 * Pay per events Actors are still in alpha. Please, reach out to us with any questions or feedback.
 *
 * :::
 *
 */
export const postChargeRun = <ThrowOnError extends boolean = false>(options: Options<PostChargeRunData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<PostChargeRunResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/actor-runs/{runId}/charge',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Get user builds list
 * Gets a list of all builds for a user. The response is a JSON array of
 * objects, where each object contains basic information about a single build.
 *
 * The endpoint supports pagination using the `limit` and `offset` parameters
 * and it will not return more than 1000 records.
 *
 * By default, the records are sorted by the `startedAt` field in ascending
 * order. Therefore, you can use pagination to incrementally fetch all builds while
 * new ones are still being started. To sort the records in descending order, use
 * the `desc=1` parameter.
 *
 */
export const actorBuildsGet = <ThrowOnError extends boolean = false>(options?: Options<ActorBuildsGetData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<ActorBuildsGetResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/actor-builds',
        ...options
    });
};

/**
 * Delete build
 * Delete the build. The build that is the current default build for the Actor
 * cannot be deleted.
 *
 * Only users with build permissions for the Actor can delete builds.
 *
 */
export const actorBuildDelete = <ThrowOnError extends boolean = false>(options: Options<ActorBuildDeleteData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<ActorBuildDeleteResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/actor-builds/{buildId}',
        ...options
    });
};

/**
 * Get build
 * Gets an object that contains all the details about a specific build of an
 * Actor.
 *
 * By passing the optional `waitForFinish` parameter the API endpoint will
 * synchronously wait for the build to finish. This is useful to avoid periodic
 * polling when waiting for an Actor build to finish.
 *
 * This endpoint does not require the authentication token. Instead, calls are authenticated using a hard-to-guess ID of the build. However,
 * if you access the endpoint without the token, certain attributes, such as `usageUsd` and `usageTotalUsd`, will be hidden.
 *
 */
export const actorBuildGet = <ThrowOnError extends boolean = false>(options: Options<ActorBuildGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<ActorBuildGetResponses, unknown, ThrowOnError>({
        security: [
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            },
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/v2/actor-builds/{buildId}',
        ...options
    });
};

/**
 * Abort build
 * Aborts an Actor build and returns an object that contains all the details
 * about the build.
 *
 * Only builds that are starting or running are aborted. For builds with status
 * `FINISHED`, `FAILED`, `ABORTING` and `TIMED-OUT` this call does nothing.
 *
 */
export const actorBuildAbortPost = <ThrowOnError extends boolean = false>(options: Options<ActorBuildAbortPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<ActorBuildAbortPostResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/actor-builds/{buildId}/abort',
        ...options
    });
};

/**
 * Get log
 * Check out [Logs](#/reference/logs) for full reference.
 */
export const actorBuildLogGet = <ThrowOnError extends boolean = false>(options: Options<ActorBuildLogGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<ActorBuildLogGetResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/actor-builds/{buildId}/log',
        ...options
    });
};

/**
 * Get OpenAPI definition
 * Get the OpenAPI definition for Actor builds. Two similar endpoints are available:
 *
 * - [First endpoint](/api/v2/act-openapi-json-get): Requires both `actorId` and `buildId`. Use `default` as the `buildId` to get the OpenAPI schema for the default Actor build.
 * - [Second endpoint](/api/v2/actor-build-openapi-json-get): Requires only `buildId`.
 *
 * Get the OpenAPI definition for a specific Actor build.
 * Authentication is based on the build's unique ID. No authentication token is required.
 *
 * :::note
 *
 * You can also use the [`/api/v2/act-openapi-json-get`](/api/v2/act-openapi-json-get) endpoint to get the OpenAPI definition for a build.
 *
 * :::
 *
 */
export const actorBuildOpenapiJsonGet = <ThrowOnError extends boolean = false>(options: Options<ActorBuildOpenapiJsonGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<ActorBuildOpenapiJsonGetResponses, unknown, ThrowOnError>({
        security: [
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            },
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/v2/actor-builds/{buildId}/openapi.json',
        ...options
    });
};

/**
 * Get list of key-value stores
 * Gets the list of key-value stores owned by the user.
 *
 * The response is a list of objects, where each objects contains a basic
 * information about a single key-value store.
 *
 * The endpoint supports pagination using the `limit` and `offset` parameters
 * and it will not return more than 1000 array elements.
 *
 * By default, the records are sorted by the `createdAt` field in ascending
 * order, therefore you can use pagination to incrementally fetch all key-value stores
 * while new ones are still being created. To sort the records in descending order, use
 * the `desc=1` parameter.
 *
 */
export const keyValueStoresGet = <ThrowOnError extends boolean = false>(options?: Options<KeyValueStoresGetData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<KeyValueStoresGetResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/key-value-stores',
        ...options
    });
};

/**
 * Create key-value store
 * Creates a key-value store and returns its object. The response is the same
 * object as returned by the [Get store](#/reference/key-value-stores/store-object/get-store)
 * endpoint.
 *
 * Keep in mind that data stored under unnamed store follows [data retention
 * period](https://docs.apify.com/platform/storage#data-retention).
 *
 * It creates a store with the given name if the parameter name is used.
 * If there is another store with the same name, the endpoint does not create a
 * new one and returns the existing object instead.
 *
 */
export const keyValueStoresPost = <ThrowOnError extends boolean = false>(options?: Options<KeyValueStoresPostData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<KeyValueStoresPostResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/key-value-stores',
        ...options
    });
};

/**
 * Delete store
 * Deletes a key-value store.
 */
export const keyValueStoreDelete = <ThrowOnError extends boolean = false>(options: Options<KeyValueStoreDeleteData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<KeyValueStoreDeleteResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/key-value-stores/{storeId}',
        ...options
    });
};

/**
 * Get store
 * Gets an object that contains all the details about a specific key-value
 * store.
 *
 */
export const keyValueStoreGet = <ThrowOnError extends boolean = false>(options: Options<KeyValueStoreGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<KeyValueStoreGetResponses, unknown, ThrowOnError>({
        security: [
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            },
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/v2/key-value-stores/{storeId}',
        ...options
    });
};

/**
 * Update store
 * Updates a key-value store's name using a value specified by a JSON object
 * passed in the PUT payload.
 *
 * The response is the updated key-value store object, as returned by the [Get
 * store](#/reference/key-value-stores/store-object/get-store) API endpoint.
 *
 */
export const keyValueStorePut = <ThrowOnError extends boolean = false>(options: Options<KeyValueStorePutData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<KeyValueStorePutResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/key-value-stores/{storeId}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Get list of keys
 * Returns a list of objects describing keys of a given key-value store, as
 * well as some information about the values (e.g. size).
 *
 * This endpoint is paginated using `exclusiveStartKey` and `limit` parameters
 * - see [Pagination](/api/v2#using-key) for more details.
 *
 */
export const keyValueStoreKeysGet = <ThrowOnError extends boolean = false>(options: Options<KeyValueStoreKeysGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<KeyValueStoreKeysGetResponses, unknown, ThrowOnError>({
        security: [
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            },
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/v2/key-value-stores/{storeId}/keys',
        ...options
    });
};

/**
 * Delete record
 * Removes a record specified by a key from the key-value store.
 */
export const keyValueStoreRecordDelete = <ThrowOnError extends boolean = false>(options: Options<KeyValueStoreRecordDeleteData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<KeyValueStoreRecordDeleteResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/key-value-stores/{storeId}/records/{recordKey}',
        ...options
    });
};

/**
 * Get record
 * Gets a value stored in the key-value store under a specific key.
 *
 * The response body has the same `Content-Encoding` header as it was set in
 * [Put record](#tag/Key-value-storesRecord/operation/keyValueStore_record_put).
 *
 * If the request does not define the `Accept-Encoding` HTTP header with the
 * right encoding, the record will be decompressed.
 *
 * Most HTTP clients support decompression by default. After using the HTTP
 * client with decompression support, the `Accept-Encoding` header is set by
 * the client and body is decompressed automatically.
 *
 */
export const keyValueStoreRecordGet = <ThrowOnError extends boolean = false>(options: Options<KeyValueStoreRecordGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<KeyValueStoreRecordGetResponses, unknown, ThrowOnError>({
        security: [
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            },
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/v2/key-value-stores/{storeId}/records/{recordKey}',
        ...options
    });
};

/**
 * Store record
 * Stores a value under a specific key to the key-value store.
 *
 * The value is passed as the PUT payload and it is stored with a MIME content
 * type defined by the `Content-Type` header and with encoding defined by the
 * `Content-Encoding` header.
 *
 * To save bandwidth, storage, and speed up your upload, send the request
 * payload compressed with Gzip compression and add the `Content-Encoding: gzip`
 * header. It is possible to set up another compression type with `Content-Encoding`
 * request header.
 *
 * Below is a list of supported `Content-Encoding` types.
 *
 * * Gzip compression: `Content-Encoding: gzip`
 * * Deflate compression: `Content-Encoding: deflate`
 * * Brotli compression: `Content-Encoding: br`
 *
 */
export const keyValueStoreRecordPut = <ThrowOnError extends boolean = false>(options: Options<KeyValueStoreRecordPutData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<KeyValueStoreRecordPutResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/key-value-stores/{storeId}/records/{recordKey}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Get list of datasets
 * Lists all of a user's datasets.
 *
 * The response is a JSON array of objects,
 * where each object contains basic information about one dataset.
 *
 * By default, the objects are sorted by the `createdAt` field in ascending
 * order, therefore you can use pagination to incrementally fetch all datasets while new
 * ones are still being created. To sort them in descending order, use `desc=1`
 * parameter. The endpoint supports pagination using `limit` and `offset`
 * parameters and it will not return more than 1000 array elements.
 *
 */
export const datasetsGet = <ThrowOnError extends boolean = false>(options?: Options<DatasetsGetData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<DatasetsGetResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/datasets',
        ...options
    });
};

/**
 * Create dataset
 * Creates a dataset and returns its object.
 * Keep in mind that data stored under unnamed dataset follows [data retention period](https://docs.apify.com/platform/storage#data-retention).
 * It creates a dataset with the given name if the parameter name is used.
 * If a dataset with the given name already exists then returns its object.
 *
 */
export const datasetsPost = <ThrowOnError extends boolean = false>(options?: Options<DatasetsPostData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<DatasetsPostResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/datasets',
        ...options
    });
};

/**
 * Delete dataset
 * Deletes a specific dataset.
 */
export const datasetDelete = <ThrowOnError extends boolean = false>(options: Options<DatasetDeleteData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DatasetDeleteResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/datasets/{datasetId}',
        ...options
    });
};

/**
 * Get dataset
 * Returns dataset object for given dataset ID.
 *
 * :::note
 *
 * Keep in mind that attributes `itemCount` and `cleanItemCount` are not propagated right away after data are pushed into a dataset.
 *
 * :::
 *
 * There is a short period (up to 5 seconds) during which these counters may not match with exact counts in dataset items.
 *
 */
export const datasetGet = <ThrowOnError extends boolean = false>(options: Options<DatasetGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<DatasetGetResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/datasets/{datasetId}',
        ...options
    });
};

/**
 * Update dataset
 * Updates a dataset's name using a value specified by a JSON object passed in the PUT payload.
 * The response is the updated dataset object, as returned by the [Get dataset](#/reference/datasets/dataset-collection/get-dataset) API endpoint.
 *
 */
export const datasetPut = <ThrowOnError extends boolean = false>(options: Options<DatasetPutData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<DatasetPutResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/datasets/{datasetId}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Get items
 * Returns data stored in the dataset in a desired format.
 *
 * ### Response format
 *
 * The format of the response depends on <code>format</code> query parameter.
 *
 * The <code>format</code> parameter can have one of the following values:
 * <code>json</code>, <code>jsonl</code>, <code>xml</code>, <code>html</code>,
 * <code>csv</code>, <code>xlsx</code> and <code>rss</code>.
 *
 * The following table describes how each format is treated.
 *
 * <table>
 * <tr>
 * <th>Format</th>
 * <th>Items</th>
 * </tr>
 * <tr>
 * <td><code>json</code></td>
 * <td rowspan="3">The response is a JSON, JSONL or XML array of raw item objects.</td>
 * </tr>
 * <tr>
 * <td><code>jsonl</code></td>
 * </tr>
 * <tr>
 * <td><code>xml</code></td>
 * </tr>
 * <tr>
 * <td><code>html</code></td>
 * <td rowspan="3">The response is a HTML, CSV or XLSX table, where columns correspond to the
 * properties of the item and rows correspond to each dataset item.</td>
 * </tr>
 * <tr>
 * <td><code>csv</code></td>
 * </tr>
 * <tr>
 * <td><code>xlsx</code></td>
 * </tr>
 * <tr>
 * <td><code>rss</code></td>
 * <td colspan="2">The response is a RSS file. Each item is displayed as child elements of one
 * <code>&lt;item&gt;</code>.</td>
 * </tr>
 * </table>
 *
 * Note that CSV, XLSX and HTML tables are limited to 2000 columns and the column names cannot be longer than 200 characters.
 * JSON, XML and RSS formats do not have such restrictions.
 *
 * ### Hidden fields
 *
 * The top-level fields starting with the `#` character are considered hidden.
 * These are useful to store debugging information and can be omitted from the output by providing the `skipHidden=1` or `clean=1` query parameters.
 * For example, if you store the following object to the dataset:
 *
 * ```
 * {
 * productName: "iPhone Xs",
 * description: "Welcome to the big screens."
 * #debug: {
 * url: "https://www.apple.com/lae/iphone-xs/",
 * crawledAt: "2019-01-21T16:06:03.683Z"
 * }
 * }
 * ```
 *
 * The `#debug` field will be considered as hidden and can be omitted from the
 * results. This is useful to
 * provide nice cleaned data to end users, while keeping debugging info
 * available if needed. The Dataset object
 * returned by the API contains the number of such clean items in the`dataset.cleanItemCount` property.
 *
 * ### XML format extension
 *
 * When exporting results to XML or RSS formats, the names of object properties become XML tags and the corresponding values become tag's children. For example, the following JavaScript object:
 *
 * ```
 * {
 * name: "Paul Newman",
 * address: [
 * { type: "home", street: "21st", city: "Chicago" },
 * { type: "office", street: null, city: null }
 * ]
 * }
 * ```
 *
 * will be transformed to the following XML snippet:
 *
 * ```
 * <name>Paul Newman</name>
 * <address>
 * <type>home</type>
 * <street>21st</street>
 * <city>Chicago</city>
 * </address>
 * <address>
 * <type>office</type>
 * <street/>
 * <city/>
 * </address>
 * ```
 *
 * If the JavaScript object contains a property named `@` then its sub-properties are exported as attributes of the parent XML
 * element.
 * If the parent XML element does not have any child elements then its value is taken from a JavaScript object property named `#`.
 *
 * For example, the following JavaScript object:
 *
 * ```
 * {
 * "address": [{
 * "@": {
 * "type": "home"
 * },
 * "street": "21st",
 * "city": "Chicago"
 * },
 * {
 * "@": {
 * "type": "office"
 * },
 * "#": 'unknown'
 * }]
 * }
 * ```
 *
 * will be transformed to the following XML snippet:
 *
 * ```
 * <address type="home">
 * <street>21st</street>
 * <city>Chicago</city>
 * </address>
 * <address type="office">unknown</address>
 * ```
 *
 * This feature is also useful to customize your RSS feeds generated for various websites.
 *
 * By default the whole result is wrapped in a `<items>` element and each page object is wrapped in a `<item>` element.
 * You can change this using <code>xmlRoot</code> and <code>xmlRow</code> url parameters.
 *
 * ### Pagination
 *
 * The generated response supports [pagination](#/introduction/pagination).
 * The pagination is always performed with the granularity of a single item, regardless whether <code>unwind</code> parameter was provided.
 * By default, the **Items** in the response are sorted by the time they were stored to the database, therefore you can use pagination to incrementally fetch the items as they are being added.
 * No limit exists to how many items can be returned in one response.
 *
 * If you specify `desc=1` query parameter, the results are returned in the reverse order than they were stored (i.e. from newest to oldest items).
 * Note that only the order of **Items** is reversed, but not the order of the `unwind` array elements.
 *
 */
export const datasetItemsGet = <ThrowOnError extends boolean = false>(options: Options<DatasetItemsGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<DatasetItemsGetResponses, unknown, ThrowOnError>({
        security: [
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            },
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/v2/datasets/{datasetId}/items',
        ...options
    });
};

/**
 * Store items
 * Appends an item or an array of items to the end of the dataset.
 * The POST payload is a JSON object or a JSON array of objects to save into the dataset.
 *
 * If the data you attempt to store in the dataset is invalid (meaning any of the items received by the API fails the validation), the whole request is discarded and the API will return a response with status code 400.
 * For more information about dataset schema validation, see [Dataset schema](https://docs.apify.com/platform/actors/development/actor-definition/dataset-schema/validation).
 *
 * **IMPORTANT:** The limit of request payload size for the dataset is 5 MB. If the array exceeds the size, you'll need to split it into a number of smaller arrays.
 *
 */
export const datasetItemsPost = <ThrowOnError extends boolean = false>(options: Options<DatasetItemsPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<DatasetItemsPostResponses, DatasetItemsPostErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/datasets/{datasetId}/items',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Get dataset statistics
 * Returns statistics for given dataset.
 *
 * Provides only [field statistics](https://docs.apify.com/platform/actors/development/actor-definition/dataset-schema/validation#dataset-field-statistics).
 *
 */
export const datasetStatisticsGet = <ThrowOnError extends boolean = false>(options: Options<DatasetStatisticsGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<DatasetStatisticsGetResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/datasets/{datasetId}/statistics',
        ...options
    });
};

/**
 * Get list of request queues
 * Lists all of a user's request queues. The response is a JSON array of
 * objects, where each object
 * contains basic information about one queue.
 *
 * By default, the objects are sorted by the `createdAt` field in ascending order,
 * therefore you can use pagination to incrementally fetch all queues while new
 * ones are still being created. To sort them in descending order, use `desc=1`
 * parameter. The endpoint supports pagination using `limit` and `offset`
 * parameters and it will not return more than 1000
 * array elements.
 *
 */
export const requestQueuesGet = <ThrowOnError extends boolean = false>(options?: Options<RequestQueuesGetData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<RequestQueuesGetResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/request-queues',
        ...options
    });
};

/**
 * Create request queue
 * Creates a request queue and returns its object.
 * Keep in mind that requests stored under unnamed queue follows [data
 * retention period](https://docs.apify.com/platform/storage#data-retention).
 *
 * It creates a queue of given name if the parameter name is used. If a queue
 * with the given name already exists then the endpoint returns
 * its object.
 *
 */
export const requestQueuesPost = <ThrowOnError extends boolean = false>(options?: Options<RequestQueuesPostData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<RequestQueuesPostResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/request-queues',
        ...options
    });
};

/**
 * Delete request queue
 * Deletes given queue.
 */
export const requestQueueDelete = <ThrowOnError extends boolean = false>(options: Options<RequestQueueDeleteData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<RequestQueueDeleteResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/request-queues/{queueId}',
        ...options
    });
};

/**
 * Get request queue
 * Returns queue object for given queue ID.
 */
export const requestQueueGet = <ThrowOnError extends boolean = false>(options: Options<RequestQueueGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<RequestQueueGetResponses, unknown, ThrowOnError>({
        security: [
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            },
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/v2/request-queues/{queueId}',
        ...options
    });
};

/**
 * Update request queue
 * Updates a request queue's name using a value specified by a JSON object
 * passed in the PUT payload.
 *
 * The response is the updated request queue object, as returned by the
 * [Get request queue](#/reference/request-queues/queue-collection/get-request-queue) API endpoint.
 *
 */
export const requestQueuePut = <ThrowOnError extends boolean = false>(options: Options<RequestQueuePutData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<RequestQueuePutResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/request-queues/{queueId}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Delete requests
 * Batch-deletes given requests from the queue. The number of requests in a
 * batch is limited to 25. The response contains an array of unprocessed and
 * processed requests.
 * If any delete operation fails because the request queue rate limit is
 * exceeded or an internal failure occurs,
 * the failed request is returned in the `unprocessedRequests` response
 * parameter.
 * You can re-send these delete requests. It is recommended to use an
 * exponential backoff algorithm for these retries.
 * Each request is identified by its ID or uniqueKey parameter. You can use
 * either of them to identify the request.
 *
 */
export const requestQueueRequestsBatchDelete = <ThrowOnError extends boolean = false>(options: Options<RequestQueueRequestsBatchDeleteData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<RequestQueueRequestsBatchDeleteResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/request-queues/{queueId}/requests/batch',
        ...options
    });
};

/**
 * Add requests
 * Adds requests to the queue in batch. The maximum requests in batch is limit
 * to 25. The response contains an array of unprocessed and processed requests.
 * If any add operation fails because the request queue rate limit is exceeded
 * or an internal failure occurs,
 * the failed request is returned in the unprocessedRequests response
 * parameter.
 * You can resend these requests to add. It is recommended to use exponential
 * backoff algorithm for these retries.
 * If a request with the same `uniqueKey` was already present in the queue,
 * then it returns an ID of the existing request.
 *
 */
export const requestQueueRequestsBatchPost = <ThrowOnError extends boolean = false>(options: Options<RequestQueueRequestsBatchPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<RequestQueueRequestsBatchPostResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/request-queues/{queueId}/requests/batch',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Unlock requests
 * Unlocks requests in the queue that are currently locked by the client.
 *
 * * If the client is within an Actor run, it unlocks all requests locked by that specific run plus all requests locked by the same clientKey.
 * * If the client is outside of an Actor run, it unlocks all requests locked using the same clientKey.
 *
 */
export const requestQueueRequestsUnlockPost = <ThrowOnError extends boolean = false>(options: Options<RequestQueueRequestsUnlockPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<RequestQueueRequestsUnlockPostResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/request-queues/{queueId}/requests/unlock',
        ...options
    });
};

/**
 * List requests
 * Returns a list of requests. This endpoint is paginated using
 * exclusiveStartId and limit parameters.
 *
 */
export const requestQueueRequestsGet = <ThrowOnError extends boolean = false>(options: Options<RequestQueueRequestsGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<RequestQueueRequestsGetResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/request-queues/{queueId}/requests',
        ...options
    });
};

/**
 * Add request
 * Adds request to the queue. Response contains ID of the request and info if
 * request was already present in the queue or handled.
 *
 * If request with same `uniqueKey` was already present in the queue then
 * returns an ID of existing request.
 *
 */
export const requestQueueRequestsPost = <ThrowOnError extends boolean = false>(options: Options<RequestQueueRequestsPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<RequestQueueRequestsPostResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/request-queues/{queueId}/requests',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Delete request
 * Deletes given request from queue.
 */
export const requestQueueRequestDelete = <ThrowOnError extends boolean = false>(options: Options<RequestQueueRequestDeleteData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<RequestQueueRequestDeleteResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/request-queues/{queueId}/requests/{requestId}',
        ...options
    });
};

/**
 * Get request
 * Returns request from queue.
 */
export const requestQueueRequestGet = <ThrowOnError extends boolean = false>(options: Options<RequestQueueRequestGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<RequestQueueRequestGetResponses, unknown, ThrowOnError>({
        security: [
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            },
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/v2/request-queues/{queueId}/requests/{requestId}',
        ...options
    });
};

/**
 * Update request
 * Updates a request in a queue. Mark request as handled by setting
 * `request.handledAt = new Date()`.
 * If `handledAt` is set, the request will be removed from head of the queue (and unlocked, if applicable).
 *
 */
export const requestQueueRequestPut = <ThrowOnError extends boolean = false>(options: Options<RequestQueueRequestPutData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<RequestQueueRequestPutResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/request-queues/{queueId}/requests/{requestId}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Get head
 * Returns given number of first requests from the queue.
 *
 * The response contains the `hadMultipleClients` boolean field which indicates
 * that the queue was accessed by more than one client (with unique or empty
 * `clientKey`).
 * This field is used by [Apify SDK](https://sdk.apify.com) to determine
 * whether the local cache is consistent with the request queue, and thus
 * optimize performance of certain operations.
 *
 */
export const requestQueueHeadGet = <ThrowOnError extends boolean = false>(options: Options<RequestQueueHeadGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<RequestQueueHeadGetResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/request-queues/{queueId}/head',
        ...options
    });
};

/**
 * Get head and lock
 * Returns the given number of first requests from the queue and locks them for
 * the given time.
 *
 * If this endpoint locks the request, no other client or run will be able to get and
 * lock these requests.
 *
 * The response contains the `hadMultipleClients` boolean field which indicates
 * that the queue was accessed by more than one client (with unique or empty
 * `clientKey`).
 *
 */
export const requestQueueHeadLockPost = <ThrowOnError extends boolean = false>(options: Options<RequestQueueHeadLockPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<RequestQueueHeadLockPostResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/request-queues/{queueId}/head/lock',
        ...options
    });
};

/**
 * Delete request lock
 * Deletes a request lock. The request lock can be deleted only by the client
 * that has locked it using [Get and lock head
 * operation](#/reference/request-queues/queue-head-with-locks).
 *
 */
export const requestQueueRequestLockDelete = <ThrowOnError extends boolean = false>(options: Options<RequestQueueRequestLockDeleteData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<RequestQueueRequestLockDeleteResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/request-queues/{queueId}/requests/{requestId}/lock',
        ...options
    });
};

/**
 * Prolong request lock
 * Prolongs request lock. The request lock can be prolonged only by the client
 * that has locked it using [Get and lock head
 * operation](#/reference/request-queues/queue-head-with-locks).
 *
 */
export const requestQueueRequestLockPut = <ThrowOnError extends boolean = false>(options: Options<RequestQueueRequestLockPutData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<RequestQueueRequestLockPutResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/request-queues/{queueId}/requests/{requestId}/lock',
        ...options
    });
};

/**
 * Get list of webhooks
 * Gets the list of webhooks that the user created.
 *
 * The endpoint supports pagination using the `limit` and `offset` parameters
 * and it will not return more than 1000 records.
 * By default, the records are sorted by the `createdAt` field in ascending
 * order. To sort the records in descending order, use the `desc=1`
 * parameter.
 *
 */
export const webhooksGet = <ThrowOnError extends boolean = false>(options?: Options<WebhooksGetData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<WebhooksGetResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/webhooks',
        ...options
    });
};

/**
 * Create webhook
 * Creates a new webhook with settings provided by the webhook object passed as
 * JSON in the payload.
 * The response is the created webhook object.
 *
 * To avoid duplicating a webhook, use the `idempotencyKey` parameter in the
 * request body.
 * Multiple calls to create a webhook with the same `idempotencyKey` will only
 * create the webhook with the first call and return the existing webhook on
 * subsequent calls.
 * Idempotency keys must be unique, so use a UUID or another random string with
 * enough entropy.
 *
 * To assign the new webhook to an Actor or task, the request body must contain
 * `requestUrl`, `eventTypes`, and `condition` properties.
 *
 * * `requestUrl` is the webhook's target URL, to which data is sent as a POST
 * request with a JSON payload.
 * * `eventTypes` is a list of events that will trigger the webhook, e.g. when
 * the Actor run succeeds.
 * * `condition` should be an object containing the ID of the Actor or task to
 * which the webhook will be assigned.
 * * `payloadTemplate` is a JSON-like string, whose syntax is extended with the
 * use of variables.
 * * `headersTemplate` is a JSON-like string, whose syntax is extended with the
 * use of variables. Following values will be re-written to defaults: "host",
 * "Content-Type", "X-Apify-Webhook", "X-Apify-Webhook-Dispatch-Id",
 * "X-Apify-Request-Origin"
 * * `description` is an optional string.
 * * `shouldInterpolateStrings` is a boolean indicating whether to interpolate
 * variables contained inside strings in the `payloadTemplate`
 *
 * ```
 * "isAdHoc" : false,
 * "requestUrl" : "https://example.com",
 * "eventTypes" : [
 * "ACTOR.RUN.SUCCEEDED",
 * "ACTOR.RUN.ABORTED"
 * ],
 * "condition" : {
 * "actorId": "janedoe~my-actor",
 * "actorTaskId" : "W9bs9JE9v7wprjAnJ"
 * },
 * "payloadTemplate": "",
 * "headersTemplate": "",
 * "description": "my awesome webhook",
 * "shouldInterpolateStrings": false,
 * ```
 *
 * **Important**: The request must specify the `Content-Type: application/json`
 * HTTP header.
 *
 */
export const webhooksPost = <ThrowOnError extends boolean = false>(options: Options<WebhooksPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<WebhooksPostResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/webhooks',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Delete webhook
 * Deletes a webhook.
 */
export const webhookDelete = <ThrowOnError extends boolean = false>(options: Options<WebhookDeleteData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<WebhookDeleteResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/webhooks/{webhookId}',
        ...options
    });
};

/**
 * Get webhook
 * Gets webhook object with all details.
 */
export const webhookGet = <ThrowOnError extends boolean = false>(options: Options<WebhookGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<WebhookGetResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/webhooks/{webhookId}',
        ...options
    });
};

/**
 * Update webhook
 * Updates a webhook using values specified by a webhook object passed as JSON
 * in the POST payload.
 * If the object does not define a specific property, its value will not be
 * updated.
 *
 * The response is the full webhook object as returned by the
 * [Get webhook](#/reference/webhooks/webhook-object/get-webhook) endpoint.
 *
 * The request needs to specify the `Content-Type: application/json` HTTP
 * header!
 *
 * When providing your API authentication token, we recommend using the
 * request's `Authorization` header, rather than the URL. ([More
 * info](#/introduction/authentication)).
 *
 */
export const webhookPut = <ThrowOnError extends boolean = false>(options: Options<WebhookPutData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<WebhookPutResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/webhooks/{webhookId}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Test webhook
 * Tests a webhook. Creates a webhook dispatch with a dummy payload.
 */
export const webhookTestPost = <ThrowOnError extends boolean = false>(options: Options<WebhookTestPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<WebhookTestPostResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/webhooks/{webhookId}/test',
        ...options
    });
};

/**
 * Get collection
 * Gets a given webhook's list of dispatches.
 */
export const webhookDispatchesGet = <ThrowOnError extends boolean = false>(options: Options<WebhookDispatchesGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<WebhookDispatchesGetResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/webhooks/{webhookId}/dispatches',
        ...options
    });
};

/**
 * Get list of webhook dispatches
 * Gets the list of webhook dispatches that the user have.
 *
 * The endpoint supports pagination using the `limit` and `offset` parameters
 * and it will not return more than 1000 records.
 * By default, the records are sorted by the `createdAt` field in ascending
 * order. To sort the records in descending order, use the `desc=1`
 * parameter.
 *
 */
export const webhookDispatchesGet2 = <ThrowOnError extends boolean = false>(options?: Options<WebhookDispatchesGet2Data, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<WebhookDispatchesGet2Responses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/webhook-dispatches',
        ...options
    });
};

/**
 * Get webhook dispatch
 * Gets webhook dispatch object with all details.
 */
export const webhookDispatchGet = <ThrowOnError extends boolean = false>(options: Options<WebhookDispatchGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<WebhookDispatchGetResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/webhook-dispatches/{dispatchId}',
        ...options
    });
};

/**
 * Get list of schedules
 * Gets the list of schedules that the user created.
 *
 * The endpoint supports pagination using the `limit` and `offset` parameters.
 * It will not return more than 1000 records.
 *
 * By default, the records are sorted by the `createdAt` field in ascending
 * order. To sort the records in descending order, use the `desc=1` parameter.
 *
 */
export const schedulesGet = <ThrowOnError extends boolean = false>(options?: Options<SchedulesGetData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<SchedulesGetResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/schedules',
        ...options
    });
};

/**
 * Create schedule
 * Creates a new schedule with settings provided by the schedule object passed
 * as JSON in the payload. The response is the created schedule object.
 *
 * The request needs to specify the `Content-Type: application/json` HTTP header!
 *
 * When providing your API authentication token, we recommend using the
 * request's `Authorization` header, rather than the URL. ([More
 * info](#/introduction/authentication)).
 *
 */
export const schedulesPost = <ThrowOnError extends boolean = false>(options: Options<SchedulesPostData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<SchedulesPostResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/schedules',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Delete schedule
 * Deletes a schedule.
 */
export const scheduleDelete = <ThrowOnError extends boolean = false>(options: Options<ScheduleDeleteData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<ScheduleDeleteResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/schedules/{scheduleId}',
        ...options
    });
};

/**
 * Get schedule
 * Gets the schedule object with all details.
 */
export const scheduleGet = <ThrowOnError extends boolean = false>(options: Options<ScheduleGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<ScheduleGetResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/schedules/{scheduleId}',
        ...options
    });
};

/**
 * Update schedule
 * Updates a schedule using values specified by a schedule object passed as
 * JSON in the POST payload. If the object does not define a specific property,
 * its value will not be updated.
 *
 * The response is the full schedule object as returned by the
 * [Get schedule](#/reference/schedules/schedule-object/get-schedule) endpoint.
 *
 * **The request needs to specify the `Content-Type: application/json` HTTP
 * header!**
 *
 * When providing your API authentication token, we recommend using the
 * request's `Authorization` header, rather than the URL. ([More
 * info](#/introduction/authentication)).
 *
 */
export const schedulePut = <ThrowOnError extends boolean = false>(options: Options<SchedulePutData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<SchedulePutResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/schedules/{scheduleId}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Get schedule log
 * Gets the schedule log as a JSON array containing information about up to a
 * 1000 invocations of the schedule.
 *
 */
export const scheduleLogGet = <ThrowOnError extends boolean = false>(options: Options<ScheduleLogGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<ScheduleLogGetResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/schedules/{scheduleId}/log',
        ...options
    });
};

/**
 * Get list of Actors in store
 * Gets the list of public Actors in Apify Store. You can use `search`
 * parameter to search Actors by string in title, name, description, username
 * and readme.
 * If you need detailed info about a specific Actor, use the [Get
 * Actor](#/reference/actors/actor-object/get-actor) endpoint.
 *
 * The endpoint supports pagination using the `limit` and `offset` parameters.
 * It will not return more than 1,000 records.
 *
 */
export const storeGet = <ThrowOnError extends boolean = false>(options?: Options<StoreGetData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<StoreGetResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/store',
        ...options
    });
};

/**
 * Get log
 * Retrieves logs for a specific Actor build or run.
 *
 */
export const logGet = <ThrowOnError extends boolean = false>(options: Options<LogGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<LogGetResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/logs/{buildOrRunId}',
        ...options
    });
};

/**
 * Get public user data
 * Returns public information about a specific user account, similar to what
 * can be seen on public profile pages (e.g. https://apify.com/apify).
 *
 * This operation requires no authentication token.
 *
 */
export const userGet = <ThrowOnError extends boolean = false>(options: Options<UserGetData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<UserGetResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/users/{userId}',
        ...options
    });
};

/**
 * Get private user data
 * Returns information about the current user account, including both public
 * and private information.
 *
 * The user account is identified by the provided authentication token.
 *
 * The fields `plan`, `email` and `profile` are omitted when this endpoint is accessed from Actor run.
 *
 */
export const usersMeGet = <ThrowOnError extends boolean = false>(options?: Options<UsersMeGetData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<UsersMeGetResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/users/me',
        ...options
    });
};

/**
 * Get monthly usage
 * Returns a complete summary of your usage for the current usage cycle,
 * an overall sum, as well as a daily breakdown of usage. It is the same
 * information you will see on your account's [Billing page](https://console.apify.com/billing#/usage). The information
 * includes your use of storage, data transfer, and request queue usage.
 *
 * Using the `date` parameter will show your usage in the usage cycle that
 * includes that date.
 *
 */
export const usersMeUsageMonthlyGet = <ThrowOnError extends boolean = false>(options?: Options<UsersMeUsageMonthlyGetData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<UsersMeUsageMonthlyGetResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/users/me/usage/monthly',
        ...options
    });
};

/**
 * Get limits
 * Returns a complete summary of your account's limits. It is the same
 * information you will see on your account's [Limits page](https://console.apify.com/billing#/limits). The returned data
 * includes the current usage cycle, a summary of your limits, and your current usage.
 *
 */
export const usersMeLimitsGet = <ThrowOnError extends boolean = false>(options?: Options<UsersMeLimitsGetData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<UsersMeLimitsGetResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/users/me/limits',
        ...options
    });
};

/**
 * Update limits
 * Updates the account's limits manageable on your account's [Limits page](https://console.apify.com/billing#/limits).
 * Specifically the: `maxMonthlyUsageUsd` and `dataRetentionDays` limits (see request body schema for more details).
 *
 */
export const usersMeLimitsPut = <ThrowOnError extends boolean = false>(options?: Options<UsersMeLimitsPutData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).put<UsersMeLimitsPutResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            },
            {
                in: 'query',
                name: 'token',
                type: 'apiKey'
            }
        ],
        url: '/v2/users/me/limits',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};