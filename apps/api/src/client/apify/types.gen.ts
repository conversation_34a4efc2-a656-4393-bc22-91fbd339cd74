// This file is auto-generated by @hey-api/openapi-ts

/**
 * PaginationResponse
 */
export type PaginationResponse = {
    total: number;
    offset: number;
    limit: number;
    desc: boolean;
    count: number;
};

/**
 * ActorShort
 */
export type ActorShort = {
    id: string;
    createdAt: string;
    modifiedAt: string;
    name: string;
    username: string;
};

/**
 * GetListOfActorsResponse
 */
export type GetListOfActorsResponse = {
    data: PaginationResponse & {
        items: Array<ActorShort>;
    };
};

/**
 * VersionSourceType
 */
export type VersionSourceType = 'SOURCE_FILES' | 'GIT_REPO' | 'TARBALL' | 'GITHUB_GIST';

/**
 * EnvVar
 */
export type EnvVar = {
    name: string;
    value: string;
    isSecret?: boolean | null;
};

/**
 * VersionSourceFiles
 */
export type VersionSourceFiles = Array<{
    format: 'BASE64' | 'TEXT';
    content: string;
    name: string;
} | {
    name: string;
    folder: true;
}>;

/**
 * Version
 */
export type Version = {
    versionNumber: string;
    sourceType: unknown | VersionSourceType;
    envVars?: Array<EnvVar> | null;
    applyEnvVarsToBuild?: boolean | null;
    buildTag: string;
    sourceFiles?: VersionSourceFiles;
};

/**
 * CommonActorPricingInfo
 */
export type CommonActorPricingInfo = {
    /**
     * In [0, 1], fraction of pricePerUnitUsd that goes to Apify
     */
    apifyMarginPercentage: number;
    /**
     * When this pricing info record has been created
     */
    createdAt: string;
    /**
     * Since when is this pricing info record effective for a given Actor
     */
    startedAt: string;
    notifiedAboutFutureChangeAt?: string | null;
    notifiedAboutChangeAt?: string | null;
    reasonForChange?: string | null;
};

/**
 * ActorChargeEvent
 */
export type ActorChargeEvent = {
    eventPriceUsd: number;
    eventTitle: string;
    eventDescription: string;
};

/**
 * PayPerEventActorPricingInfo
 */
export type PayPerEventActorPricingInfo = CommonActorPricingInfo & {
    pricingModel: 'PAY_PER_EVENT';
    pricingPerEvent: {
        actorChargeEvents?: {
            [key: string]: ActorChargeEvent;
        };
    };
    minimalMaxTotalChargeUsd?: number | null;
};

/**
 * PricePerDatasetItemActorPricingInfo
 */
export type PricePerDatasetItemActorPricingInfo = CommonActorPricingInfo & {
    pricingModel: 'PRICE_PER_DATASET_ITEM';
    /**
     * Name of the unit that is being charged
     */
    unitName: string;
    pricePerUnitUsd: number;
};

/**
 * FlatPricePerMonthActorPricingInfo
 */
export type FlatPricePerMonthActorPricingInfo = CommonActorPricingInfo & {
    pricingModel: 'FLAT_PRICE_PER_MONTH';
    /**
     * For how long this Actor can be used for free in trial period
     */
    trialMinutes: number;
    /**
     * Monthly flat price in USD
     */
    pricePerUnitUsd: number;
};

/**
 * FreeActorPricingInfo
 */
export type FreeActorPricingInfo = CommonActorPricingInfo & {
    pricingModel: 'FREE';
};

/**
 * ActorRunPricingInfo
 */
export type ActorRunPricingInfo = ({
    pricingModel: 'PAY_PER_EVENT';
} & PayPerEventActorPricingInfo) | ({
    pricingModel: 'PRICE_PER_DATASET_ITEM';
} & PricePerDatasetItemActorPricingInfo) | ({
    pricingModel: 'FLAT_PRICE_PER_MONTH';
} & FlatPricePerMonthActorPricingInfo) | ({
    pricingModel: 'FREE';
} & FreeActorPricingInfo);

/**
 * defaultRunOptions
 */
export type DefaultRunOptions = {
    build: string;
    timeoutSecs: number;
    memoryMbytes: number;
};

/**
 * CreateActorRequest
 */
export type CreateActorRequest = {
    name?: string | null;
    description?: string | null;
    title?: string | null;
    isPublic?: boolean | null;
    seoTitle?: string | null;
    seoDescription?: string | null;
    restartOnError?: boolean | null;
    versions?: Array<Version> | null;
    pricingInfos?: Array<ActorRunPricingInfo>;
    categories?: Array<string> | null;
    defaultRunOptions?: unknown | DefaultRunOptions;
};

/**
 * ActorStats
 */
export type ActorStats = {
    totalBuilds: number;
    totalRuns: number;
    totalUsers: number;
    totalUsers7Days: number;
    totalUsers30Days: number;
    totalUsers90Days: number;
    totalMetamorphs: number;
    lastRunStartedAt: string;
};

/**
 * exampleRunInput
 */
export type ExampleRunInput = {
    body: string;
    contentType: string;
};

/**
 * taggedBuilds
 */
export type TaggedBuilds = {
    latest?: unknown | {
        buildId?: string | null;
        buildNumber?: string | null;
        finishedAt?: string | null;
    };
};

/**
 * Actor
 */
export type Actor = {
    id: string;
    userId: string;
    name: string;
    username: string;
    description?: string | null;
    restartOnError?: boolean | null;
    isPublic: boolean;
    createdAt: string;
    modifiedAt: string;
    stats: ActorStats;
    versions: Array<Version>;
    pricingInfos?: Array<ActorRunPricingInfo>;
    defaultRunOptions: DefaultRunOptions;
    exampleRunInput?: unknown | ExampleRunInput;
    isDeprecated?: boolean | null;
    deploymentKey: string;
    title?: string | null;
    taggedBuilds?: unknown | TaggedBuilds;
};

/**
 * CreateActorResponse
 */
export type CreateActorResponse = {
    data: Actor;
};

/**
 * GetActorResponse
 */
export type GetActorResponse = {
    data: Actor;
};

/**
 * CreateOrUpdateEnvVarRequest
 */
export type CreateOrUpdateEnvVarRequest = {
    name: string;
    value: string;
    isSecret?: boolean | null;
};

/**
 * ActUpdate
 */
export type UpdateActorRequest = {
    name: string;
    description?: string | null;
    isPublic: boolean;
    seoTitle?: string | null;
    seoDescription?: string | null;
    title?: string | null;
    restartOnError?: boolean | null;
    versions: Array<CreateOrUpdateEnvVarRequest>;
    pricingInfos?: Array<ActorRunPricingInfo>;
    categories?: Array<string> | null;
    defaultRunOptions?: unknown | DefaultRunOptions;
};

/**
 * UpdateActorResponse
 */
export type UpdateActorResponse = {
    data: Actor;
};

/**
 * GetVersionListResponse
 */
export type GetVersionListResponse = {
    data: {
        total: number;
        items: Array<Version>;
    };
};

/**
 * CreateOrUpdateVersionRequest
 */
export type CreateOrUpdateVersionRequest = {
    versionNumber?: string | null;
    sourceType?: unknown | VersionSourceType;
    envVars?: Array<EnvVar> | null;
    applyEnvVarsToBuild?: boolean | null;
    buildTag?: string | null;
    sourceFiles?: VersionSourceFiles;
};

/**
 * GetVersionResponse
 */
export type GetVersionResponse = {
    data: Version;
};

/**
 * GetEnvVarListResponse
 */
export type GetEnvVarListResponse = {
    data: {
        total: number;
        items: Array<EnvVar>;
    };
};

/**
 * CreateEnvironmentVariableResponse
 */
export type GetEnvVarResponse = {
    data: EnvVar;
};

/**
 * WebhookCondition
 */
export type WebhookCondition = {
    actorId?: string | null;
    actorTaskId?: string | null;
    actorRunId?: string | null;
};

/**
 * ExampleWebhookDispatch
 */
export type ExampleWebhookDispatch = {
    status: string;
    finishedAt: string;
};

/**
 * WebhookStats
 */
export type WebhookStats = {
    totalDispatches: number;
};

/**
 * WebhookShort
 */
export type WebhookShort = {
    id: string;
    createdAt: string;
    modifiedAt: string;
    userId: string;
    isAdHoc?: boolean | null;
    shouldInterpolateStrings?: boolean | null;
    eventTypes: Array<string>;
    condition: WebhookCondition;
    ignoreSslErrors: boolean;
    doNotRetry: boolean;
    requestUrl: string;
    lastDispatch?: ExampleWebhookDispatch | null;
    stats?: WebhookStats | null;
};

/**
 * GetListOfWebhooksResponse
 */
export type GetListOfWebhooksResponse = {
    data: PaginationResponse & {
        items?: Array<WebhookShort>;
    };
};

/**
 * BuildsMeta
 */
export type BuildsMeta = {
    origin: string;
    clientIp: string;
    userAgent: string;
};

/**
 * BuildShort
 */
export type BuildShort = {
    id: string;
    actId?: string;
    status: string;
    startedAt: string;
    finishedAt: string;
    usageTotalUsd: number;
    meta?: BuildsMeta & unknown;
};

/**
 * GetBuildListResponse
 */
export type GetBuildListResponse = {
    data: {
        total: number;
        offset: number;
        limit: number;
        desc: boolean;
        count: number;
        items: Array<BuildShort>;
    };
};

/**
 * BuildStats
 */
export type BuildStats = {
    durationMillis: number;
    runTimeSecs: number;
    computeUnits: number;
};

/**
 * BuildOptions
 */
export type BuildOptions = {
    useCache?: boolean | null;
    betaPackages?: boolean | null;
    memoryMbytes?: number | null;
    diskMbytes?: number | null;
};

/**
 * BuildUsage
 */
export type BuildUsage = {
    ACTOR_COMPUTE_UNITS?: number | null;
};

/**
 * ActorDefinition
 * The definition of the Actor, the full specification of this field can be found in [Apify docs](https://docs.apify.com/platform/actors/development/actor-definition/actor-json)
 */
export type ActorDefinition = {
    /**
     * The Actor specification version that this Actor follows. This property must be set to 1.
     */
    actorSpecification: 1;
    /**
     * The name of the Actor.
     */
    name: string;
    /**
     * The version of the Actor, specified in the format [Number].[Number], e.g., 0.1, 1.0.
     */
    version: string;
    /**
     * The tag name to be applied to a successful build of the Actor. Defaults to 'latest' if not specified.
     */
    buildTag?: string;
    /**
     * A map of environment variables to be used during local development and deployment.
     */
    environmentVariables?: {
        [key: string]: string;
    };
    /**
     * The path to the Dockerfile used for building the Actor on the platform.
     */
    dockerfile?: string;
    /**
     * The path to the directory used as the Docker context when building the Actor.
     */
    dockerContextDir?: string;
    /**
     * The path to the README file for the Actor.
     */
    readme?: string;
    /**
     * The input schema object, the full specification can be found in [Apify docs](https://docs.apify.com/platform/actors/development/actor-definition/input-schema)
     */
    input?: {
        [key: string]: unknown;
    };
    /**
     * The path to the CHANGELOG file displayed in the Actor's information tab.
     */
    changelog?: string;
    storages?: {
        /**
         * Defines the schema of items in your dataset, the full specification can be found in [Apify docs](https://docs.apify.com/platform/actors/development/actor-definition/dataset-schema)
         */
        dataset?: {
            [key: string]: unknown;
        };
    };
    /**
     * Specifies the minimum amount of memory in megabytes required by the Actor.
     */
    minMemoryMbytes?: number;
    /**
     * Specifies the maximum amount of memory in megabytes required by the Actor.
     */
    maxMemoryMbytes?: number;
    /**
     * Specifies whether the Actor will have Standby mode enabled.
     */
    usesStandbyMode?: boolean;
};

/**
 * Build
 */
export type Build = {
    id: string;
    actId: string;
    userId: string;
    startedAt: string;
    finishedAt?: string | null;
    status: string;
    meta: BuildsMeta;
    stats?: (unknown | BuildStats) & unknown;
    options?: (unknown | BuildOptions) & unknown;
    usage?: (unknown | BuildUsage) & unknown;
    usageTotalUsd?: number | null;
    usageUsd?: (unknown | BuildUsage) & unknown;
    /**
     * @deprecated
     */
    inputSchema?: string | null;
    /**
     * @deprecated
     */
    readme?: string | null;
    buildNumber: string;
    actorDefinition?: ActorDefinition | unknown;
};

/**
 * BuildActorResponse
 */
export type BuildActorResponse = {
    data: Build;
};

/**
 * GetBuildResponse
 */
export type GetBuildResponse = {
    data: Build & unknown;
};

/**
 * GetOpenApiResponse
 */
export type GetOpenApiResponse = {
    openapi?: string;
    info?: {
        title?: string;
        version?: string;
        'x-build-id'?: string;
    };
    servers?: Array<{
        url?: string;
    }>;
    paths?: {
        '/acts/<username>~<actor>/run-sync-get-dataset-items'?: {
            post?: {
                operationId?: string;
                'x-openai-isConsequential'?: boolean;
                summary?: string;
                tags?: Array<string>;
                requestBody?: {
                    required?: boolean;
                    content?: {
                        'application/json'?: {
                            schema?: {
                                $ref?: string;
                            };
                        };
                    };
                };
                parameters?: Array<{
                    name?: string;
                    in?: string;
                    required?: boolean;
                    schema?: {
                        type?: string;
                    };
                    description?: string;
                }>;
                responses?: {
                    200?: {
                        description?: string;
                    };
                };
            };
        };
        '/acts/<username>~<actor>/runs'?: {
            post?: {
                operationId?: string;
                'x-openai-isConsequential'?: boolean;
                summary?: string;
                tags?: Array<string>;
                requestBody?: {
                    required?: boolean;
                    content?: {
                        'application/json'?: {
                            schema?: {
                                $ref?: string;
                            };
                        };
                    };
                };
                parameters?: Array<{
                    name?: string;
                    in?: string;
                    required?: boolean;
                    schema?: {
                        type?: string;
                    };
                    description?: string;
                }>;
                responses?: {
                    200?: {
                        description?: string;
                        content?: {
                            'application/json'?: {
                                schema?: {
                                    $ref?: string;
                                };
                            };
                        };
                    };
                };
            };
        };
        '/acts/<username>~<actor>/run-sync'?: {
            post?: {
                operationId?: string;
                'x-openai-isConsequential'?: boolean;
                summary?: string;
                tags?: Array<string>;
                requestBody?: {
                    required?: boolean;
                    content?: {
                        'application/json'?: {
                            schema?: {
                                $ref?: string;
                            };
                        };
                    };
                };
                parameters?: Array<{
                    name?: string;
                    in?: string;
                    required?: boolean;
                    schema?: {
                        type?: string;
                    };
                    description?: string;
                }>;
                responses?: {
                    200?: {
                        description?: string;
                    };
                };
            };
        };
    };
    components?: {
        schemas?: {
            inputSchema?: {
                type?: string;
            };
            runsResponseSchema?: {
                type?: string;
                properties?: {
                    data?: {
                        type?: string;
                        properties?: {
                            id?: {
                                type?: string;
                            };
                            actId?: {
                                type?: string;
                            };
                            userId?: {
                                type?: string;
                            };
                            startedAt?: {
                                type?: string;
                                format?: string;
                                example?: string;
                            };
                            finishedAt?: {
                                type?: string;
                                format?: string;
                                example?: string;
                            };
                            status?: {
                                type?: string;
                                example?: string;
                            };
                            meta?: {
                                type?: string;
                                properties?: {
                                    origin?: {
                                        type?: string;
                                        example?: string;
                                    };
                                    userAgent?: {
                                        type?: string;
                                    };
                                };
                            };
                        };
                    };
                };
            };
        };
    };
};

/**
 * PostAbortBuildResponse
 */
export type PostAbortBuildResponse = {
    data: Build & unknown;
};

/**
 * RunMeta
 */
export type RunMeta = {
    origin: 'DEVELOPMENT' | 'WEB' | 'API' | 'SCHEDULER' | 'TEST' | 'WEBHOOK' | 'ACTOR' | 'CLI' | 'STANDBY';
};

/**
 * RunShort
 */
export type RunShort = {
    id: string;
    actId: string;
    actorTaskId?: string | null;
    status: string;
    startedAt: string;
    finishedAt: string;
    buildId: string;
    buildNumber: string;
    meta: RunMeta & unknown;
    usageTotalUsd: number;
    defaultKeyValueStoreId: string;
    defaultDatasetId: string;
    defaultRequestQueueId: string;
};

/**
 * GetUserRunsListResponse
 */
export type GetUserRunsListResponse = {
    data: PaginationResponse & {
        items: Array<RunShort>;
    };
};

/**
 * RunStats
 */
export type RunStats = {
    inputBodyLen?: number;
    migrationCount?: number;
    restartCount: number;
    resurrectCount: number;
    memAvgBytes?: number;
    memMaxBytes?: number;
    memCurrentBytes?: number;
    cpuAvgUsage?: number;
    cpuMaxUsage?: number;
    cpuCurrentUsage?: number;
    netRxBytes?: number;
    netTxBytes?: number;
    durationMillis?: number;
    runTimeSecs?: number;
    metamorph?: number;
    computeUnits: number;
};

/**
 * RunOptions
 */
export type RunOptions = {
    build: string;
    timeoutSecs: number;
    memoryMbytes: number;
    diskMbytes: number;
};

/**
 * RunUsage
 */
export type RunUsage = {
    ACTOR_COMPUTE_UNITS?: number | null;
    DATASET_READS?: number | null;
    DATASET_WRITES?: number | null;
    KEY_VALUE_STORE_READS?: number | null;
    KEY_VALUE_STORE_WRITES?: number | null;
    KEY_VALUE_STORE_LISTS?: number | null;
    REQUEST_QUEUE_READS?: number | null;
    REQUEST_QUEUE_WRITES?: number | null;
    DATA_TRANSFER_INTERNAL_GBYTES?: number | null;
    'DATA_TRANSFER_EXTERNAL_GBYTES?'?: number | null;
    PROXY_RESIDENTIAL_TRANSFER_GBYTES?: number | null;
    PROXY_SERPS?: number | null;
};

/**
 * Run
 */
export type Run = {
    id: string;
    actId: string;
    userId: string;
    actorTaskId?: string | null;
    startedAt: string;
    finishedAt: string;
    status: string;
    statusMessage?: string | null;
    isStatusMessageTerminal?: boolean | null;
    meta: RunMeta;
    pricingInfo?: ActorRunPricingInfo;
    stats: RunStats;
    chargedEventCounts?: {
        [key: string]: ActorChargeEvent;
    };
    options: RunOptions;
    buildId: string;
    exitCode?: number | null;
    defaultKeyValueStoreId: string;
    defaultDatasetId: string;
    defaultRequestQueueId: string;
    buildNumber: string;
    containerUrl: string;
    isContainerServerReady?: boolean | null;
    gitBranchName?: string | null;
    usage?: (unknown | RunUsage) & unknown;
    usageTotalUsd?: number | null;
    usageUsd?: (unknown | RunUsage) & unknown;
};

/**
 * RunResponse
 */
export type RunResponse = {
    data: Run & unknown;
};

/**
 * ErrorResponse
 */
export type ErrorResponse = {
    error: {
        type: string;
        message: string;
    };
};

/**
 * TaskStats
 */
export type TaskStats = {
    totalRuns: number;
};

/**
 * TaskShort
 */
export type TaskShort = {
    id: string;
    userId: string;
    actId: string;
    actName: string;
    name: string;
    username?: string | null;
    actUsername: string;
    createdAt: string;
    modifiedAt: string;
    stats?: (unknown | TaskStats) & unknown;
};

/**
 * TaskOptions
 */
export type TaskOptions = {
    build?: string | null;
    timeoutSecs?: number | null;
    memoryMbytes?: number | null;
};

/**
 * TaskInput
 */
export type TaskInput = {
    hello?: string | null;
};

/**
 * CreateTaskRequest
 */
export type CreateTaskRequest = {
    actId: string;
    name: string;
    options?: (unknown | TaskOptions) & unknown;
    input?: (unknown | TaskInput) & unknown;
};

/**
 * Task
 */
export type Task = {
    id: string;
    userId: string;
    actId: string;
    name: string;
    username?: string | null;
    createdAt: string;
    modifiedAt: string;
    removedAt?: string | null;
    stats?: (unknown | TaskStats) & unknown;
    options?: (unknown | TaskOptions) & unknown;
    input?: (unknown | TaskInput) & unknown;
};

/**
 * UpdateTaskRequest
 */
export type UpdateTaskRequest = {
    id: string;
    userId: string;
    actId: string;
    name: string;
    username?: string | null;
    createdAt: string;
    modifiedAt: string;
    removedAt?: string | null;
    stats?: (unknown | TaskStats) & unknown;
    options?: (unknown | TaskOptions) & unknown;
    input?: (unknown | Task) & unknown;
};

/**
 * Webhook
 */
export type Webhook = {
    id: string;
    createdAt: string;
    modifiedAt: string;
    userId: string;
    isAdHoc?: boolean | null;
    shouldInterpolateStrings?: boolean | null;
    eventTypes: Array<string>;
    condition: WebhookCondition;
    ignoreSslErrors: boolean;
    doNotRetry?: boolean | null;
    requestUrl: string;
    payloadTemplate?: string | null;
    headersTemplate?: string | null;
    description?: string | null;
    lastDispatch?: ExampleWebhookDispatch | null;
    stats?: WebhookStats | null;
};

/**
 * UpdateRunRequest
 */
export type UpdateRunRequest = {
    runId: string;
    statusMessage: string;
    isStatusMessageTerminal?: boolean;
};

/**
 * ChargeRunRequest
 */
export type ChargeRunRequest = {
    eventName: string;
    eventCount: number;
};

/**
 * KeyValueStoreStats
 */
export type KeyValueStoreStats = {
    readCount: number;
    writeCount: number;
    deleteCount: number;
    listCount: number;
    s3StorageBytes: number;
};

/**
 * KeyValueStore
 */
export type KeyValueStore = {
    id: string;
    name: string;
    userId?: string | null;
    username?: string | null;
    createdAt: string;
    modifiedAt: string;
    accessedAt: string;
    actId?: string | null;
    actRunId?: string | null;
    consoleUrl: string;
    stats?: KeyValueStoreStats;
};

/**
 * GetListOfKeyValueStoresResponse
 */
export type GetListOfKeyValueStoresResponse = {
    data: PaginationResponse & {
        items: Array<KeyValueStore>;
    };
};

/**
 * CreateKeyValueStoreResponse
 */
export type CreateKeyValueStoreResponse = {
    data: KeyValueStore;
};

/**
 * GetStoreResponse
 */
export type GetStoreResponse = {
    data: KeyValueStore;
};

/**
 * UpdateStoreRequest
 */
export type UpdateStoreRequest = {
    name: string;
};

/**
 * UpdateStoreResponse
 */
export type UpdateStoreResponse = {
    data: KeyValueStore;
};

/**
 * ListOfKeysResponse
 */
export type ListOfKeysResponse = {
    items: Array<{
        key: string;
        size: number;
    }>;
    count: number;
    limit: number;
    exclusiveStartKey?: string;
    isTruncated: boolean;
    nextExclusiveStartKey?: string;
};

/**
 * GetListOfKeysResponse
 */
export type GetListOfKeysResponse = {
    data: ListOfKeysResponse;
};

/**
 * GetRecordResponse
 */
export type GetRecordResponse = {
    foo: string;
};

/**
 * PutRecordRequest
 */
export type PutRecordRequest = {
    foo?: string;
};

/**
 * DatasetListItem
 */
export type DatasetListItem = {
    id: string;
    name: string;
    userId: string;
    createdAt: string;
    modifiedAt: string;
    accessedAt: string;
    itemCount: number;
    cleanItemCount: number;
    actId?: string | null;
    actRunId?: string | null;
};

/**
 * GetListOfDatasetsResponse
 */
export type GetListOfDatasetsResponse = {
    data: PaginationResponse & {
        items: Array<DatasetListItem>;
    };
};

/**
 * DatasetStats
 */
export type DatasetStats = {
    readCount: number;
    writeCount: number;
    storageBytes: number;
};

/**
 * Dataset
 */
export type Dataset = {
    id: string;
    name: string;
    userId: string;
    createdAt: string;
    modifiedAt: string;
    accessedAt: string;
    itemCount: number;
    cleanItemCount: number;
    actId?: string | null;
    actRunId?: string | null;
    fields?: Array<string> | null;
    /**
     * Defines the schema of items in your dataset, the full specification can be found in [Apify docs](/platform/actors/development/actor-definition/dataset-schema)
     */
    schema?: {
        [key: string]: unknown;
    } | null;
    consoleUrl: string;
    stats?: DatasetStats;
};

/**
 * DatasetResponse
 */
export type DatasetResponse = {
    data: Dataset;
};

/**
 * UpdateDatasetRequest
 */
export type UpdateDatasetRequest = {
    name: string;
};

/**
 * PutItemsRequest
 */
export type PutItemsRequest = {
    foo: string;
};

export type DatasetSchemaValidationError = {
    error?: {
        /**
         * The type of the error.
         */
        type: string;
        /**
         * A human-readable message describing the error.
         */
        message: string;
        data: {
            /**
             * A list of invalid items in the received array of items.
             */
            invalidItems: Array<{
                /**
                 * The position of the invalid item in the array.
                 */
                itemPosition?: number;
                /**
                 * A complete list of AJV validation error objects for the invalid item.
                 */
                validationErrors?: Array<{
                    /**
                     * The path to the instance being validated.
                     */
                    instancePath?: string;
                    /**
                     * The path to the schema that failed the validation.
                     */
                    schemaPath?: string;
                    /**
                     * The validation keyword that caused the error.
                     */
                    keyword?: string;
                    /**
                     * A message describing the validation error.
                     */
                    message?: string;
                    /**
                     * Additional parameters specific to the validation error.
                     */
                    params?: {
                        [key: string]: unknown;
                    };
                }>;
            }>;
        };
    };
};

/**
 * PutItemResponseError
 */
export type PutItemResponseError = {
    error: DatasetSchemaValidationError & unknown;
};

/**
 * DatasetFieldStatistics
 */
export type DatasetFieldStatistics = {
    /**
     * Minimum value of the field. For numbers, this is calculated directly. For strings, this is the length of the shortest string. For arrays, this is the length of the shortest array. For objects, this is the number of keys in the smallest object.
     */
    min?: number | null;
    /**
     * Maximum value of the field. For numbers, this is calculated directly. For strings, this is the length of the longest string. For arrays, this is the length of the longest array. For objects, this is the number of keys in the largest object.
     */
    max?: number | null;
    /**
     * How many items in the dataset have a null value for this field.
     */
    nullCount?: number | null;
    /**
     * How many items in the dataset are `undefined`, meaning that for example empty string is not considered empty.
     */
    emptyCount?: number | null;
};

/**
 * GetDatasetStatisticsResponse
 */
export type GetDatasetStatisticsResponse = {
    data: {
        /**
         * When you configure the dataset [fields schema](https://docs.apify.com/platform/actors/development/actor-definition/dataset-schema/validation), we measure the statistics such as `min`, `max`, `nullCount` and `emptyCount` for each field. This property provides statistics for each field from dataset fields schema. <br/></br>See dataset field statistics [documentation](https://docs.apify.com/platform/actors/development/actor-definition/dataset-schema/validation#dataset-field-statistics) for more information.
         */
        fieldStatistics?: {
            [key: string]: DatasetFieldStatistics;
        } | null;
    };
};

/**
 * RequestQueueShort
 */
export type RequestQueueShort = {
    id: string;
    name: string;
    userId: string;
    username: string;
    createdAt: string;
    modifiedAt: string;
    accessedAt: string;
    expireAt: string;
    totalRequestCount: number;
    handledRequestCount: number;
    pendingRequestCount: number;
    actId?: string | null;
    actRunId?: string | null;
    hadMultipleClients: boolean;
};

/**
 * GetListOfRequestQueuesResponse
 */
export type GetListOfRequestQueuesResponse = {
    data: {
        total: number;
        offset: number;
        limit: number;
        desc: boolean;
        count: number;
        items: Array<RequestQueueShort>;
    };
};

/**
 * RequestQueue
 */
export type RequestQueue = {
    id: string;
    name?: string;
    userId: string;
    createdAt: string;
    /**
     * The modifiedAt is updated whenever the queue is modified. Modifications include adding, updating, or removing requests, as well as locking or unlocking requests in the queue.
     */
    modifiedAt: string;
    accessedAt: string;
    totalRequestCount: number;
    handledRequestCount: number;
    pendingRequestCount: number;
    hadMultipleClients: boolean;
    consoleUrl: string;
};

/**
 * CreateRequestQueueResponse
 */
export type CreateRequestQueueResponse = {
    data: RequestQueue;
};

/**
 * GetRequestQueueResponse
 */
export type GetRequestQueueResponse = {
    data: RequestQueue & unknown;
};

/**
 * UpdateRequestQueueRequest
 */
export type UpdateRequestQueueRequest = {
    name: string;
};

/**
 * UpdateRequestQueueResponse
 */
export type UpdateRequestQueueResponse = {
    data: RequestQueue & unknown;
};

/**
 * RequestWithoutId
 */
export type RequestWithoutId = {
    uniqueKey: string;
    url: string;
    method: string;
};

/**
 * ProcessedRequest
 */
export type ProcessedRequest = {
    requestId: string;
    uniqueKey: string;
    wasAlreadyPresent: boolean;
    wasAlreadyHandled: boolean;
};

/**
 * UnprocessedRequest
 */
export type UnprocessedRequest = {
    uniqueKey: string;
    url: string;
    method: string;
};

/**
 * BatchOperationResponse
 */
export type BatchOperationResponse = {
    data: {
        processedRequests: Array<ProcessedRequest>;
        unprocessedRequests: Array<UnprocessedRequest>;
    };
};

/**
 * UserData
 */
export type UserData = {
    label?: string | null;
    image?: string | null;
};

/**
 * RequestQueueItems
 */
export type RequestQueueItems = {
    id: string;
    retryCount: number;
    uniqueKey: string;
    url: string;
    method: string;
    loadedUrl?: string | null;
    payload?: {
        [key: string]: unknown;
    } | null;
    noRetry?: boolean | null;
    errorMessages?: Array<string> | null;
    headers?: {
        [key: string]: unknown;
    } | null;
    userData?: UserData & unknown;
    handledAt?: string | null;
};

/**
 * ListRequestsResponse
 */
export type ListRequestsResponse = {
    data: {
        items: Array<RequestQueueItems>;
        count: number;
        limit: number;
        exclusiveStartId?: string;
    };
};

/**
 * RequestOperationInfo
 */
export type RequestOperationInfo = {
    requestId: string;
    wasAlreadyPresent: boolean;
    wasAlreadyHandled: boolean;
};

/**
 * AddRequestResponse
 */
export type AddRequestResponse = {
    data: RequestOperationInfo & unknown;
};

/**
 * GetRequestResponse
 */
export type GetRequestResponse = {
    data: RequestQueueItems & unknown;
};

/**
 * UpdateRequestResponse
 */
export type UpdateRequestResponse = {
    data: RequestOperationInfo & unknown;
};

/**
 * GetHeadResponse
 */
export type GetHeadResponse = {
    data: {
        limit: number;
        queueModifiedAt: string;
        hadMultipleClients: boolean;
        items: Array<{
            id: string;
            retryCount: number;
            uniqueKey: string;
            url: string;
            method: string;
        }>;
    };
};

/**
 * GetHeadAndLockResponse
 */
export type GetHeadAndLockResponse = {
    data: {
        limit: number;
        /**
         * The modifiedAt is updated whenever the queue is modified. Modifications include adding, updating, or removing requests, as well as locking or unlocking requests in the queue.
         */
        queueModifiedAt: string;
        /**
         * Whether the queue contains requests locked by any client (either the one calling the endpoint or a different one).
         */
        queueHasLockedRequests?: boolean;
        clientKey?: string;
        hadMultipleClients: boolean;
        lockSecs: number;
        items: Array<{
            id: string;
            retryCount: number;
            uniqueKey: string;
            url: string;
            method: string;
            lockExpiresAt: string;
        }>;
    };
};

/**
 * ProlongRequestLockResponse
 */
export type ProlongRequestLockResponse = {
    data?: {
        /**
         * Date when lock expires.
         */
        lockExpiresAt: string;
    };
};

/**
 * WebhookCreate
 */
export type WebhookCreate = {
    isAdHoc?: boolean | null;
    eventTypes: Array<string>;
    condition: WebhookCondition;
    idempotencyKey?: string | null;
    ignoreSslErrors?: boolean | null;
    doNotRetry?: boolean | null;
    requestUrl: string;
    payloadTemplate?: string | null;
    headersTemplate?: string | null;
    description?: string | null;
    shouldInterpolateStrings?: boolean | null;
};

/**
 * CreateWebhookResponse
 */
export type CreateWebhookResponse = {
    data: Webhook;
};

/**
 * GetWebhookResponse
 */
export type GetWebhookResponse = {
    data: Webhook;
};

/**
 * WebhookUpdate
 */
export type WebhookUpdate = {
    isAdHoc?: boolean | null;
    eventTypes?: Array<string> | null;
    condition?: WebhookCondition | null;
    ignoreSslErrors?: boolean | null;
    doNotRetry?: boolean | null;
    requestUrl?: string | null;
    payloadTemplate?: string | null;
    headersTemplate?: string | null;
    description?: string | null;
    shouldInterpolateStrings?: boolean | null;
};

/**
 * UpdateWebhookResponse
 */
export type UpdateWebhookResponse = {
    data: Webhook;
};

/**
 * WebhookDispatch
 */
export type WebhookDispatch = {
    id: string;
    userId: string;
    webhookId: string;
    createdAt: string;
    status: string;
    eventType: string;
    /**
     * eventData
     */
    eventData: {
        actorId: string;
        actorRunId: string;
    };
    /**
     * calls
     */
    calls?: {
        startedAt?: string | null;
        finishedAt?: string | null;
        errorMessage?: string | null;
        responseStatus?: number | null;
        responseBody?: string | null;
    };
};

/**
 * TestWebhookResponse
 */
export type TestWebhookResponse = {
    data: WebhookDispatch;
};

/**
 * WebhookDispatchList
 */
export type WebhookDispatchList = {
    data?: PaginationResponse & {
        items: Array<WebhookDispatch>;
    };
};

/**
 * GetWebhookDispatchResponse
 */
export type GetWebhookDispatchResponse = {
    data: WebhookDispatch;
};

/**
 * GetListOfSchedulesResponseDataItemsActions
 */
export type GetListOfSchedulesResponseDataItemsActions = {
    id: string;
    type: string;
    actorId: string;
};

/**
 * GetListOfSchedulesResponseDataItems
 */
export type GetListOfSchedulesResponseDataItems = {
    id: string;
    userId: string;
    name: string;
    createdAt: string;
    modifiedAt: string;
    lastRunAt: string;
    nextRunAt: string;
    isEnabled: boolean;
    isExclusive: boolean;
    cronExpression: string;
    timezone: string;
    actions: Array<GetListOfSchedulesResponseDataItemsActions>;
};

/**
 * GetListOfSchedulesResponseData
 */
export type GetListOfSchedulesResponseData = {
    total: number;
    offset: number;
    limit: number;
    desc: boolean;
    count: number;
    items: Array<GetListOfSchedulesResponseDataItems>;
};

/**
 * GetListOfSchedulesResponse
 */
export type GetListOfSchedulesResponse = {
    data: GetListOfSchedulesResponseData;
};

/**
 * ScheduleActionsRunInput
 */
export type ScheduleActionsRunInput = {
    body?: string | null;
    contentType?: string | null;
};

/**
 * ScheduleActionsRunOptions
 */
export type ScheduleActionsRunOptions = {
    build?: string | null;
    timeoutSecs?: number | null;
    memoryMbytes?: number | null;
};

/**
 * CreateScheduleActions
 */
export type ScheduleCreateActions = {
    type: string;
    actorId: string;
    runInput?: ScheduleActionsRunInput | null;
    runOptions?: ScheduleActionsRunOptions | null;
};

/**
 * ScheduleCreate
 */
export type ScheduleCreate = {
    name?: string | null;
    isEnabled?: boolean | null;
    isExclusive?: boolean | null;
    cronExpression?: string | null;
    timezone?: string | null;
    description?: string | null;
    actions?: Array<ScheduleCreateActions> | null;
};

/**
 * ScheduleActions
 */
export type ScheduleResponseDataActions = {
    id: string;
    type: string;
    actorId: string;
    runInput?: ScheduleActionsRunInput | null;
    runOptions?: ScheduleActionsRunOptions | null;
};

/**
 * Schedule
 */
export type ScheduleResponseData = {
    id: string;
    userId: string;
    name: string;
    cronExpression: string;
    timezone: string;
    isEnabled: boolean;
    isExclusive: boolean;
    description?: string | null;
    createdAt: string;
    modifiedAt: string;
    nextRunAt?: string | null;
    lastRunAt?: string | null;
    actions: Array<ScheduleResponseDataActions>;
};

/**
 * CreateScheduleResponse
 */
export type ScheduleResponse = {
    data: ScheduleResponseData;
};

/**
 * ScheduleInvoked
 */
export type ScheduleInvoked = {
    message: string;
    level: string;
    createdAt: string;
};

/**
 * GetScheduleLogResponse
 */
export type GetScheduleLogResponse = {
    data: Array<ScheduleInvoked>;
};

/**
 * currentPricingInfo
 */
export type CurrentPricingInfo = {
    pricingModel: string;
};

/**
 * StoreListActor
 */
export type StoreListActor = {
    id: string;
    title: string;
    name: string;
    username: string;
    userFullName: string;
    description: string;
    categories?: Array<string>;
    notice?: string;
    pictureUrl?: string | null;
    userPictureUrl?: string | null;
    url?: string | null;
    stats: ActorStats;
    currentPricingInfo: CurrentPricingInfo;
};

/**
 * StoreData
 */
export type StoreData = {
    total: number;
    offset: number;
    limit: number;
    desc: boolean;
    count: number;
    items: Array<StoreListActor>;
};

/**
 * GetListOfActorsInStoreResponse
 */
export type GetListOfActorsInStoreResponse = {
    data: StoreData;
};

/**
 * Profile
 */
export type Profile = {
    bio?: string;
    name?: string;
    pictureUrl?: string;
    githubUsername?: string;
    websiteUrl?: string;
    twitterUsername?: string;
};

/**
 * UserPublicInfo
 */
export type UserPublicInfo = {
    username: string;
    profile: Profile;
};

/**
 * GetPublicUserDataResponse
 */
export type GetPublicUserDataResponse = {
    data: UserPublicInfo;
};

/**
 * ProxyGroup
 */
export type ProxyGroup = {
    name: string;
    description: string;
    availableCount: number;
};

/**
 * Proxy
 */
export type Proxy = {
    password: string;
    groups: Array<unknown>;
};

/**
 * AvailableProxyGroups
 */
export type AvailableProxyGroups = {
    SOMEGROUP: number;
    ANOTHERGROUP: number;
};

/**
 * Plan
 */
export type Plan = {
    id: string;
    description: string;
    isEnabled: boolean;
    monthlyBasePriceUsd: number;
    monthlyUsageCreditsUsd: number;
    usageDiscountPercent: number;
    enabledPlatformFeatures: Array<Array<unknown>>;
    maxMonthlyUsageUsd: number;
    maxActorMemoryGbytes: number;
    maxMonthlyActorComputeUnits: number;
    maxMonthlyResidentialProxyGbytes: number;
    maxMonthlyProxySerps: number;
    maxMonthlyExternalDataTransferGbytes: number;
    maxActorCount: number;
    maxActorTaskCount: number;
    dataRetentionDays: number;
    availableProxyGroups: AvailableProxyGroups;
    teamAccountSeatCount: number;
    supportLevel: string;
    availableAddOns: Array<string>;
};

/**
 * EffectivePlatformFeature
 */
export type EffectivePlatformFeature = {
    isEnabled: boolean;
    disabledReason: string | null;
    disabledReasonType: string | null;
    isTrial: boolean;
    trialExpirationAt: string | null;
};

/**
 * EffectivePlatformFeatures
 */
export type EffectivePlatformFeatures = {
    ACTORS: EffectivePlatformFeature;
    STORAGE: EffectivePlatformFeature;
    SCHEDULER: EffectivePlatformFeature;
    PROXY: EffectivePlatformFeature;
    PROXY_EXTERNAL_ACCESS: EffectivePlatformFeature;
    PROXY_RESIDENTIAL: EffectivePlatformFeature;
    PROXY_SERPS: EffectivePlatformFeature;
    WEBHOOKS: EffectivePlatformFeature;
    ACTORS_PUBLIC_ALL: EffectivePlatformFeature;
    ACTORS_PUBLIC_DEVELOPER: EffectivePlatformFeature;
};

/**
 * UserPrivateInfo
 */
export type UserPrivateInfo = {
    id: string;
    username: string;
    profile: Profile;
    email: string;
    proxy: Proxy;
    plan: Plan;
    effectivePlatformFeatures: EffectivePlatformFeatures;
    createdAt: string;
    isPaying: boolean;
};

/**
 * GetPrivateUserDataResponse
 */
export type GetPrivateUserDataResponse = {
    data: UserPrivateInfo;
};

/**
 * UsageCycle
 */
export type UsageCycle = {
    startAt: string;
    endAt: string;
};

/**
 * PriceTiers
 */
export type PriceTiers = {
    quantityAbove: number;
    discountPercent: number;
    tierQuantity: number;
    unitPriceUsd: number;
    priceUsd: number;
};

/**
 * UsageItem
 */
export type UsageItem = {
    quantity: number;
    baseAmountUsd: number;
    baseUnitPriceUsd: number;
    amountAfterVolumeDiscountUsd: number;
    priceTiers: Array<PriceTiers>;
};

/**
 * MonthlyServiceUsage
 */
export type MonthlyServiceUsage = {
    USAGE_ITEM: UsageItem;
};

/**
 * ServiceUsage
 */
export type ServiceUsage = {
    SERVICE_USAGE_ITEM: UsageItem;
};

/**
 * DailyServiceUsages
 */
export type DailyServiceUsages = {
    date: string;
    serviceUsage: ServiceUsage;
    totalUsageCreditsUsd: number;
};

/**
 * MonthlyUsage
 */
export type MonthlyUsage = {
    usageCycle: UsageCycle;
    monthlyServiceUsage: MonthlyServiceUsage;
    dailyServiceUsages: Array<DailyServiceUsages>;
    totalUsageCreditsUsdBeforeVolumeDiscount: number;
    totalUsageCreditsUsdAfterVolumeDiscount: number;
};

/**
 * GetMonthlyUsageResponse
 */
export type GetMonthlyUsageResponse = {
    data: MonthlyUsage;
};

/**
 * MonthlyUsageCycle
 */
export type MonthlyUsageCycle = {
    startAt: string;
    endAt: string;
};

/**
 * Limits
 */
export type Limits = {
    maxMonthlyUsageUsd: number;
    maxMonthlyActorComputeUnits: number;
    maxMonthlyExternalDataTransferGbytes: number;
    maxMonthlyProxySerps: number;
    maxMonthlyResidentialProxyGbytes: number;
    maxActorMemoryGbytes: number;
    maxActorCount: number;
    maxActorTaskCount: number;
    maxConcurrentActorJobs: number;
    maxTeamAccountSeatCount: number;
    dataRetentionDays: number;
};

/**
 * Current
 */
export type Current = {
    monthlyUsageUsd: number;
    monthlyActorComputeUnits: number;
    monthlyExternalDataTransferGbytes: number;
    monthlyProxySerps: number;
    monthlyResidentialProxyGbytes: number;
    actorMemoryGbytes: number;
    actorCount: number;
    actorTaskCount: number;
    activeActorJobCount: number;
    teamAccountSeatCount: number;
};

/**
 * AccountLimits
 */
export type AccountLimits = {
    monthlyUsageCycle: MonthlyUsageCycle;
    limits: Limits;
    current: Current;
};

/**
 * GetLimitsResponse
 */
export type GetLimitsResponse = {
    data: AccountLimits;
};

/**
 * UpdateLimitsRequest
 */
export type UpdateLimitsRequest = {
    /**
     * If your platform usage in the billing period exceeds the prepaid usage, you will be charged extra.
     * Setting this property you can update your hard limit on monthly platform usage to prevent accidental overage or to limit the extra charges
     *
     */
    maxMonthlyUsageUsd?: number;
    /**
     * Apify securely stores your ten most recent Actor runs indefinitely, ensuring they are always accessible.
     * Unnamed storages and other Actor runs are automatically deleted after the retention period.
     * If you're subscribed, you can change it to keep data for longer or to limit your usage. [Lear more](https://docs.apify.com/platform/storage/usage#data-retention)
     *
     */
    dataRetentionDays?: number;
};

export type ActsGetData = {
    body?: never;
    path?: never;
    query?: {
        /**
         * If `true` or `1` then the returned list only contains Actors owned by the user. The default value is `false`.
         *
         */
        my?: boolean;
        /**
         * Number of records that should be skipped at the start. The default value
         * is `0`.
         *
         */
        offset?: number;
        /**
         * Maximum number of records to return. The default value as well as the
         * maximum is `1000`.
         *
         */
        limit?: number;
        /**
         * If `true` or `1` then the objects are sorted by the `createdAt` field in
         * descending order. By default, they are sorted in ascending order.
         *
         */
        desc?: boolean;
        /**
         * Field to sort the records by. The default is `createdAt`. You can also use `lastRunStartedAt` to sort
         * by the most recently ran Actors.
         *
         */
        sortBy?: 'createdAt' | 'lastRunStartedAt';
    };
    url: '/v2/acts';
};

export type ActsGetResponses = {
    200: GetListOfActorsResponse;
};

export type ActsGetResponse = ActsGetResponses[keyof ActsGetResponses];

export type ActsPostData = {
    body: CreateActorRequest;
    path?: never;
    query?: never;
    url: '/v2/acts';
};

export type ActsPostResponses = {
    201: CreateActorResponse;
};

export type ActsPostResponse = ActsPostResponses[keyof ActsPostResponses];

export type ActDeleteData = {
    body?: never;
    path: {
        /**
         * Actor ID or a tilde-separated owner's username and Actor name.
         */
        actorId: string;
    };
    query?: never;
    url: '/v2/acts/{actorId}';
};

export type ActDeleteResponses = {
    204: {
        [key: string]: unknown;
    };
};

export type ActDeleteResponse = ActDeleteResponses[keyof ActDeleteResponses];

export type ActGetData = {
    body?: never;
    path: {
        /**
         * Actor ID or a tilde-separated owner's username and Actor name.
         */
        actorId: string;
    };
    query?: never;
    url: '/v2/acts/{actorId}';
};

export type ActGetResponses = {
    200: GetActorResponse;
};

export type ActGetResponse = ActGetResponses[keyof ActGetResponses];

export type ActPutData = {
    body: UpdateActorRequest;
    path: {
        /**
         * Actor ID or a tilde-separated owner's username and Actor name.
         */
        actorId: string;
    };
    query?: never;
    url: '/v2/acts/{actorId}';
};

export type ActPutResponses = {
    200: UpdateActorResponse;
};

export type ActPutResponse = ActPutResponses[keyof ActPutResponses];

export type ActVersionsGetData = {
    body?: never;
    path: {
        /**
         * Actor ID or a tilde-separated owner's username and Actor name.
         */
        actorId: string;
    };
    query?: never;
    url: '/v2/acts/{actorId}/versions';
};

export type ActVersionsGetResponses = {
    200: GetVersionListResponse;
};

export type ActVersionsGetResponse = ActVersionsGetResponses[keyof ActVersionsGetResponses];

export type ActVersionsPostData = {
    body: CreateOrUpdateVersionRequest;
    path: {
        /**
         * Actor ID or a tilde-separated owner's username and Actor name.
         */
        actorId: string;
    };
    query?: never;
    url: '/v2/acts/{actorId}/versions';
};

export type ActVersionsPostResponses = {
    201: GetVersionResponse;
};

export type ActVersionsPostResponse = ActVersionsPostResponses[keyof ActVersionsPostResponses];

export type ActVersionDeleteData = {
    body?: never;
    path: {
        /**
         * Actor ID or a tilde-separated owner's username and Actor name.
         */
        actorId: string;
        /**
         * Actor major and minor version of the Actor.
         */
        versionNumber: string;
    };
    query?: never;
    url: '/v2/acts/{actorId}/versions/{versionNumber}';
};

export type ActVersionDeleteResponses = {
    204: {
        [key: string]: unknown;
    };
};

export type ActVersionDeleteResponse = ActVersionDeleteResponses[keyof ActVersionDeleteResponses];

export type ActVersionGetData = {
    body?: never;
    path: {
        /**
         * Actor ID or a tilde-separated owner's username and Actor name.
         */
        actorId: string;
        /**
         * Actor major and minor version of the Actor.
         */
        versionNumber: string;
    };
    query?: never;
    url: '/v2/acts/{actorId}/versions/{versionNumber}';
};

export type ActVersionGetResponses = {
    200: GetVersionResponse;
};

export type ActVersionGetResponse = ActVersionGetResponses[keyof ActVersionGetResponses];

export type ActVersionPutData = {
    body: CreateOrUpdateVersionRequest;
    path: {
        /**
         * Actor ID or a tilde-separated owner's username and Actor name.
         */
        actorId: string;
        /**
         * Actor major and minor version of the Actor.
         */
        versionNumber: string;
    };
    query?: never;
    url: '/v2/acts/{actorId}/versions/{versionNumber}';
};

export type ActVersionPutResponses = {
    200: GetVersionResponse;
};

export type ActVersionPutResponse = ActVersionPutResponses[keyof ActVersionPutResponses];

export type ActVersionEnvVarsGetData = {
    body?: never;
    path: {
        /**
         * Actor ID or a tilde-separated owner's username and Actor name.
         */
        actorId: string;
        /**
         * Actor version
         */
        versionNumber: string;
    };
    query?: never;
    url: '/v2/acts/{actorId}/versions/{versionNumber}/env-vars';
};

export type ActVersionEnvVarsGetResponses = {
    200: GetEnvVarListResponse;
};

export type ActVersionEnvVarsGetResponse = ActVersionEnvVarsGetResponses[keyof ActVersionEnvVarsGetResponses];

export type ActVersionEnvVarsPostData = {
    body: CreateOrUpdateEnvVarRequest;
    path: {
        /**
         * Actor ID or a tilde-separated owner's username and Actor name.
         */
        actorId: string;
        /**
         * Actor version
         */
        versionNumber: string;
    };
    query?: never;
    url: '/v2/acts/{actorId}/versions/{versionNumber}/env-vars';
};

export type ActVersionEnvVarsPostResponses = {
    201: GetEnvVarResponse;
};

export type ActVersionEnvVarsPostResponse = ActVersionEnvVarsPostResponses[keyof ActVersionEnvVarsPostResponses];

export type ActVersionEnvVarDeleteData = {
    body?: never;
    path: {
        /**
         * Actor ID or a tilde-separated owner's username and Actor name.
         */
        actorId: string;
        /**
         * Actor version
         */
        versionNumber: string;
        /**
         * The name of the environment variable
         */
        envVarName: string;
    };
    query?: never;
    url: '/v2/acts/{actorId}/versions/{versionNumber}/env-vars/{envVarName}';
};

export type ActVersionEnvVarDeleteResponses = {
    204: {
        [key: string]: unknown;
    };
};

export type ActVersionEnvVarDeleteResponse = ActVersionEnvVarDeleteResponses[keyof ActVersionEnvVarDeleteResponses];

export type ActVersionEnvVarGetData = {
    body?: never;
    path: {
        /**
         * Actor ID or a tilde-separated owner's username and Actor name.
         */
        actorId: string;
        /**
         * Actor version
         */
        versionNumber: string;
        /**
         * The name of the environment variable
         */
        envVarName: string;
    };
    query?: never;
    url: '/v2/acts/{actorId}/versions/{versionNumber}/env-vars/{envVarName}';
};

export type ActVersionEnvVarGetResponses = {
    200: GetEnvVarResponse;
};

export type ActVersionEnvVarGetResponse = ActVersionEnvVarGetResponses[keyof ActVersionEnvVarGetResponses];

export type ActVersionEnvVarPutData = {
    body: CreateOrUpdateEnvVarRequest;
    path: {
        /**
         * Actor ID or a tilde-separated owner's username and Actor name.
         */
        actorId: string;
        /**
         * Actor version
         */
        versionNumber: string;
        /**
         * The name of the environment variable
         */
        envVarName: string;
    };
    query?: never;
    url: '/v2/acts/{actorId}/versions/{versionNumber}/env-vars/{envVarName}';
};

export type ActVersionEnvVarPutResponses = {
    200: GetEnvVarResponse;
};

export type ActVersionEnvVarPutResponse = ActVersionEnvVarPutResponses[keyof ActVersionEnvVarPutResponses];

export type ActWebhooksGetData = {
    body?: never;
    path: {
        /**
         * Actor ID or a tilde-separated owner's username and Actor name.
         */
        actorId: string;
    };
    query?: {
        /**
         * Number of array elements that should be skipped at the start. The
         * default value is `0`.
         *
         */
        offset?: number;
        /**
         * Maximum number of array elements to return. The default value as well as
         * the maximum is `1000`.
         *
         */
        limit?: number;
        /**
         * If `true` or `1` then the objects are sorted by the `createdAt` field in
         * descending order. By default, they are sorted in ascending order.
         *
         */
        desc?: boolean;
    };
    url: '/v2/acts/{actorId}/webhooks';
};

export type ActWebhooksGetResponses = {
    200: GetListOfWebhooksResponse;
};

export type ActWebhooksGetResponse = ActWebhooksGetResponses[keyof ActWebhooksGetResponses];

export type ActBuildsGetData = {
    body?: never;
    path: {
        /**
         * Actor ID or a tilde-separated owner's username and Actor name.
         */
        actorId: string;
    };
    query?: {
        /**
         * Number of records that should be skipped at the start. The default value is `0`.
         *
         */
        offset?: number;
        /**
         * Maximum number of records to return. The default value as well as the maximum is `1000`.
         *
         */
        limit?: number;
        /**
         * If `true` or `1` then the objects are sorted by the `startedAt` field in
         * descending order. By default, they are sorted in ascending order.
         *
         */
        desc?: boolean;
    };
    url: '/v2/acts/{actorId}/builds';
};

export type ActBuildsGetResponses = {
    200: GetBuildListResponse;
};

export type ActBuildsGetResponse = ActBuildsGetResponses[keyof ActBuildsGetResponses];

export type ActBuildsPostData = {
    body?: never;
    path: {
        /**
         * Actor ID or a tilde-separated owner's username and Actor name.
         */
        actorId: string;
    };
    query: {
        /**
         * Actor version number to be built.
         */
        version: string;
        /**
         * If `true` or `1`, the system will use a cache to speed up the build
         * process. By default, cache is not used.
         *
         */
        useCache?: boolean;
        /**
         * If `true` or `1` then the Actor is built with beta versions of Apify NPM
         * packages. By default, the build uses `latest` packages.
         *
         */
        betaPackages?: boolean;
        /**
         * Tag to be applied to the build on success. By default, the tag is taken
         * from Actor version's `buildTag` property.
         *
         */
        tag?: string;
        /**
         * The maximum number of seconds the server waits for the build to finish.
         * By default it is `0`, the maximum value is `60`. <!-- MAX_ACTOR_JOB_ASYNC_WAIT_SECS -->
         * If the build finishes in time then the returned build object will have a terminal status (e.g. `SUCCEEDED`),
         * otherwise it will have a transitional status (e.g. `RUNNING`).
         *
         */
        waitForFinish?: number;
    };
    url: '/v2/acts/{actorId}/builds';
};

export type ActBuildsPostResponses = {
    201: BuildActorResponse;
};

export type ActBuildsPostResponse = ActBuildsPostResponses[keyof ActBuildsPostResponses];

export type ActBuildDefaultGetData = {
    body?: never;
    path: {
        /**
         * Actor ID or a tilde-separated owner's username and Actor name.
         */
        actorId: string;
    };
    query?: {
        /**
         * The maximum number of seconds the server waits for the build to finish.
         * If the build finishes within this time, the returned build object will have a terminal status (e.g. `SUCCEEDED`),
         * otherwise it will have a transitional status (e.g. `RUNNING`).
         *
         * By default it is `0`, the maximum value is `60`. <!-- MAX_ACTOR_JOB_ASYNC_WAIT_SECS -->
         *
         */
        waitForFinish?: number;
    };
    url: '/v2/acts/{actorId}/builds/default';
};

export type ActBuildDefaultGetResponses = {
    200: GetBuildResponse;
};

export type ActBuildDefaultGetResponse = ActBuildDefaultGetResponses[keyof ActBuildDefaultGetResponses];

export type ActOpenapiJsonGetData = {
    body?: never;
    path: {
        /**
         * Actor ID or a tilde-separated owner's username and Actor name.
         */
        actorId: string;
        /**
         * ID of the build you want to get, found in the build's `Info` tab. Pass `default` for default Actor build.
         */
        buildId: string;
    };
    query?: never;
    url: '/v2/acts/{actorId}/builds/{buildId}/openapi.json';
};

export type ActOpenapiJsonGetResponses = {
    200: GetOpenApiResponse;
};

export type ActOpenapiJsonGetResponse = ActOpenapiJsonGetResponses[keyof ActOpenapiJsonGetResponses];

export type ActBuildGetData = {
    body?: never;
    path: {
        /**
         * Actor ID or a tilde-separated owner's username and Actor name.
         */
        actorId: string;
        /**
         * ID of the build you want to get, found in the build's `Info` tab.
         */
        buildId: string;
    };
    query?: {
        /**
         * The maximum number of seconds the server waits for the build to finish.
         * By default it is `0`, the maximum value is `60`. <!-- MAX_ACTOR_JOB_ASYNC_WAIT_SECS -->
         * If the build finishes in time then the returned build object will have a terminal status (e.g. `SUCCEEDED`),
         * otherwise it will have a transitional status (e.g. `RUNNING`).
         *
         */
        waitForFinish?: number;
    };
    url: '/v2/acts/{actorId}/builds/{buildId}';
};

export type ActBuildGetResponses = {
    200: GetBuildResponse;
};

export type ActBuildGetResponse = ActBuildGetResponses[keyof ActBuildGetResponses];

export type ActBuildAbortPostData = {
    body?: never;
    path: {
        /**
         * Actor ID or a tilde-separated owner's username and Actor name.
         */
        actorId: string;
        /**
         * Build ID.
         */
        buildId: string;
    };
    query?: never;
    url: '/v2/acts/{actorId}/builds/{buildId}/abort';
};

export type ActBuildAbortPostResponses = {
    200: PostAbortBuildResponse;
};

export type ActBuildAbortPostResponse = ActBuildAbortPostResponses[keyof ActBuildAbortPostResponses];

export type ActRunsGetData = {
    body?: never;
    path: {
        /**
         * Actor ID or a tilde-separated owner's username and Actor name.
         */
        actorId: string;
    };
    query?: {
        /**
         * Number of array elements that should be skipped at the start. The
         * default value is `0`.
         *
         */
        offset?: number;
        /**
         * Maximum number of array elements to return. The default value as well as
         * the maximum is `1000`.
         *
         */
        limit?: number;
        /**
         * If `true` or `1` then the objects are sorted by the `startedAt` field in
         * descending order. By default, they are sorted in ascending order.
         *
         */
        desc?: boolean;
        /**
         * Return only runs with the provided status ([available
         * statuses](https://docs.apify.com/platform/actors/running/runs-and-builds#lifecycle))
         *
         */
        status?: string;
    };
    url: '/v2/acts/{actorId}/runs';
};

export type ActRunsGetResponses = {
    200: GetUserRunsListResponse;
};

export type ActRunsGetResponse = ActRunsGetResponses[keyof ActRunsGetResponses];

export type ActRunsPostData = {
    body: {
        [key: string]: unknown;
    };
    path: {
        /**
         * Actor ID or a tilde-separated owner's username and Actor name.
         */
        actorId: string;
    };
    query?: {
        /**
         * Optional timeout for the run, in seconds. By default, the run uses a
         * timeout specified in the default run configuration for the Actor.
         *
         */
        timeout?: number;
        /**
         * Memory limit for the run, in megabytes. The amount of memory can be set
         * to a power of 2 with a minimum of 128. By default, the run uses a memory
         * limit specified in the default run configuration for the Actor.
         *
         */
        memory?: number;
        /**
         * The maximum number of items that the Actor run should return. This is
         * useful for pay-per-result Actors, as it allows you to limit the number
         * of results that will be charged to your subscription. You can access the
         * maximum number of items in your Actor by using the
         * `ACTOR_MAX_PAID_DATASET_ITEMS` environment variable.
         *
         */
        maxItems?: number;
        /**
         * Specifies the maximum cost of the Actor run. This parameter is
         * useful for pay-per-event Actors, as it allows you to limit the amount
         * charged to your subscription. You can access the
         * maximum cost in your Actor by using the
         * `ACTOR_MAX_TOTAL_CHARGE_USD` environment variable.
         *
         */
        maxTotalChargeUsd?: number;
        /**
         * Specifies the Actor build to run. It can be either a build tag or build
         * number. By default, the run uses the build specified in the default run
         * configuration for the Actor (typically `latest`).
         *
         */
        build?: string;
        /**
         * The maximum number of seconds the server waits for the run to finish. By
         * default, it is `0`, the maximum value is `60`. <!-- MAX_ACTOR_JOB_ASYNC_WAIT_SECS -->
         * If the run finishes in time then the returned run object will have a terminal status (e.g. `SUCCEEDED`),
         * otherwise it will have a transitional status (e.g. `RUNNING`).
         *
         */
        waitForFinish?: number;
        /**
         * Specifies optional webhooks associated with the Actor run, which can be
         * used to receive a notification e.g. when the Actor finished or failed. The value is a Base64-encoded
         * JSON array of objects defining the webhooks. For more information, see
         * [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).
         *
         */
        webhooks?: string;
    };
    url: '/v2/acts/{actorId}/runs';
};

export type ActRunsPostResponses = {
    201: RunResponse;
};

export type ActRunsPostResponse = ActRunsPostResponses[keyof ActRunsPostResponses];

export type ActRunSyncGetData = {
    body?: never;
    path: {
        /**
         * Actor ID or a tilde-separated owner's username and Actor name.
         */
        actorId: string;
    };
    query?: {
        /**
         * Key of the record from run's default key-value store to be returned
         * in the response. By default, it is `OUTPUT`.
         *
         */
        outputRecordKey?: string;
        /**
         * Optional timeout for the run, in seconds. By default, the run uses a
         * timeout specified in the default run configuration for the Actor.
         *
         */
        timeout?: number;
        /**
         * Memory limit for the run, in megabytes. The amount of memory can be set
         * to a power of 2 with a minimum of 128. By default, the run uses a memory
         * limit specified in the default run configuration for the Actor.
         *
         */
        memory?: number;
        /**
         * The maximum number of items that the Actor run should return. This is
         * useful for pay-per-result Actors, as it allows you to limit the number
         * of results that will be charged to your subscription. You can access the
         * maximum number of items in your Actor by using the
         * `ACTOR_MAX_PAID_DATASET_ITEMS` environment variable.
         *
         */
        maxItems?: number;
        /**
         * Specifies the maximum cost of the Actor run. This parameter is
         * useful for pay-per-event Actors, as it allows you to limit the amount
         * charged to your subscription. You can access the
         * maximum cost in your Actor by using the
         * `ACTOR_MAX_TOTAL_CHARGE_USD` environment variable.
         *
         */
        maxTotalChargeUsd?: number;
        /**
         * Specifies the Actor build to run. It can be either a build tag or build
         * number. By default, the run uses the build specified in the default run
         * configuration for the Actor (typically `latest`).
         *
         */
        build?: string;
        /**
         * Specifies optional webhooks associated with the Actor run, which can be
         * used to receive a notification
         * e.g. when the Actor finished or failed. The value is a Base64-encoded
         * JSON array of objects defining the webhooks. For more information, see
         * [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).
         *
         */
        webhooks?: string;
    };
    url: '/v2/acts/{actorId}/run-sync';
};

export type ActRunSyncGetErrors = {
    400: ErrorResponse;
    408: ErrorResponse;
};

export type ActRunSyncGetError = ActRunSyncGetErrors[keyof ActRunSyncGetErrors];

export type ActRunSyncGetResponses = {
    201: {
        [key: string]: unknown;
    };
};

export type ActRunSyncGetResponse = ActRunSyncGetResponses[keyof ActRunSyncGetResponses];

export type ActRunSyncPostData = {
    body: {
        [key: string]: unknown;
    };
    path: {
        /**
         * Actor ID or a tilde-separated owner's username and Actor name.
         */
        actorId: string;
    };
    query?: {
        /**
         * Key of the record from run's default key-value store to be returned
         * in the response. By default, it is `OUTPUT`.
         *
         */
        outputRecordKey?: string;
        /**
         * Optional timeout for the run, in seconds. By default, the run uses a
         * timeout specified in the default run configuration for the Actor.
         *
         */
        timeout?: number;
        /**
         * Memory limit for the run, in megabytes. The amount of memory can be set
         * to a power of 2 with a minimum of 128. By default, the run uses a memory
         * limit specified in the default run configuration for the Actor.
         *
         */
        memory?: number;
        /**
         * The maximum number of items that the Actor run should return. This is
         * useful for pay-per-result Actors, as it allows you to limit the number
         * of results that will be charged to your subscription. You can access the
         * maximum number of items in your Actor by using the
         * `ACTOR_MAX_PAID_DATASET_ITEMS` environment variable.
         *
         */
        maxItems?: number;
        /**
         * Specifies the maximum cost of the Actor run. This parameter is
         * useful for pay-per-event Actors, as it allows you to limit the amount
         * charged to your subscription. You can access the
         * maximum cost in your Actor by using the
         * `ACTOR_MAX_TOTAL_CHARGE_USD` environment variable.
         *
         */
        maxTotalChargeUsd?: number;
        /**
         * Specifies the Actor build to run. It can be either a build tag or build
         * number. By default, the run uses the build specified in the default run
         * configuration for the Actor (typically `latest`).
         *
         */
        build?: string;
        /**
         * Specifies optional webhooks associated with the Actor run, which can be
         * used to receive a notification
         * e.g. when the Actor finished or failed. The value is a Base64-encoded
         * JSON array of objects defining the webhooks. For more information, see
         * [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).
         *
         */
        webhooks?: string;
    };
    url: '/v2/acts/{actorId}/run-sync';
};

export type ActRunSyncPostErrors = {
    400: ErrorResponse;
    408: ErrorResponse;
};

export type ActRunSyncPostError = ActRunSyncPostErrors[keyof ActRunSyncPostErrors];

export type ActRunSyncPostResponses = {
    201: {
        [key: string]: unknown;
    };
};

export type ActRunSyncPostResponse = ActRunSyncPostResponses[keyof ActRunSyncPostResponses];

export type ActRunSyncGetDatasetItemsGetData = {
    body?: never;
    path: {
        /**
         * Actor ID or a tilde-separated owner's username and Actor name.
         */
        actorId: string;
    };
    query?: {
        /**
         * Optional timeout for the run, in seconds. By default, the run uses a
         * timeout specified in the default run configuration for the Actor.
         *
         */
        timeout?: number;
        /**
         * Memory limit for the run, in megabytes. The amount of memory can be set
         * to a power of 2 with a minimum of 128. By default, the run uses a memory
         * limit specified in the default run configuration for the Actor.
         *
         */
        memory?: number;
        /**
         * The maximum number of items that the Actor run should return. This is
         * useful for pay-per-result Actors, as it allows you to limit the number
         * of results that will be charged to your subscription. You can access the
         * maximum number of items in your Actor by using the
         * `ACTOR_MAX_PAID_DATASET_ITEMS` environment variable.
         *
         */
        maxItems?: number;
        /**
         * Specifies the maximum cost of the Actor run. This parameter is
         * useful for pay-per-event Actors, as it allows you to limit the amount
         * charged to your subscription. You can access the
         * maximum cost in your Actor by using the
         * `ACTOR_MAX_TOTAL_CHARGE_USD` environment variable.
         *
         */
        maxTotalChargeUsd?: number;
        /**
         * Specifies the Actor build to run. It can be either a build tag or build
         * number. By default, the run uses the build specified in the default run
         * configuration for the Actor (typically `latest`).
         *
         */
        build?: string;
        /**
         * Specifies optional webhooks associated with the Actor run, which can be
         * used to receive a notification
         * e.g. when the Actor finished or failed. The value is a Base64-encoded
         * JSON array of objects defining the webhooks. For more information, see
         * [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).
         *
         */
        webhooks?: string;
        /**
         * Format of the results, possible values are: `json`, `jsonl`, `csv`,
         * `html`, `xlsx`, `xml` and `rss`. The default value is `json`.
         *
         */
        format?: string;
        /**
         * If `true` or `1` then the API endpoint returns only non-empty items and
         * skips hidden fields (i.e. fields starting with the # character).
         * The `clean` parameter is just a shortcut for `skipHidden=true` and `skipEmpty=true` parameters.
         * Note that since some objects might be skipped from the output, that the
         * result might contain less items than the `limit` value.
         *
         */
        clean?: boolean;
        /**
         * Number of items that should be skipped at the start. The default value is `0`.
         *
         */
        offset?: number;
        /**
         * Maximum number of items to return. By default there is no limit.
         */
        limit?: number;
        /**
         * A comma-separated list of fields which should be picked from the items,
         * only these fields will remain in the resulting record objects.
         * Note that the fields in the outputted items are sorted the same way as
         * they are specified in the `fields` query parameter.
         * You can use this feature to effectively fix the output format.
         *
         */
        fields?: string;
        /**
         * A comma-separated list of fields which should be omitted from the items.
         */
        omit?: string;
        /**
         * A comma-separated list of fields which should be unwound, in order which
         * they should be processed. Each field should be either an array or an object.
         * If the field is an array then every element of
         * the array will become a separate record and merged with parent object.
         * If the unwound field is an object then it is merged with the parent object
         * If the unwound field is missing or its value is neither an array nor an
         * object and therefore cannot be merged with a parent object then the item
         * gets preserved as it is.
         * Note that the unwound items ignore the `desc` parameter.
         *
         */
        unwind?: string;
        /**
         * A comma-separated list of fields which should transform nested objects into flat structures.
         * For example, with `flatten="foo"` the object `{"foo":{"bar": "hello"}}` is turned into `{"foo.bar": "hello"}`.
         * The original object with properties is replaced with the flattened object.
         *
         */
        flatten?: string;
        /**
         * By default, results are returned in the same order as they were stored.
         * To reverse the order, set this parameter to `true` or `1`.
         *
         */
        desc?: boolean;
        /**
         * If `true` or `1` then the response will define the `Content-Disposition:
         * attachment` header, forcing a web browser to download the file rather
         * than to display it. By default this header is not present.
         *
         */
        attachment?: boolean;
        /**
         * A delimiter character for CSV files, only used if `format=csv`. You
         * might need to URL-encode the character (e.g. use `%09` for tab or `%3B`
         * for semicolon). The default delimiter is a simple comma (`,`).
         *
         */
        delimiter?: string;
        /**
         * All text responses are encoded in UTF-8 encoding. By default, the `format=csv` files are prefixed with
         * the UTF-8 Byte Order Mark (BOM), while `json`, `jsonl`, `xml`, `html` and `rss` files are not.
         * If you want to override this default behavior, specify `bom=1` query
         * parameter to include the BOM or `bom=0` to skip it.
         *
         */
        bom?: boolean;
        /**
         * Overrides default root element name of `xml` output. By default the root
         * element is `items`.
         *
         */
        xmlRoot?: string;
        /**
         * Overrides default element name that wraps each page or page function
         * result object in `xml` output. By default the element name is `item`.
         *
         */
        xmlRow?: string;
        /**
         * If `true` or `1` then header row in the `csv` format is skipped.
         */
        skipHeaderRow?: boolean;
        /**
         * If `true` or `1` then hidden fields are skipped from the output,
         * i.e. fields starting with the `#` character.
         *
         */
        skipHidden?: boolean;
        /**
         * If `true` or `1` then empty items are skipped from the output.
         *
         * Note that if used, the results might contain less items than the limit
         * value.
         *
         */
        skipEmpty?: boolean;
        /**
         * If `true` or `1` then, the endpoint applies the `fields=url,pageFunctionResult,errorInfo`
         * and `unwind=pageFunctionResult` query parameters. This feature is used
         * to emulate simplified results provided by the
         * legacy Apify Crawler product and it's not recommended to use it in new integrations.
         *
         */
        simplified?: boolean;
        /**
         * If `true` or `1` then, the all the items with errorInfo property will be
         * skipped from the output.
         * This feature is here to emulate functionality of API version 1 used for
         * the legacy Apify Crawler product and it's not recommended to use it in
         * new integrations.
         *
         */
        skipFailedPages?: boolean;
    };
    url: '/v2/acts/{actorId}/run-sync-get-dataset-items';
};

export type ActRunSyncGetDatasetItemsGetErrors = {
    400: ErrorResponse;
    408: ErrorResponse;
};

export type ActRunSyncGetDatasetItemsGetError = ActRunSyncGetDatasetItemsGetErrors[keyof ActRunSyncGetDatasetItemsGetErrors];

export type ActRunSyncGetDatasetItemsGetResponses = {
    201: {
        [key: string]: unknown;
    };
};

export type ActRunSyncGetDatasetItemsGetResponse = ActRunSyncGetDatasetItemsGetResponses[keyof ActRunSyncGetDatasetItemsGetResponses];

export type ActRunSyncGetDatasetItemsPostData = {
    body: {
        [key: string]: unknown;
    };
    path: {
        /**
         * Actor ID or a tilde-separated owner's username and Actor name.
         */
        actorId: string;
    };
    query?: {
        /**
         * Optional timeout for the run, in seconds. By default, the run uses a
         * timeout specified in the default run configuration for the Actor.
         *
         */
        timeout?: number;
        /**
         * Memory limit for the run, in megabytes. The amount of memory can be set
         * to a power of 2 with a minimum of 128. By default, the run uses a memory
         * limit specified in the default run configuration for the Actor.
         *
         */
        memory?: number;
        /**
         * The maximum number of items that the Actor run should return. This is
         * useful for pay-per-result Actors, as it allows you to limit the number
         * of results that will be charged to your subscription. You can access the
         * maximum number of items in your Actor by using the
         * `ACTOR_MAX_PAID_DATASET_ITEMS` environment variable.
         *
         */
        maxItems?: number;
        /**
         * Specifies the maximum cost of the Actor run. This parameter is
         * useful for pay-per-event Actors, as it allows you to limit the amount
         * charged to your subscription. You can access the
         * maximum cost in your Actor by using the
         * `ACTOR_MAX_TOTAL_CHARGE_USD` environment variable.
         *
         */
        maxTotalChargeUsd?: number;
        /**
         * Specifies the Actor build to run. It can be either a build tag or build
         * number. By default, the run uses the build specified in the default run
         * configuration for the Actor (typically `latest`).
         *
         */
        build?: string;
        /**
         * Specifies optional webhooks associated with the Actor run, which can be
         * used to receive a notification
         * e.g. when the Actor finished or failed. The value is a Base64-encoded
         * JSON array of objects defining the webhooks. For more information, see
         * [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).
         *
         */
        webhooks?: string;
        /**
         * Format of the results, possible values are: `json`, `jsonl`, `csv`,
         * `html`, `xlsx`, `xml` and `rss`. The default value is `json`.
         *
         */
        format?: string;
        /**
         * If `true` or `1` then the API endpoint returns only non-empty items and
         * skips hidden fields (i.e. fields starting with the # character).
         * The `clean` parameter is just a shortcut for `skipHidden=true` and
         * `skipEmpty=true` parameters.
         * Note that since some objects might be skipped from the output, that the
         * result might contain less items than the `limit` value.
         *
         */
        clean?: boolean;
        /**
         * Number of items that should be skipped at the start. The default value
         * is `0`.
         *
         */
        offset?: number;
        /**
         * Maximum number of items to return. By default there is no limit.
         */
        limit?: number;
        /**
         * A comma-separated list of fields which should be picked from the items,
         * only these fields will remain in the resulting record objects.
         * Note that the fields in the outputted items are sorted the same way as
         * they are specified in the `fields` query parameter.
         * You can use this feature to effectively fix the output format.
         *
         */
        fields?: string;
        /**
         * A comma-separated list of fields which should be omitted from the items.
         */
        omit?: string;
        /**
         * A comma-separated list of fields which should be unwound, in order which
         * they should be processed. Each field should be either an array or an object.
         * If the field is an array then every element of
         * the array will become a separate record and merged with parent object.
         * If the unwound field is an object then it is merged with the parent object.
         * If the unwound field is missing or its value is neither an array nor an
         * object and therefore cannot be merged with a parent object then the item
         * gets preserved as it is.
         * Note that the unwound items ignore the `desc` parameter.
         *
         */
        unwind?: string;
        /**
         * A comma-separated list of fields which should transform nested objects
         * into flat structures.
         * For example, with `flatten="foo"` the object `{"foo":{"bar": "hello"}}`
         * is turned into `{"foo.bar": "hello"}`.
         * The original object with properties is replaced with the flattened
         * object.
         *
         */
        flatten?: string;
        /**
         * By default, results are returned in the same order as they were stored.
         * To reverse the order, set this parameter to `true` or `1`.
         *
         */
        desc?: boolean;
        /**
         * If `true` or `1` then the response will define the `Content-Disposition:
         * attachment` header, forcing a web browser to download the file rather
         * than to display it. By default this header is not present.
         *
         */
        attachment?: boolean;
        /**
         * A delimiter character for CSV files, only used if `format=csv`. You
         * might need to URL-encode the character (e.g. use `%09` for tab or `%3B`
         * for semicolon). The default delimiter is a simple comma (`,`).
         *
         */
        delimiter?: string;
        /**
         * All text responses are encoded in UTF-8 encoding. By default, the
         * `format=csv` files are prefixed with
         * the UTF-8 Byte Order Mark (BOM), while `json`, `jsonl`, `xml`, `html`
         * and `rss` files are not.
         * If you want to override this default behavior, specify `bom=1` query
         * parameter to include the BOM or `bom=0` to skip it.
         *
         */
        bom?: boolean;
        /**
         * Overrides default root element name of `xml` output. By default the root
         * element is `items`.
         *
         */
        xmlRoot?: string;
        /**
         * Overrides default element name that wraps each page or page function
         * result object in `xml` output. By default the element name is `item`.
         *
         */
        xmlRow?: string;
        /**
         * If `true` or `1` then header row in the `csv` format is skipped.
         */
        skipHeaderRow?: boolean;
        /**
         * If `true` or `1` then hidden fields are skipped from the output,
         * i.e. fields starting with the `#` character.
         *
         */
        skipHidden?: boolean;
        /**
         * If `true` or `1` then empty items are skipped from the output.
         *
         * Note that if used, the results might contain less items than the limit
         * value.
         *
         */
        skipEmpty?: boolean;
        /**
         * If `true` or `1` then, the endpoint applies the
         * `fields=url,pageFunctionResult,errorInfo`
         * and `unwind=pageFunctionResult` query parameters. This feature is used
         * to emulate simplified results provided by the
         * legacy Apify Crawler product and it's not recommended to use it in new
         * integrations.
         *
         */
        simplified?: boolean;
        /**
         * If `true` or `1` then, the all the items with errorInfo property will be
         * skipped from the output.
         * This feature is here to emulate functionality of API version 1 used for
         * the legacy Apify Crawler product and it's not recommended to use it in
         * new integrations.
         *
         */
        skipFailedPages?: boolean;
    };
    url: '/v2/acts/{actorId}/run-sync-get-dataset-items';
};

export type ActRunSyncGetDatasetItemsPostErrors = {
    400: ErrorResponse;
    408: ErrorResponse;
};

export type ActRunSyncGetDatasetItemsPostError = ActRunSyncGetDatasetItemsPostErrors[keyof ActRunSyncGetDatasetItemsPostErrors];

export type ActRunSyncGetDatasetItemsPostResponses = {
    201: {
        [key: string]: unknown;
    };
};

export type ActRunSyncGetDatasetItemsPostResponse = ActRunSyncGetDatasetItemsPostResponses[keyof ActRunSyncGetDatasetItemsPostResponses];

export type ActRunResurrectPostData = {
    body?: never;
    path: {
        /**
         * Actor ID or a tilde-separated owner's username and Actor name.
         */
        actorId: string;
        /**
         * Run ID.
         */
        runId: string;
    };
    query?: {
        /**
         * Specifies the Actor build to run. It can be either a build tag or build
         * number. By default, the run uses the build specified in the run that is
         * being resurrected (typically `latest`).
         *
         */
        build?: string;
        /**
         * Optional timeout for the run, in seconds. By default, the run uses a
         * timeout specified in the run that is being resurrected.
         *
         */
        timeout?: number;
        /**
         * Memory limit for the run, in megabytes. The amount of memory can be set
         * to a power of 2 with a minimum of 128. By default, the run uses a memory
         * limit specified in the run that is being resurrected.
         *
         */
        memory?: number;
    };
    url: '/v2/acts/{actorId}/runs/{runId}/resurrect';
};

export type ActRunResurrectPostResponses = {
    200: RunResponse;
};

export type ActRunResurrectPostResponse = ActRunResurrectPostResponses[keyof ActRunResurrectPostResponses];

export type ActRunsLastGetData = {
    body?: never;
    path: {
        /**
         * Actor ID or a tilde-separated owner's username and Actor name.
         */
        actorId: string;
    };
    query?: {
        /**
         * Filter for the run status.
         */
        status?: string;
    };
    url: '/v2/acts/{actorId}/runs/last';
};

export type ActRunsLastGetResponses = {
    200: RunResponse;
};

export type ActRunsLastGetResponse = ActRunsLastGetResponses[keyof ActRunsLastGetResponses];

export type ActRunGetData = {
    body?: never;
    path: {
        /**
         * Actor ID or a tilde-separated owner's username and Actor name.
         */
        actorId: string;
        /**
         * Run ID.
         */
        runId: string;
    };
    query?: {
        /**
         * The maximum number of seconds the server waits for the run to finish. By
         * default it is `0`, the maximum value is `60`. <!-- MAX_ACTOR_JOB_ASYNC_WAIT_SECS -->
         * If the run finishes in time then the returned run object will have a terminal status (e.g. `SUCCEEDED`),
         * otherwise it will have a transitional status (e.g. `RUNNING`).
         *
         */
        waitForFinish?: number;
    };
    url: '/v2/acts/{actorId}/runs/{runId}';
};

export type ActRunGetResponses = {
    200: RunResponse;
};

export type ActRunGetResponse = ActRunGetResponses[keyof ActRunGetResponses];

export type ActRunAbortPostData = {
    body?: never;
    path: {
        /**
         * Actor ID or a tilde-separated owner's username and Actor name.
         */
        actorId: string;
        /**
         * Run ID.
         */
        runId: string;
    };
    query?: {
        /**
         * If true passed, the Actor run will abort gracefully.
         * It will send `aborting` and `persistState` event into run and force-stop the run after 30 seconds.
         * It is helpful in cases where you plan to resurrect the run later.
         *
         */
        gracefully?: boolean;
    };
    url: '/v2/acts/{actorId}/runs/{runId}/abort';
};

export type ActRunAbortPostResponses = {
    200: RunResponse;
};

export type ActRunAbortPostResponse = ActRunAbortPostResponses[keyof ActRunAbortPostResponses];

export type ActRunMetamorphPostData = {
    body?: never;
    path: {
        /**
         * Actor ID or a tilde-separated owner's username and Actor name.
         */
        actorId: string;
        /**
         * Actor run ID.
         */
        runId: string;
    };
    query: {
        /**
         * ID of a target Actor that the run should be transformed into.
         */
        targetActorId: string;
        /**
         * Optional build of the target Actor.
         *
         * It can be either a build tag or build number. By default, the run uses
         * the build specified in the default run configuration for the target
         * Actor (typically `latest`).
         *
         */
        build?: string;
    };
    url: '/v2/acts/{actorId}/runs/{runId}/metamorph';
};

export type ActRunMetamorphPostResponses = {
    200: RunResponse;
};

export type ActRunMetamorphPostResponse = ActRunMetamorphPostResponses[keyof ActRunMetamorphPostResponses];

export type ActorTasksGetData = {
    body?: never;
    path?: never;
    query?: {
        /**
         * Number of records that should be skipped at the start. The default value is `0`.
         *
         */
        offset?: number;
        /**
         * Maximum number of records to return. The default value as well as the maximum is `1000`.
         *
         */
        limit?: number;
        /**
         * If `true` or `1` then the objects are sorted by the `createdAt` field in
         * descending order. By default, they are sorted in ascending order.
         *
         */
        desc?: boolean;
    };
    url: '/v2/actor-tasks';
};

export type ActorTasksGetResponses = {
    200: {
        data: PaginationResponse & {
            items: Array<TaskShort>;
        };
    };
};

export type ActorTasksGetResponse = ActorTasksGetResponses[keyof ActorTasksGetResponses];

export type ActorTasksPostData = {
    body: CreateTaskRequest & unknown;
    path?: never;
    query?: never;
    url: '/v2/actor-tasks';
};

export type ActorTasksPostResponses = {
    201: {
        data: Task;
    };
};

export type ActorTasksPostResponse = ActorTasksPostResponses[keyof ActorTasksPostResponses];

export type ActorTaskDeleteData = {
    body?: never;
    path: {
        /**
         * Task ID or a tilde-separated owner's username and task's name.
         */
        actorTaskId: string;
    };
    query?: never;
    url: '/v2/actor-tasks/{actorTaskId}';
};

export type ActorTaskDeleteResponses = {
    204: {
        [key: string]: unknown;
    };
};

export type ActorTaskDeleteResponse = ActorTaskDeleteResponses[keyof ActorTaskDeleteResponses];

export type ActorTaskGetData = {
    body?: never;
    path: {
        /**
         * Task ID or a tilde-separated owner's username and task's name.
         */
        actorTaskId: string;
    };
    query?: never;
    url: '/v2/actor-tasks/{actorTaskId}';
};

export type ActorTaskGetResponses = {
    200: {
        data: Task;
    };
};

export type ActorTaskGetResponse = ActorTaskGetResponses[keyof ActorTaskGetResponses];

export type ActorTaskPutData = {
    body: UpdateTaskRequest;
    path: {
        /**
         * Task ID or a tilde-separated owner's username and task's name.
         */
        actorTaskId: string;
    };
    query?: never;
    url: '/v2/actor-tasks/{actorTaskId}';
};

export type ActorTaskPutResponses = {
    200: {
        data: Task;
    };
};

export type ActorTaskPutResponse = ActorTaskPutResponses[keyof ActorTaskPutResponses];

export type ActorTaskInputGetData = {
    body?: never;
    path: {
        /**
         * Task ID or a tilde-separated owner's username and task's name.
         */
        actorTaskId: string;
    };
    query?: never;
    url: '/v2/actor-tasks/{actorTaskId}/input';
};

export type ActorTaskInputGetResponses = {
    200: {
        [key: string]: unknown;
    };
};

export type ActorTaskInputGetResponse = ActorTaskInputGetResponses[keyof ActorTaskInputGetResponses];

export type ActorTaskInputPutData = {
    body: {
        [key: string]: unknown;
    };
    path: {
        /**
         * Task ID or a tilde-separated owner's username and task's name.
         */
        actorTaskId: string;
    };
    query?: never;
    url: '/v2/actor-tasks/{actorTaskId}/input';
};

export type ActorTaskInputPutResponses = {
    200: {
        [key: string]: unknown;
    };
};

export type ActorTaskInputPutResponse = ActorTaskInputPutResponses[keyof ActorTaskInputPutResponses];

export type ActorTaskWebhooksGetData = {
    body?: never;
    path: {
        /**
         * Task ID or a tilde-separated owner's username and task's name.
         */
        actorTaskId: string;
    };
    query?: {
        /**
         * Number of array elements that should be skipped at the start. The
         * default value is `0`.
         *
         */
        offset?: number;
        /**
         * Maximum number of array elements to return. The default value as well as
         * the maximum is `1000`.
         *
         */
        limit?: number;
        /**
         * If `true` or `1` then the objects are sorted by the `createdAt` field in
         * descending order. By default, they are sorted in ascending order.
         *
         */
        desc?: boolean;
    };
    url: '/v2/actor-tasks/{actorTaskId}/webhooks';
};

export type ActorTaskWebhooksGetResponses = {
    200: {
        data: PaginationResponse & {
            items: Array<Webhook>;
        };
    };
};

export type ActorTaskWebhooksGetResponse = ActorTaskWebhooksGetResponses[keyof ActorTaskWebhooksGetResponses];

export type ActorTaskRunsGetData = {
    body?: never;
    path: {
        /**
         * Task ID or a tilde-separated owner's username and task's name.
         */
        actorTaskId: string;
    };
    query?: {
        /**
         * Number of array elements that should be skipped at the start. The default value is `0`.
         *
         */
        offset?: number;
        /**
         * Maximum number of array elements to return. The default value as well as the maximum is `1000`.
         *
         */
        limit?: number;
        /**
         * If `true` or `1` then the objects are sorted by the `startedAt` field in
         * descending order. By default, they are sorted in ascending order.
         *
         */
        desc?: boolean;
        /**
         * Return only runs with the provided status ([available
         * statuses](https://docs.apify.com/platform/actors/running/runs-and-builds#lifecycle))
         *
         */
        status?: string;
    };
    url: '/v2/actor-tasks/{actorTaskId}/runs';
};

export type ActorTaskRunsGetResponses = {
    200: {
        data: PaginationResponse & {
            items: Array<RunShort>;
        };
    };
};

export type ActorTaskRunsGetResponse = ActorTaskRunsGetResponses[keyof ActorTaskRunsGetResponses];

export type ActorTaskRunsPostData = {
    body: {
        [key: string]: unknown;
    };
    path: {
        /**
         * Task ID or a tilde-separated owner's username and task's name.
         */
        actorTaskId: string;
    };
    query?: {
        /**
         * Optional timeout for the run, in seconds. By default, the run uses a
         * timeout specified in the task settings.
         *
         */
        timeout?: number;
        /**
         * Memory limit for the run, in megabytes. The amount of memory can be set
         * to a power of 2 with a minimum of 128. By default, the run uses a memory
         * limit specified in the task settings.
         *
         */
        memory?: number;
        /**
         * The maximum number of items that the Actor run should return. This is
         * useful for pay-per-result Actors, as it allows you to limit the number
         * of results that will be charged to your subscription. You can access the
         * maximum number of items in your Actor by using the
         * `ACTOR_MAX_PAID_DATASET_ITEMS` environment variable.
         *
         */
        maxItems?: number;
        /**
         * Specifies the maximum cost of the Actor run. This parameter is
         * useful for pay-per-event Actors, as it allows you to limit the amount
         * charged to your subscription. You can access the
         * maximum cost in your Actor by using the
         * `ACTOR_MAX_TOTAL_CHARGE_USD` environment variable.
         *
         */
        maxTotalChargeUsd?: number;
        /**
         * Specifies the Actor build to run. It can be either a build tag or build
         * number. By default, the run uses the build specified in the task
         * settings (typically `latest`).
         *
         */
        build?: string;
        /**
         * The maximum number of seconds the server waits for the run to finish. By
         * default, it is `0`, the maximum value is `60`. <!-- MAX_ACTOR_JOB_ASYNC_WAIT_SECS -->
         * If the run finishes in time then the returned run object will have a
         * terminal status (e.g. `SUCCEEDED`),
         * otherwise it will have a transitional status (e.g. `RUNNING`).
         *
         */
        waitForFinish?: number;
        /**
         * Specifies optional webhooks associated with the Actor run, which can be
         * used to receive a notification
         * e.g. when the Actor finished or failed. The value is a Base64-encoded
         * JSON array of objects defining the webhooks.
         *
         * **Note**: if you already have a webhook set up for the Actor or task,
         * you do not have to add it again here.
         *
         * For more information, see [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).
         *
         */
        webhooks?: string;
    };
    url: '/v2/actor-tasks/{actorTaskId}/runs';
};

export type ActorTaskRunsPostResponses = {
    201: {
        data: Run;
    };
};

export type ActorTaskRunsPostResponse = ActorTaskRunsPostResponses[keyof ActorTaskRunsPostResponses];

export type ActorTaskRunSyncGetData = {
    body?: never;
    path: {
        /**
         * Task ID or a tilde-separated owner's username and task's name.
         */
        actorTaskId: string;
    };
    query?: {
        /**
         * Optional timeout for the run, in seconds. By default, the run uses a
         * timeout specified in the task settings.
         *
         */
        timeout?: number;
        /**
         * Memory limit for the run, in megabytes. The amount of memory can be set
         * to a power of 2 with a minimum of 128. By default, the run uses a memory
         * limit specified in the task settings.
         *
         */
        memory?: number;
        /**
         * The maximum number of items that the task run should return. This is
         * useful for pay-per-result tasks, as it allows you to limit the number of
         * results that will be charged to your subscription. You can access the
         * maximum number of items in your Actor by using the
         * `ACTOR_MAX_PAID_DATASET_ITEMS` environment variable.
         *
         */
        maxItems?: number;
        /**
         * Specifies the Actor build to run. It can be either a build tag or build
         * number. By default, the run uses the build specified in the task
         * settings (typically `latest`).
         *
         */
        build?: string;
        /**
         * Key of the record from run's default key-value store to be returned
         * in the response. By default, it is `OUTPUT`.
         *
         */
        outputRecordKey?: string;
        /**
         * Specifies optional webhooks associated with the Actor run, which can be
         * used to receive a notification
         * e.g. when the Actor finished or failed. The value is a Base64-encoded
         * JSON array of objects defining the webhooks. For more information, see
         * [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).
         *
         */
        webhooks?: string;
    };
    url: '/v2/actor-tasks/{actorTaskId}/run-sync';
};

export type ActorTaskRunSyncGetErrors = {
    400: ErrorResponse;
    /**
     * Request Timeout: the HTTP request exceeded the 300 second limit
     */
    408: ErrorResponse;
};

export type ActorTaskRunSyncGetError = ActorTaskRunSyncGetErrors[keyof ActorTaskRunSyncGetErrors];

export type ActorTaskRunSyncGetResponses = {
    201: {
        [key: string]: unknown;
    };
};

export type ActorTaskRunSyncGetResponse = ActorTaskRunSyncGetResponses[keyof ActorTaskRunSyncGetResponses];

export type ActorTaskRunSyncPostData = {
    body: {
        [key: string]: unknown;
    };
    path: {
        /**
         * Task ID or a tilde-separated owner's username and task's name.
         */
        actorTaskId: string;
    };
    query?: {
        /**
         * Optional timeout for the run, in seconds. By default, the run uses a
         * timeout specified in the task settings.
         *
         */
        timeout?: number;
        /**
         * Memory limit for the run, in megabytes. The amount of memory can be set
         * to a power of 2 with a minimum of 128. By default, the run uses a memory
         * limit specified in the task settings.
         *
         */
        memory?: number;
        /**
         * The maximum number of items that the task run should return. This is
         * useful for pay-per-result tasks, as it allows you to limit the number of
         * results that will be charged to your subscription. You can access the
         * maximum number of items in your Actor by using the
         * `ACTOR_MAX_PAID_DATASET_ITEMS` environment variable.
         *
         */
        maxItems?: number;
        /**
         * Specifies the maximum cost of the task run. This parameter is
         * useful for pay-per-event tasks, as it allows you to limit the amount
         * charged to your subscription. You can access the
         * maximum cost in your Actor by using the
         * `ACTOR_MAX_TOTAL_CHARGE_USD` environment variable.
         *
         */
        maxTotalChargeUsd?: number;
        /**
         * Specifies the Actor build to run. It can be either a build tag or build
         * number. By default, the run uses the build specified in the task
         * settings (typically `latest`).
         *
         */
        build?: string;
        /**
         * Key of the record from run's default key-value store to be returned
         * in the response. By default, it is `OUTPUT`.
         *
         */
        outputRecordKey?: string;
        /**
         * Specifies optional webhooks associated with the Actor run, which can be
         * used to receive a notification
         *
         * e.g. when the Actor finished or failed. The value is a Base64-encoded
         * JSON array of objects defining the webhooks. For more information, see
         *
         * [Webhooks
         * documentation](https://docs.apify.com/platform/integrations/webhooks).
         *
         */
        webhooks?: string;
    };
    url: '/v2/actor-tasks/{actorTaskId}/run-sync';
};

export type ActorTaskRunSyncPostErrors = {
    400: ErrorResponse;
};

export type ActorTaskRunSyncPostError = ActorTaskRunSyncPostErrors[keyof ActorTaskRunSyncPostErrors];

export type ActorTaskRunSyncPostResponses = {
    201: {
        [key: string]: unknown;
    };
};

export type ActorTaskRunSyncPostResponse = ActorTaskRunSyncPostResponses[keyof ActorTaskRunSyncPostResponses];

export type ActorTaskRunSyncGetDatasetItemsGetData = {
    body?: never;
    path: {
        /**
         * Task ID or a tilde-separated owner's username and task's name.
         */
        actorTaskId: string;
    };
    query?: {
        /**
         * Optional timeout for the run, in seconds. By default, the run uses a
         * timeout specified in the task settings.
         *
         */
        timeout?: number;
        /**
         * Memory limit for the run, in megabytes. The amount of memory can be set
         * to a power of 2 with a minimum of 128. By default, the run uses a memory
         * limit specified in the task settings.
         *
         */
        memory?: number;
        /**
         * The maximum number of items that the task run should return. This is
         * useful for pay-per-result tasks, as it allows you to limit the number of
         * results that will be charged to your subscription. You can access the
         * maximum number of items in your Actor by using the
         * `ACTOR_MAX_PAID_DATASET_ITEMS` environment variable.
         *
         */
        maxItems?: number;
        /**
         * Specifies the Actor build to run. It can be either a build tag or build
         * number. By default, the run uses the build specified in the task
         * settings (typically `latest`).
         *
         */
        build?: string;
        /**
         * Specifies optional webhooks associated with the Actor run, which can be
         * used to receive a notification e.g. when the Actor finished or failed. The value is a Base64-encoded
         * JSON array of objects defining the webhooks. For more information, see
         * [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).
         *
         */
        webhooks?: string;
        /**
         * Format of the results, possible values are: `json`, `jsonl`, `csv`,
         * `html`, `xlsx`, `xml` and `rss`. The default value is `json`.
         *
         */
        format?: string;
        /**
         * If `true` or `1` then the API endpoint returns only non-empty items and
         * skips hidden fields
         *
         * (i.e. fields starting with the # character).
         *
         * The `clean` parameter is just a shortcut for `skipHidden=true` and
         * `skipEmpty=true` parameters.
         *
         * Note that since some objects might be skipped from the output, that the
         * result might contain less items than the `limit` value.
         *
         */
        clean?: boolean;
        /**
         * Number of items that should be skipped at the start. The default value
         * is `0`.
         *
         */
        offset?: number;
        /**
         * Maximum number of items to return. By default there is no limit.
         */
        limit?: number;
        /**
         * A comma-separated list of fields which should be picked from the items,
         *
         * only these fields will remain in the resulting record objects.
         *
         * Note that the fields in the outputted items are sorted the same way as
         * they are specified in the `fields` query parameter.
         *
         * You can use this feature to effectively fix the output format.
         *
         */
        fields?: string;
        /**
         * A comma-separated list of fields which should be omitted from the items.
         */
        omit?: string;
        /**
         * A comma-separated list of fields which should be unwound, in order which
         * they should be processed. Each field should be either an array or an object.
         *
         * If the field is an array then every element of
         *
         * the array will become a separate record and merged with parent object.
         *
         * If the unwound field is an object then it is merged with the parent
         * object
         *
         * If the unwound field is missing or its value is neither an array nor an
         * object and therefore cannot be merged with a parent object then the item
         * gets preserved as it is.
         *
         * Note that the unwound items ignore the `desc` parameter.
         *
         */
        unwind?: string;
        /**
         * A comma-separated list of fields which should transform nested objects
         * into flat structures.
         *
         * For example, with `flatten="foo"` the object `{"foo":{"bar": "hello"}}`
         * is turned into `{"foo.bar": "hello"}`.
         *
         * The original object with properties is replaced with the flattened
         * object.
         *
         */
        flatten?: string;
        /**
         * By default, results are returned in the same order as they were stored.
         * To reverse the order, set this parameter to `true` or `1`.
         *
         */
        desc?: boolean;
        /**
         * If `true` or `1` then the response will define the `Content-Disposition:
         * attachment` header, forcing a web browser to download the file rather
         * than to display it. By default this header is not present.
         *
         */
        attachment?: boolean;
        /**
         * A delimiter character for CSV files, only used if `format=csv`. You
         * might need to URL-encode the character (e.g. use `%09` for tab or `%3B`
         * for semicolon). The default delimiter is a simple comma (`,`).
         *
         */
        delimiter?: string;
        /**
         * All text responses are encoded in UTF-8 encoding. By default, the
         * `format=csv` files are prefixed with
         *
         * the UTF-8 Byte Order Mark (BOM), while `json`, `jsonl`, `xml`, `html`
         * and `rss` files are not.
         *
         * If you want to override this default behavior, specify `bom=1` query
         * parameter to include the BOM or `bom=0` to skip it.
         *
         */
        bom?: boolean;
        /**
         * Overrides default root element name of `xml` output. By default the root
         * element is `items`.
         *
         */
        xmlRoot?: string;
        /**
         * Overrides default element name that wraps each page or page function
         * result object in `xml` output. By default the element name is `item`.
         *
         */
        xmlRow?: string;
        /**
         * If `true` or `1` then header row in the `csv` format is skipped.
         */
        skipHeaderRow?: boolean;
        /**
         * If `true` or `1` then hidden fields are skipped from the output,
         * i.e. fields starting with the `#` character.
         *
         */
        skipHidden?: boolean;
        /**
         * If `true` or `1` then empty items are skipped from the output.
         *
         * Note that if used, the results might contain less items than the limit
         * value.
         *
         */
        skipEmpty?: boolean;
        /**
         * If `true` or `1` then, the endpoint applies the
         * `fields=url,pageFunctionResult,errorInfo`
         * and `unwind=pageFunctionResult` query parameters. This feature is used
         * to emulate simplified results provided by the
         * legacy Apify Crawler product and it's not recommended to use it in new
         * integrations.
         *
         */
        simplified?: boolean;
        /**
         * If `true` or `1` then, the all the items with errorInfo property will be
         * skipped from the output.
         * This feature is here to emulate functionality of API version 1 used for
         * the legacy Apify Crawler product and it's not recommended to use it in
         * new integrations.
         *
         */
        skipFailedPages?: boolean;
    };
    url: '/v2/actor-tasks/{actorTaskId}/run-sync-get-dataset-items';
};

export type ActorTaskRunSyncGetDatasetItemsGetErrors = {
    400: ErrorResponse;
    /**
     * Request Timeout: the HTTP request exceeded the 300 second limit
     */
    408: ErrorResponse;
};

export type ActorTaskRunSyncGetDatasetItemsGetError = ActorTaskRunSyncGetDatasetItemsGetErrors[keyof ActorTaskRunSyncGetDatasetItemsGetErrors];

export type ActorTaskRunSyncGetDatasetItemsGetResponses = {
    201: {
        [key: string]: unknown;
    };
};

export type ActorTaskRunSyncGetDatasetItemsGetResponse = ActorTaskRunSyncGetDatasetItemsGetResponses[keyof ActorTaskRunSyncGetDatasetItemsGetResponses];

export type ActorTaskRunSyncGetDatasetItemsPostData = {
    body: {
        [key: string]: unknown;
    };
    path: {
        /**
         * Task ID or a tilde-separated owner's username and task's name.
         */
        actorTaskId: string;
    };
    query?: {
        /**
         * Optional timeout for the run, in seconds. By default, the run uses a
         * timeout specified in the task settings.
         *
         */
        timeout?: number;
        /**
         * Memory limit for the run, in megabytes. The amount of memory can be set
         * to a power of 2 with a minimum of 128. By default, the run uses a memory
         * limit specified in the task settings.
         *
         */
        memory?: number;
        /**
         * The maximum number of items that the task run should return. This is
         * useful for pay-per-result tasks, as it allows you to limit the number of
         * results that will be charged to your subscription. You can access the
         * maximum number of items in your Actor by using the
         * `ACTOR_MAX_PAID_DATASET_ITEMS` environment variable.
         *
         */
        maxItems?: number;
        /**
         * Specifies the maximum cost of the task run. This is
         * useful for pay-per-event tasks, as it allows you to limit the amount
         * charged to your subscription. You can access the
         * maximum cost in your Actor by using the
         * `ACTOR_MAX_TOTAL_CHARGE_USD` environment variable.
         *
         */
        maxTotalChargeUsd?: number;
        /**
         * Specifies the Actor build to run. It can be either a build tag or build
         * number. By default, the run uses the build specified in the task
         * settings (typically `latest`).
         *
         */
        build?: string;
        /**
         * Specifies optional webhooks associated with the Actor run, which can be
         * used to receive a notification
         * e.g. when the Actor finished or failed. The value is a Base64-encoded
         * JSON array of objects defining the webhooks. For more information, see
         * [Webhooks
         * documentation](https://docs.apify.com/platform/integrations/webhooks).
         *
         */
        webhooks?: string;
        /**
         * Format of the results, possible values are: `json`, `jsonl`, `csv`,
         * `html`, `xlsx`, `xml` and `rss`. The default value is `json`.
         *
         */
        format?: string;
        /**
         * If `true` or `1` then the API endpoint returns only non-empty items and
         * skips hidden fields
         * (i.e. fields starting with the # character).
         * The `clean` parameter is just a shortcut for `skipHidden=true` and
         * `skipEmpty=true` parameters.
         * Note that since some objects might be skipped from the output, that the
         * result might contain less items than the `limit` value.
         *
         */
        clean?: boolean;
        /**
         * Number of items that should be skipped at the start. The default value
         * is `0`.
         *
         */
        offset?: number;
        /**
         * Maximum number of items to return. By default there is no limit.
         */
        limit?: number;
        /**
         * A comma-separated list of fields which should be picked from the items,
         * only these fields will remain in the resulting record objects.
         * Note that the fields in the outputted items are sorted the same way as
         * they are specified in the `fields` query parameter.
         * You can use this feature to effectively fix the output format.
         *
         */
        fields?: string;
        /**
         * A comma-separated list of fields which should be omitted from the items.
         */
        omit?: string;
        /**
         * A comma-separated list of fields which should be unwound, in order which
         * they should be processed. Each field should be either an array or an object.
         * If the field is an array then every element of
         * the array will become a separate record and merged with parent object.
         * If the unwound field is an object then it is merged with the parent
         * object
         * If the unwound field is missing or its value is neither an array nor an
         * object and therefore cannot be merged with a parent object then the item
         * gets preserved as it is.
         * Note that the unwound items ignore the `desc` parameter.
         *
         */
        unwind?: string;
        /**
         * A comma-separated list of fields which should transform nested objects
         * into flat structures.
         * For example, with `flatten="foo"` the object `{"foo":{"bar": "hello"}}`
         * is turned into `{"foo.bar": "hello"}`.
         * The original object with properties is replaced with the flattened
         * object.
         *
         */
        flatten?: string;
        /**
         * By default, results are returned in the same order as they were stored.
         * To reverse the order, set this parameter to `true` or `1`.
         *
         */
        desc?: boolean;
        /**
         * If `true` or `1` then the response will define the `Content-Disposition:
         * attachment` header, forcing a web browser to download the file rather
         * than to display it. By default this header is not present.
         *
         */
        attachment?: boolean;
        /**
         * A delimiter character for CSV files, only used if `format=csv`. You
         * might need to URL-encode the character (e.g. use `%09` for tab or `%3B`
         * for semicolon). The default delimiter is a simple comma (`,`).
         *
         */
        delimiter?: string;
        /**
         * All text responses are encoded in UTF-8 encoding. By default, the
         * `format=csv` files are prefixed with
         * the UTF-8 Byte Order Mark (BOM), while `json`, `jsonl`, `xml`, `html`
         * and `rss` files are not.
         * If you want to override this default behavior, specify `bom=1` query
         * parameter to include the BOM or `bom=0` to skip it.
         *
         */
        bom?: boolean;
        /**
         * Overrides default root element name of `xml` output. By default the root
         * element is `items`.
         *
         */
        xmlRoot?: string;
        /**
         * Overrides default element name that wraps each page or page function
         * result object in `xml` output. By default the element name is `item`.
         *
         */
        xmlRow?: string;
        /**
         * If `true` or `1` then header row in the `csv` format is skipped.
         */
        skipHeaderRow?: boolean;
        /**
         * If `true` or `1` then hidden fields are skipped from the output,
         * i.e. fields starting with the `#` character.
         *
         */
        skipHidden?: boolean;
        /**
         * If `true` or `1` then empty items are skipped from the output.
         *
         * Note that if used, the results might contain less items than the limit
         * value.
         *
         */
        skipEmpty?: boolean;
        /**
         * If `true` or `1` then, the endpoint applies the
         * `fields=url,pageFunctionResult,errorInfo`
         *
         * and `unwind=pageFunctionResult` query parameters. This feature is used
         * to emulate simplified results provided by the
         *
         * legacy Apify Crawler product and it's not recommended to use it in new
         * integrations.
         *
         */
        simplified?: boolean;
        /**
         * If `true` or `1` then, the all the items with errorInfo property will be
         * skipped from the output.
         *
         * This feature is here to emulate functionality of API version 1 used for
         * the legacy Apify Crawler product and it's not recommended to use it in
         * new integrations.
         *
         */
        skipFailedPages?: boolean;
    };
    url: '/v2/actor-tasks/{actorTaskId}/run-sync-get-dataset-items';
};

export type ActorTaskRunSyncGetDatasetItemsPostErrors = {
    400: ErrorResponse;
};

export type ActorTaskRunSyncGetDatasetItemsPostError = ActorTaskRunSyncGetDatasetItemsPostErrors[keyof ActorTaskRunSyncGetDatasetItemsPostErrors];

export type ActorTaskRunSyncGetDatasetItemsPostResponses = {
    201: {
        [key: string]: unknown;
    };
};

export type ActorTaskRunSyncGetDatasetItemsPostResponse = ActorTaskRunSyncGetDatasetItemsPostResponses[keyof ActorTaskRunSyncGetDatasetItemsPostResponses];

export type ActorTaskRunsLastGetData = {
    body?: never;
    path: {
        /**
         * Task ID or a tilde-separated owner's username and Actor task name.
         */
        actorTaskId: string;
    };
    query?: {
        /**
         * Filter for the run status.
         */
        status?: string;
    };
    url: '/v2/actor-tasks/{actorTaskId}/runs/last';
};

export type ActorTaskRunsLastGetResponses = {
    200: {
        data: Run;
    };
};

export type ActorTaskRunsLastGetResponse = ActorTaskRunsLastGetResponses[keyof ActorTaskRunsLastGetResponses];

export type ActorRunsGetData = {
    body?: never;
    path?: never;
    query?: {
        /**
         * Number of array elements that should be skipped at the start. The
         * default value is `0`.
         *
         */
        offset?: number;
        /**
         * Maximum number of array elements to return. The default value (as well
         * as the maximum) is `1000`.
         *
         */
        limit?: number;
        /**
         * If `true` or `1` then the objects are sorted by the `startedAt` field in
         * descending order. By default, they are sorted in ascending order.
         *
         */
        desc?: boolean;
        /**
         * Return only runs with the provided status ([available
         * statuses](https://docs.apify.com/platform/actors/running/runs-and-builds#lifecycle))
         *
         */
        status?: string;
    };
    url: '/v2/actor-runs';
};

export type ActorRunsGetResponses = {
    200: GetUserRunsListResponse;
};

export type ActorRunsGetResponse = ActorRunsGetResponses[keyof ActorRunsGetResponses];

export type ActorRunDeleteData = {
    body?: never;
    path: {
        /**
         * Run ID.
         */
        runId: string;
    };
    query?: never;
    url: '/v2/actor-runs/{runId}';
};

export type ActorRunDeleteResponses = {
    204: void;
};

export type ActorRunDeleteResponse = ActorRunDeleteResponses[keyof ActorRunDeleteResponses];

export type ActorRunGetData = {
    body?: never;
    path: {
        /**
         * Run ID.
         */
        runId: string;
    };
    query?: {
        /**
         * The maximum number of seconds the server waits for the run to finish. By
         * default it is `0`, the maximum value is `60`. <!--
         * MAX_ACTOR_JOB_ASYNC_WAIT_SECS -->
         * If the run finishes in time then the returned run object will have a terminal status (e.g. `SUCCEEDED`),
         * otherwise it will have a transitional status (e.g. `RUNNING`).
         *
         */
        waitForFinish?: number;
    };
    url: '/v2/actor-runs/{runId}';
};

export type ActorRunGetResponses = {
    200: RunResponse;
};

export type ActorRunGetResponse = ActorRunGetResponses[keyof ActorRunGetResponses];

export type ActorRunPutData = {
    body: UpdateRunRequest & unknown;
    path: {
        /**
         * Run ID.
         */
        runId: string;
    };
    query?: never;
    url: '/v2/actor-runs/{runId}';
};

export type ActorRunPutResponses = {
    200: RunResponse;
};

export type ActorRunPutResponse = ActorRunPutResponses[keyof ActorRunPutResponses];

export type ActorRunAbortPostData = {
    body?: never;
    path: {
        /**
         * Run ID.
         */
        runId: string;
    };
    query?: {
        /**
         * If true passed, the Actor run will abort gracefully.
         * It will send `aborting` and `persistState` event into run and force-stop the run after 30 seconds.
         * It is helpful in cases where you plan to resurrect the run later.
         *
         */
        gracefully?: boolean;
    };
    url: '/v2/actor-runs/{runId}/abort';
};

export type ActorRunAbortPostResponses = {
    200: RunResponse;
};

export type ActorRunAbortPostResponse = ActorRunAbortPostResponses[keyof ActorRunAbortPostResponses];

export type ActorRunMetamorphPostData = {
    body?: never;
    path: {
        /**
         * Actor run ID.
         */
        runId: string;
    };
    query: {
        /**
         * ID of a target Actor that the run should be transformed into.
         */
        targetActorId: string;
        /**
         * Optional build of the target Actor.
         *
         * It can be either a build tag or build number. By default, the run uses
         * the build specified in the default run configuration for the target
         * Actor (typically `latest`).
         *
         */
        build?: string;
    };
    url: '/v2/actor-runs/{runId}/metamorph';
};

export type ActorRunMetamorphPostResponses = {
    200: RunResponse;
};

export type ActorRunMetamorphPostResponse = ActorRunMetamorphPostResponses[keyof ActorRunMetamorphPostResponses];

export type ActorRunRebootPostData = {
    body?: never;
    path: {
        /**
         * Actor run ID.
         */
        runId: string;
    };
    query?: never;
    url: '/v2/actor-runs/{runId}/reboot';
};

export type ActorRunRebootPostResponses = {
    200: RunResponse;
};

export type ActorRunRebootPostResponse = ActorRunRebootPostResponses[keyof ActorRunRebootPostResponses];

export type PostResurrectRunData = {
    body?: never;
    path: {
        /**
         * Run ID.
         */
        runId: string;
    };
    query?: {
        /**
         * Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the run that is being resurrected (typically `latest`).
         */
        build?: string;
        /**
         * Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the run that is being resurrected.
         */
        timeout?: number;
        memory?: number;
        /**
         * Optional number of items that the run should return. Used for pay-per-result Actors. The value can only be increased beyond the value specified when the Actor run was started.
         *
         */
        maxItems?: number;
        /**
         * Optional maximum cost of the run, in USD. Used for pay-per-event Actors. The value can only be increased beyond the value specified when the Actor run was started.
         *
         */
        maxTotalChargeUsd?: number;
    };
    url: '/v2/actor-runs/{runId}/resurrect';
};

export type PostResurrectRunResponses = {
    200: RunResponse;
};

export type PostResurrectRunResponse = PostResurrectRunResponses[keyof PostResurrectRunResponses];

export type PostChargeRunData = {
    /**
     * Define which event, and how many times, you want to charge for.
     */
    body: ChargeRunRequest;
    headers?: {
        /**
         * Always pass a unique idempotency key (any unique string) for each charge to avoid double charging in case of retries or network errors.
         */
        'idempotency-key'?: string;
    };
    path: {
        /**
         * Run ID.
         */
        runId: string;
    };
    query?: never;
    url: '/v2/actor-runs/{runId}/charge';
};

export type PostChargeRunResponses = {
    /**
     * The charge was successful. Note that you still have to make sure in your Actor that the total charge for the run respects the maximum value set by the user, as the API does not check this. Above the limit, the charges reported as successful in API will not be added to your payouts, but you will still bear the associated costs. Use the Apify charge manager or SDK to avoid having to deal with this manually.
     */
    201: unknown;
};

export type ActorBuildsGetData = {
    body?: never;
    path?: never;
    query?: {
        /**
         * Number of records that should be skipped at the start. The default value
         * is `0`.
         *
         */
        offset?: number;
        /**
         * Maximum number of records to return. The default value as well as the
         * maximum is `1000`.
         *
         */
        limit?: number;
        /**
         * If `true` or `1` then the objects are sorted by the `startedAt` field in
         * descending order. By default, they are sorted in ascending order.
         *
         */
        desc?: boolean;
    };
    url: '/v2/actor-builds';
};

export type ActorBuildsGetResponses = {
    200: GetBuildListResponse;
};

export type ActorBuildsGetResponse = ActorBuildsGetResponses[keyof ActorBuildsGetResponses];

export type ActorBuildDeleteData = {
    body?: never;
    path: {
        /**
         * ID of the build you want to get, found in the build's `Info` tab.
         */
        buildId: string;
    };
    query?: never;
    url: '/v2/actor-builds/{buildId}';
};

export type ActorBuildDeleteResponses = {
    204: void;
};

export type ActorBuildDeleteResponse = ActorBuildDeleteResponses[keyof ActorBuildDeleteResponses];

export type ActorBuildGetData = {
    body?: never;
    path: {
        /**
         * ID of the build you want to get, found in the build's `Info` tab.
         */
        buildId: string;
    };
    query?: {
        /**
         * The maximum number of seconds the server waits for the build to finish.
         * By default it is `0`, the maximum value is `60`. <!-- MAX_ACTOR_JOB_ASYNC_WAIT_SECS -->
         *
         * If the build finishes in time then the returned build object will have a
         * terminal status (e.g. `SUCCEEDED`), otherwise it will have a transitional status (e.g. `RUNNING`).
         *
         */
        waitForFinish?: number;
    };
    url: '/v2/actor-builds/{buildId}';
};

export type ActorBuildGetResponses = {
    200: GetBuildResponse;
};

export type ActorBuildGetResponse = ActorBuildGetResponses[keyof ActorBuildGetResponses];

export type ActorBuildAbortPostData = {
    body?: never;
    path: {
        /**
         * Build ID.
         */
        buildId: string;
    };
    query?: never;
    url: '/v2/actor-builds/{buildId}/abort';
};

export type ActorBuildAbortPostResponses = {
    200: PostAbortBuildResponse;
};

export type ActorBuildAbortPostResponse = ActorBuildAbortPostResponses[keyof ActorBuildAbortPostResponses];

export type ActorBuildLogGetData = {
    body?: never;
    path: {
        /**
         * ID of the Actor build.
         */
        buildId: string;
    };
    query: {
        /**
         * If `true` or `1` then the logs will be streamed as long as the run or
         * build is running.
         *
         */
        stream: boolean;
        /**
         * If `true` or `1` then the web browser will download the log file rather
         * than open it in a tab.
         *
         */
        download: boolean;
    };
    url: '/v2/actor-builds/{buildId}/log';
};

export type ActorBuildLogGetResponses = {
    200: string;
};

export type ActorBuildLogGetResponse = ActorBuildLogGetResponses[keyof ActorBuildLogGetResponses];

export type ActorBuildOpenapiJsonGetData = {
    body?: never;
    path: {
        /**
         * ID of the build you want to get, found in the build's `Info` tab.
         */
        buildId: string;
    };
    query?: never;
    url: '/v2/actor-builds/{buildId}/openapi.json';
};

export type ActorBuildOpenapiJsonGetResponses = {
    200: GetOpenApiResponse;
};

export type ActorBuildOpenapiJsonGetResponse = ActorBuildOpenapiJsonGetResponses[keyof ActorBuildOpenapiJsonGetResponses];

export type KeyValueStoresGetData = {
    body?: never;
    path?: never;
    query?: {
        /**
         * Number of records that should be skipped at the start. The default value
         * is `0`.
         *
         */
        offset?: number;
        /**
         * Maximum number of records to return. The default value as well as the
         * maximum is `1000`.
         *
         */
        limit?: number;
        /**
         * If `true` or `1` then the objects are sorted by the `startedAt` field in
         * descending order. By default, they are sorted in ascending order.
         *
         */
        desc?: boolean;
        /**
         * If `true` or `1` then all the stores are returned. By default, only
         * named key-value stores are returned.
         *
         */
        unnamed?: boolean;
    };
    url: '/v2/key-value-stores';
};

export type KeyValueStoresGetResponses = {
    200: GetListOfKeyValueStoresResponse;
};

export type KeyValueStoresGetResponse = KeyValueStoresGetResponses[keyof KeyValueStoresGetResponses];

export type KeyValueStoresPostData = {
    body?: never;
    path?: never;
    query?: {
        /**
         * Custom unique name to easily identify the store in the future.
         */
        name?: string;
    };
    url: '/v2/key-value-stores';
};

export type KeyValueStoresPostResponses = {
    201: CreateKeyValueStoreResponse;
};

export type KeyValueStoresPostResponse = KeyValueStoresPostResponses[keyof KeyValueStoresPostResponses];

export type KeyValueStoreDeleteData = {
    body?: never;
    path: {
        /**
         * Key-value store ID or `username~store-name`.
         */
        storeId: string;
    };
    query?: never;
    url: '/v2/key-value-stores/{storeId}';
};

export type KeyValueStoreDeleteResponses = {
    204: void;
};

export type KeyValueStoreDeleteResponse = KeyValueStoreDeleteResponses[keyof KeyValueStoreDeleteResponses];

export type KeyValueStoreGetData = {
    body?: never;
    path: {
        /**
         * Key-value store ID or `username~store-name`.
         */
        storeId: string;
    };
    query?: never;
    url: '/v2/key-value-stores/{storeId}';
};

export type KeyValueStoreGetResponses = {
    200: GetStoreResponse;
};

export type KeyValueStoreGetResponse = KeyValueStoreGetResponses[keyof KeyValueStoreGetResponses];

export type KeyValueStorePutData = {
    body: UpdateStoreRequest;
    path: {
        /**
         * Key-value store ID or `username~store-name`.
         */
        storeId: string;
    };
    query?: never;
    url: '/v2/key-value-stores/{storeId}';
};

export type KeyValueStorePutResponses = {
    200: UpdateStoreResponse;
};

export type KeyValueStorePutResponse = KeyValueStorePutResponses[keyof KeyValueStorePutResponses];

export type KeyValueStoreKeysGetData = {
    body?: never;
    path: {
        /**
         * Key-value store ID or `username~store-name`.
         */
        storeId: string;
    };
    query?: {
        /**
         * All keys up to this one (including) are skipped from the result.
         */
        exclusiveStartKey?: string;
        /**
         * Number of keys to be returned. Maximum value is `1000`.
         */
        limit?: number;
        /**
         * Limit the results to keys that belong to a specific collection from the key-value store schema. The key-value store need to have a schema defined for this parameter to work.
         */
        collection?: string;
        /**
         * Limit the results to keys that start with a specific prefix.
         */
        prefix?: string;
    };
    url: '/v2/key-value-stores/{storeId}/keys';
};

export type KeyValueStoreKeysGetResponses = {
    200: GetListOfKeysResponse;
};

export type KeyValueStoreKeysGetResponse = KeyValueStoreKeysGetResponses[keyof KeyValueStoreKeysGetResponses];

export type KeyValueStoreRecordDeleteData = {
    body?: never;
    path: {
        /**
         * Key-value store ID or `username~store-name`.
         */
        storeId: string;
        /**
         * Key of the record.
         */
        recordKey: string;
    };
    query?: never;
    url: '/v2/key-value-stores/{storeId}/records/{recordKey}';
};

export type KeyValueStoreRecordDeleteResponses = {
    204: void;
};

export type KeyValueStoreRecordDeleteResponse = KeyValueStoreRecordDeleteResponses[keyof KeyValueStoreRecordDeleteResponses];

export type KeyValueStoreRecordGetData = {
    body?: never;
    path: {
        /**
         * Key-value store ID or `username~store-name`.
         */
        storeId: string;
        /**
         * Key of the record.
         */
        recordKey: string;
    };
    query?: never;
    url: '/v2/key-value-stores/{storeId}/records/{recordKey}';
};

export type KeyValueStoreRecordGetResponses = {
    200: GetRecordResponse & unknown;
};

export type KeyValueStoreRecordGetResponse = KeyValueStoreRecordGetResponses[keyof KeyValueStoreRecordGetResponses];

export type KeyValueStoreRecordPutData = {
    body: PutRecordRequest;
    headers: {
        'Content-Encoding': 'gzip';
    };
    path: {
        /**
         * Key-value store ID or `username~store-name`.
         */
        storeId: string;
        /**
         * Key of the record.
         */
        recordKey: string;
    };
    query?: never;
    url: '/v2/key-value-stores/{storeId}/records/{recordKey}';
};

export type KeyValueStoreRecordPutResponses = {
    201: {
        [key: string]: unknown;
    };
};

export type KeyValueStoreRecordPutResponse = KeyValueStoreRecordPutResponses[keyof KeyValueStoreRecordPutResponses];

export type DatasetsGetData = {
    body?: never;
    path?: never;
    query?: {
        /**
         * Number of array elements that should be skipped at the start. The default value is `0`.
         *
         */
        offset?: number;
        /**
         * Maximum number of array elements to return. The default value as well as the maximum is `1000`.
         *
         */
        limit?: number;
        /**
         * If `true` or `1` then the objects are sorted by the `startedAt` field in
         * descending order. By default, they are sorted in ascending order.
         *
         */
        desc?: boolean;
        /**
         * If `true` or `1` then all the datasets are returned. By default only named datasets are returned.
         *
         */
        unnamed?: boolean;
    };
    url: '/v2/datasets';
};

export type DatasetsGetResponses = {
    200: GetListOfDatasetsResponse;
};

export type DatasetsGetResponse = DatasetsGetResponses[keyof DatasetsGetResponses];

export type DatasetsPostData = {
    body?: never;
    path?: never;
    query?: {
        /**
         * Custom unique name to easily identify the dataset in the future.
         */
        name?: string;
    };
    url: '/v2/datasets';
};

export type DatasetsPostResponses = {
    201: DatasetResponse;
};

export type DatasetsPostResponse = DatasetsPostResponses[keyof DatasetsPostResponses];

export type DatasetDeleteData = {
    body?: never;
    path: {
        /**
         * Dataset ID or `username~dataset-name`.
         */
        datasetId: string;
    };
    query?: never;
    url: '/v2/datasets/{datasetId}';
};

export type DatasetDeleteResponses = {
    204: void;
};

export type DatasetDeleteResponse = DatasetDeleteResponses[keyof DatasetDeleteResponses];

export type DatasetGetData = {
    body?: never;
    path: {
        /**
         * Dataset ID or `username~dataset-name`.
         */
        datasetId: string;
    };
    query?: {
        /**
         * API authentication token. It is required only when using the `username~dataset-name` format for `datasetId`.
         *
         */
        token?: string;
    };
    url: '/v2/datasets/{datasetId}';
};

export type DatasetGetResponses = {
    200: DatasetResponse;
};

export type DatasetGetResponse = DatasetGetResponses[keyof DatasetGetResponses];

export type DatasetPutData = {
    body: UpdateDatasetRequest;
    path: {
        /**
         * Dataset ID or `username~dataset-name`.
         */
        datasetId: string;
    };
    query?: never;
    url: '/v2/datasets/{datasetId}';
};

export type DatasetPutResponses = {
    200: DatasetResponse;
};

export type DatasetPutResponse = DatasetPutResponses[keyof DatasetPutResponses];

export type DatasetItemsGetData = {
    body?: never;
    path: {
        /**
         * Dataset ID or `username~dataset-name`.
         */
        datasetId: string;
    };
    query?: {
        /**
         * Format of the results, possible values are: `json`, `jsonl`, `csv`, `html`, `xlsx`, `xml` and `rss`. The default value is `json`.
         *
         */
        format?: string;
        /**
         * If `true` or `1` then the API endpoint returns only non-empty items and skips hidden fields (i.e. fields starting with the # character).
         * The `clean` parameter is just a shortcut for `skipHidden=true` and `skipEmpty=true` parameters.
         * Note that since some objects might be skipped from the output, that the result might contain less items than the `limit` value.
         *
         */
        clean?: boolean;
        /**
         * Number of items that should be skipped at the start. The default value is `0`.
         *
         */
        offset?: number;
        /**
         * Maximum number of items to return. By default there is no limit.
         */
        limit?: number;
        /**
         * A comma-separated list of fields which should be picked from the items, only these fields will remain in the resulting record objects.
         * Note that the fields in the outputted items are sorted the same way as they are specified in the `fields` query parameter.
         * You can use this feature to effectively fix the output format.
         *
         */
        fields?: string;
        /**
         * A comma-separated list of fields which should be omitted from the items.
         */
        omit?: string;
        /**
         * A comma-separated list of fields which should be unwound, in order which they should be processed. Each field should be either an array or an object.
         * If the field is an array then every element of the array will become a separate record and merged with parent object.
         * If the unwound field is an object then it is merged with the parent object.
         * If the unwound field is missing or its value is neither an array nor an object and therefore cannot be merged with a parent object then the item gets preserved as it is.
         * Note that the unwound items ignore the `desc` parameter.
         *
         */
        unwind?: string;
        /**
         * A comma-separated list of fields which should transform nested objects into flat structures.
         *
         * For example, with `flatten="foo"` the object `{"foo":{"bar": "hello"}}` is turned into `{"foo.bar": "hello"}`.
         *
         * The original object with properties is replaced with the flattened object.
         *
         */
        flatten?: string;
        /**
         * By default, results are returned in the same order as they were stored.
         * To reverse the order, set this parameter to `true` or `1`.
         *
         */
        desc?: boolean;
        /**
         * If `true` or `1` then the response will define the `Content-Disposition:
         * attachment` header, forcing a web browser to download the file rather
         * than to display it. By default this header is not present.
         *
         */
        attachment?: boolean;
        /**
         * A delimiter character for CSV files, only used if `format=csv`. You
         * might need to URL-encode the character (e.g. use `%09` for tab or `%3B`
         * for semicolon). The default delimiter is a simple comma (`,`).
         *
         */
        delimiter?: string;
        /**
         * All text responses are encoded in UTF-8 encoding. By default, the
         * `format=csv` files are prefixed with the UTF-8 Byte Order Mark (BOM), while `json`, `jsonl`, `xml`, `html` and `rss` files are not.
         *
         * If you want to override this default behavior, specify `bom=1` query parameter to include the BOM or `bom=0` to skip it.
         *
         */
        bom?: boolean;
        /**
         * Overrides default root element name of `xml` output. By default the root element is `items`.
         *
         */
        xmlRoot?: string;
        /**
         * Overrides default element name that wraps each page or page function result object in `xml` output. By default the element name is `item`.
         *
         */
        xmlRow?: string;
        /**
         * If `true` or `1` then header row in the `csv` format is skipped.
         */
        skipHeaderRow?: boolean;
        /**
         * If `true` or `1` then hidden fields are skipped from the output, i.e. fields starting with the `#` character.
         *
         */
        skipHidden?: boolean;
        /**
         * If `true` or `1` then empty items are skipped from the output.
         *
         * Note that if used, the results might contain less items than the limit value.
         *
         */
        skipEmpty?: boolean;
        /**
         * If `true` or `1` then, the endpoint applies the `fields=url,pageFunctionResult,errorInfo`
         * and `unwind=pageFunctionResult` query parameters. This feature is used to emulate simplified results provided by the
         * legacy Apify Crawler product and it's not recommended to use it in new integrations.
         *
         */
        simplified?: boolean;
        /**
         * If `true` or `1` then, the all the items with errorInfo property will be skipped from the output.
         *
         * This feature is here to emulate functionality of API version 1 used for the legacy Apify Crawler product and it's not recommended to use it in new integrations.
         *
         */
        skipFailedPages?: boolean;
    };
    url: '/v2/datasets/{datasetId}/items';
};

export type DatasetItemsGetResponses = {
    200: Array<{
        [key: string]: unknown;
    }>;
};

export type DatasetItemsGetResponse = DatasetItemsGetResponses[keyof DatasetItemsGetResponses];

export type DatasetItemsPostData = {
    body: Array<PutItemsRequest>;
    path: {
        /**
         * Dataset ID or `username~dataset-name`.
         */
        datasetId: string;
    };
    query?: never;
    url: '/v2/datasets/{datasetId}/items';
};

export type DatasetItemsPostErrors = {
    400: PutItemResponseError & unknown;
};

export type DatasetItemsPostError = DatasetItemsPostErrors[keyof DatasetItemsPostErrors];

export type DatasetItemsPostResponses = {
    201: {
        [key: string]: unknown;
    };
};

export type DatasetItemsPostResponse = DatasetItemsPostResponses[keyof DatasetItemsPostResponses];

export type DatasetStatisticsGetData = {
    body?: never;
    path: {
        /**
         * Dataset ID or `username~dataset-name`.
         */
        datasetId: string;
    };
    query?: never;
    url: '/v2/datasets/{datasetId}/statistics';
};

export type DatasetStatisticsGetResponses = {
    200: GetDatasetStatisticsResponse;
};

export type DatasetStatisticsGetResponse = DatasetStatisticsGetResponses[keyof DatasetStatisticsGetResponses];

export type RequestQueuesGetData = {
    body?: never;
    path?: never;
    query?: {
        /**
         * Number of array elements that should be skipped at the start. The
         * default value is `0`.
         *
         */
        offset?: number;
        /**
         * Maximum number of array elements to return. The default value as well as
         * the maximum is `1000`.
         *
         */
        limit?: number;
        /**
         * If `true` or `1` then the objects are sorted by the `startedAt` field in
         * descending order. By default, they are sorted in ascending order.
         *
         */
        desc?: boolean;
        /**
         * If `true` or `1` then all the queues are returned. By default only named
         * queues are returned.
         *
         */
        unnamed?: boolean;
    };
    url: '/v2/request-queues';
};

export type RequestQueuesGetResponses = {
    200: GetListOfRequestQueuesResponse;
};

export type RequestQueuesGetResponse = RequestQueuesGetResponses[keyof RequestQueuesGetResponses];

export type RequestQueuesPostData = {
    body?: never;
    path?: never;
    query?: {
        /**
         * Custom unique name to easily identify the queue in the future.
         */
        name?: string;
    };
    url: '/v2/request-queues';
};

export type RequestQueuesPostResponses = {
    201: CreateRequestQueueResponse & unknown;
};

export type RequestQueuesPostResponse = RequestQueuesPostResponses[keyof RequestQueuesPostResponses];

export type RequestQueueDeleteData = {
    body?: never;
    path: {
        /**
         * Queue ID or `username~queue-name`.
         */
        queueId: string;
    };
    query?: never;
    url: '/v2/request-queues/{queueId}';
};

export type RequestQueueDeleteResponses = {
    204: void;
};

export type RequestQueueDeleteResponse = RequestQueueDeleteResponses[keyof RequestQueueDeleteResponses];

export type RequestQueueGetData = {
    body?: never;
    path: {
        /**
         * Queue ID or `username~queue-name`.
         */
        queueId: string;
    };
    query?: never;
    url: '/v2/request-queues/{queueId}';
};

export type RequestQueueGetResponses = {
    200: GetRequestQueueResponse & unknown;
};

export type RequestQueueGetResponse = RequestQueueGetResponses[keyof RequestQueueGetResponses];

export type RequestQueuePutData = {
    body: UpdateRequestQueueRequest & unknown;
    path: {
        /**
         * Queue ID or `username~queue-name`.
         */
        queueId: string;
    };
    query?: never;
    url: '/v2/request-queues/{queueId}';
};

export type RequestQueuePutResponses = {
    200: UpdateRequestQueueResponse & unknown;
};

export type RequestQueuePutResponse = RequestQueuePutResponses[keyof RequestQueuePutResponses];

export type RequestQueueRequestsBatchDeleteData = {
    body?: never;
    headers: {
        'Content-Type': 'application/json';
    };
    path: {
        /**
         * Queue ID or `username~queue-name`.
         */
        queueId: string;
    };
    query?: {
        /**
         * A unique identifier of the client accessing the request queue. It must
         * be a string between 1 and 32 characters long
         *
         */
        clientKey?: string;
    };
    url: '/v2/request-queues/{queueId}/requests/batch';
};

export type RequestQueueRequestsBatchDeleteResponses = {
    204: BatchOperationResponse & unknown;
};

export type RequestQueueRequestsBatchDeleteResponse = RequestQueueRequestsBatchDeleteResponses[keyof RequestQueueRequestsBatchDeleteResponses];

export type RequestQueueRequestsBatchPostData = {
    body: Array<RequestWithoutId>;
    path: {
        /**
         * Queue ID or `username~queue-name`.
         */
        queueId: string;
    };
    query?: {
        /**
         * A unique identifier of the client accessing the request queue. It must
         * be a string between 1 and 32 characters long
         *
         */
        clientKey?: string;
        /**
         * Determines if request should be added to the head of the queue or to the
         * end. Default value is `false` (end of queue).
         *
         */
        forefront?: string;
    };
    url: '/v2/request-queues/{queueId}/requests/batch';
};

export type RequestQueueRequestsBatchPostResponses = {
    201: BatchOperationResponse & unknown;
};

export type RequestQueueRequestsBatchPostResponse = RequestQueueRequestsBatchPostResponses[keyof RequestQueueRequestsBatchPostResponses];

export type RequestQueueRequestsUnlockPostData = {
    body?: never;
    path: {
        /**
         * Queue ID or `username~queue-name`.
         */
        queueId: string;
    };
    query?: {
        /**
         * A unique identifier of the client accessing the request queue. It must
         * be a string between 1 and 32 characters long
         *
         */
        clientKey?: string;
    };
    url: '/v2/request-queues/{queueId}/requests/unlock';
};

export type RequestQueueRequestsUnlockPostResponses = {
    /**
     * Number of requests that were unlocked
     */
    200: {
        data: {
            /**
             * Number of requests that were successfully unlocked
             */
            unlockedCount: number;
        };
    };
};

export type RequestQueueRequestsUnlockPostResponse = RequestQueueRequestsUnlockPostResponses[keyof RequestQueueRequestsUnlockPostResponses];

export type RequestQueueRequestsGetData = {
    body?: never;
    path: {
        /**
         * Queue ID or `username~queue-name`.
         */
        queueId: string;
    };
    query?: {
        /**
         * A unique identifier of the client accessing the request queue. It must
         * be a string between 1 and 32 characters long. This identifier is used to
         * determine whether the queue was accessed by multiple clients. If
         * `clientKey` is not provided,
         * the system considers this API call to come from a new client. For
         * details, see the `hadMultipleClients` field returned by the [Get
         * head](#/reference/request-queues/queue-head) operation.
         *
         */
        clientKey?: string;
        /**
         * All requests up to this one (including) are skipped from the result.
         */
        exclusiveStartId?: string;
        /**
         * Number of keys to be returned. Maximum value is `10000`.
         */
        limit?: number;
    };
    url: '/v2/request-queues/{queueId}/requests';
};

export type RequestQueueRequestsGetResponses = {
    200: ListRequestsResponse & unknown;
};

export type RequestQueueRequestsGetResponse = RequestQueueRequestsGetResponses[keyof RequestQueueRequestsGetResponses];

export type RequestQueueRequestsPostData = {
    body: RequestWithoutId & unknown;
    path: {
        /**
         * Queue ID or `username~queue-name`.
         */
        queueId: string;
    };
    query?: {
        /**
         * A unique identifier of the client accessing the request queue. It must
         * be a string between 1 and 32 characters long. This identifier is used to
         * determine whether the queue was accessed by multiple clients. If
         * `clientKey` is not provided,
         * the system considers this API call to come from a new client. For
         * details, see the `hadMultipleClients` field returned by the [Get
         * head](#/reference/request-queues/queue-head) operation.
         *
         */
        clientKey?: string;
        /**
         * Determines if request should be added to the head of the queue or to the
         * end. Default value is `false` (end of queue).
         *
         */
        forefront?: string;
    };
    url: '/v2/request-queues/{queueId}/requests';
};

export type RequestQueueRequestsPostResponses = {
    201: AddRequestResponse & unknown;
};

export type RequestQueueRequestsPostResponse = RequestQueueRequestsPostResponses[keyof RequestQueueRequestsPostResponses];

export type RequestQueueRequestDeleteData = {
    body?: never;
    path: {
        /**
         * Queue ID or `username~queue-name`.
         */
        queueId: string;
        /**
         * Request ID.
         */
        requestId: string;
    };
    query?: {
        /**
         * A unique identifier of the client accessing the request queue. It must
         * be a string between 1 and 32 characters long. This identifier is used to
         * determine whether the queue was accessed by multiple clients. If
         * `clientKey` is not provided,
         * the system considers this API call to come from a new client. For
         * details, see the `hadMultipleClients` field returned by the [Get
         * head](#/reference/request-queues/queue-head) operation.
         *
         */
        clientKey?: string;
    };
    url: '/v2/request-queues/{queueId}/requests/{requestId}';
};

export type RequestQueueRequestDeleteResponses = {
    204: void;
};

export type RequestQueueRequestDeleteResponse = RequestQueueRequestDeleteResponses[keyof RequestQueueRequestDeleteResponses];

export type RequestQueueRequestGetData = {
    body?: never;
    path: {
        /**
         * Queue ID or `username~queue-name`.
         */
        queueId: string;
        /**
         * Request ID.
         */
        requestId: string;
    };
    query?: never;
    url: '/v2/request-queues/{queueId}/requests/{requestId}';
};

export type RequestQueueRequestGetResponses = {
    200: GetRequestResponse & unknown;
};

export type RequestQueueRequestGetResponse = RequestQueueRequestGetResponses[keyof RequestQueueRequestGetResponses];

export type RequestQueueRequestPutData = {
    body: RequestQueueItems & unknown;
    path: {
        /**
         * Queue ID or `username~queue-name`.
         */
        queueId: string;
        /**
         * Request ID.
         */
        requestId: string;
    };
    query?: {
        /**
         * Determines if request should be added to the head of the queue or to the
         * end. Default value is `false` (end of queue).
         *
         */
        forefront?: string;
        /**
         * A unique identifier of the client accessing the request queue. It must
         * be a string between 1 and 32 characters long. This identifier is used to
         * determine whether the queue was accessed by multiple clients. If
         * `clientKey` is not provided,
         * the system considers this API call to come from a new client. For
         * details, see the `hadMultipleClients` field returned by the [Get
         * head](#/reference/request-queues/queue-head) operation.
         *
         */
        clientKey?: string;
    };
    url: '/v2/request-queues/{queueId}/requests/{requestId}';
};

export type RequestQueueRequestPutResponses = {
    200: UpdateRequestResponse & unknown;
};

export type RequestQueueRequestPutResponse = RequestQueueRequestPutResponses[keyof RequestQueueRequestPutResponses];

export type RequestQueueHeadGetData = {
    body?: never;
    path: {
        /**
         * Queue ID or `username~queue-name`.
         */
        queueId: string;
    };
    query?: {
        /**
         * How many items from queue should be returned.
         */
        limit?: number;
        /**
         * A unique identifier of the client accessing the request queue. It must
         * be a string between 1 and 32 characters long. This identifier is used to
         * determine whether the queue was accessed by multiple clients. If
         * `clientKey` is not provided,
         * the system considers this API call to come from a new client. For
         * details, see the `hadMultipleClients` field returned by the [Get
         * head](#/reference/request-queues/queue-head) operation.
         *
         */
        clientKey?: string;
    };
    url: '/v2/request-queues/{queueId}/head';
};

export type RequestQueueHeadGetResponses = {
    200: GetHeadResponse & unknown;
};

export type RequestQueueHeadGetResponse = RequestQueueHeadGetResponses[keyof RequestQueueHeadGetResponses];

export type RequestQueueHeadLockPostData = {
    body?: never;
    path: {
        /**
         * Queue ID or `username~queue-name`.
         */
        queueId: string;
    };
    query: {
        /**
         * How long the requests will be locked for (in seconds).
         */
        lockSecs: number;
        /**
         * How many items from the queue should be returned.
         */
        limit?: number;
        /**
         * A unique identifier of the client accessing the request queue. It must
         * be a string between 1 and 32 characters long.
         *
         */
        clientKey?: string;
    };
    url: '/v2/request-queues/{queueId}/head/lock';
};

export type RequestQueueHeadLockPostResponses = {
    200: GetHeadAndLockResponse & unknown;
};

export type RequestQueueHeadLockPostResponse = RequestQueueHeadLockPostResponses[keyof RequestQueueHeadLockPostResponses];

export type RequestQueueRequestLockDeleteData = {
    body?: never;
    headers: {
        'Content-Type': 'application/json';
    };
    path: {
        /**
         * Queue ID or `username~queue-name`.
         */
        queueId: string;
        /**
         * Request ID.
         */
        requestId: string;
    };
    query?: {
        /**
         * A unique identifier of the client accessing the request queue. It must
         * be a string between 1 and 32 characters long. This identifier is used to for locking
         * and unlocking requests. You can delete or prolong lock only for requests that were locked by by same
         * client key or from the same Actor run.
         *
         */
        clientKey?: string;
        /**
         * Determines if request should be added to the head of the queue or to the
         * end after lock was removed.
         *
         */
        forefront?: string;
    };
    url: '/v2/request-queues/{queueId}/requests/{requestId}/lock';
};

export type RequestQueueRequestLockDeleteResponses = {
    204: void;
};

export type RequestQueueRequestLockDeleteResponse = RequestQueueRequestLockDeleteResponses[keyof RequestQueueRequestLockDeleteResponses];

export type RequestQueueRequestLockPutData = {
    body?: never;
    path: {
        /**
         * Queue ID or `username~queue-name`.
         */
        queueId: string;
        /**
         * Request ID.
         */
        requestId: string;
    };
    query: {
        /**
         * For how long second request will be locked.
         */
        lockSecs: number;
        /**
         * A unique identifier of the client accessing the request queue. It must
         * be a string between 1 and 32 characters long. This identifier is used to for locking
         * and unlocking requests. You can delete or prolong lock only for requests that were locked by by same
         * client key or from the same Actor run.
         *
         */
        clientKey?: string;
        /**
         * Determines if request should be added to the head of the queue or to the
         * end after lock expires.
         *
         */
        forefront?: string;
    };
    url: '/v2/request-queues/{queueId}/requests/{requestId}/lock';
};

export type RequestQueueRequestLockPutResponses = {
    200: ProlongRequestLockResponse & unknown;
};

export type RequestQueueRequestLockPutResponse = RequestQueueRequestLockPutResponses[keyof RequestQueueRequestLockPutResponses];

export type WebhooksGetData = {
    body?: never;
    path?: never;
    query?: {
        /**
         * Number of records that should be skipped at the start. The default value
         * is `0`.
         *
         */
        offset?: number;
        /**
         * Maximum number of records to return. The default value as well as the
         * maximum is `1000`.
         *
         */
        limit?: number;
        /**
         * If `true` or `1` then the objects are sorted by the `createdAt` field in
         * descending order. By default, they are sorted in ascending order.
         *
         */
        desc?: boolean;
    };
    url: '/v2/webhooks';
};

export type WebhooksGetResponses = {
    200: GetListOfWebhooksResponse;
};

export type WebhooksGetResponse = WebhooksGetResponses[keyof WebhooksGetResponses];

export type WebhooksPostData = {
    body: WebhookCreate;
    path?: never;
    query?: {
        limit?: string;
        offset?: string;
        desc?: string;
    };
    url: '/v2/webhooks';
};

export type WebhooksPostResponses = {
    201: CreateWebhookResponse;
};

export type WebhooksPostResponse = WebhooksPostResponses[keyof WebhooksPostResponses];

export type WebhookDeleteData = {
    body?: never;
    path: {
        /**
         * Webhook ID.
         */
        webhookId: string;
    };
    query?: never;
    url: '/v2/webhooks/{webhookId}';
};

export type WebhookDeleteResponses = {
    204: {
        [key: string]: unknown;
    };
};

export type WebhookDeleteResponse = WebhookDeleteResponses[keyof WebhookDeleteResponses];

export type WebhookGetData = {
    body?: never;
    path: {
        /**
         * Webhook ID.
         */
        webhookId: string;
    };
    query?: never;
    url: '/v2/webhooks/{webhookId}';
};

export type WebhookGetResponses = {
    200: GetWebhookResponse;
};

export type WebhookGetResponse = WebhookGetResponses[keyof WebhookGetResponses];

export type WebhookPutData = {
    body: WebhookUpdate;
    path: {
        /**
         * Webhook ID.
         */
        webhookId: string;
    };
    query?: never;
    url: '/v2/webhooks/{webhookId}';
};

export type WebhookPutResponses = {
    200: UpdateWebhookResponse;
};

export type WebhookPutResponse = WebhookPutResponses[keyof WebhookPutResponses];

export type WebhookTestPostData = {
    body?: never;
    path: {
        /**
         * Webhook ID.
         */
        webhookId: string;
    };
    query?: never;
    url: '/v2/webhooks/{webhookId}/test';
};

export type WebhookTestPostResponses = {
    201: TestWebhookResponse;
};

export type WebhookTestPostResponse = WebhookTestPostResponses[keyof WebhookTestPostResponses];

export type WebhookDispatchesGetData = {
    body?: never;
    path: {
        /**
         * ID number of the webhook.
         */
        webhookId: string;
    };
    query?: never;
    url: '/v2/webhooks/{webhookId}/dispatches';
};

export type WebhookDispatchesGetResponses = {
    200: WebhookDispatchList;
};

export type WebhookDispatchesGetResponse = WebhookDispatchesGetResponses[keyof WebhookDispatchesGetResponses];

export type WebhookDispatchesGet2Data = {
    body?: never;
    path?: never;
    query?: {
        /**
         * Number of records that should be skipped at the start. The default value
         * is `0`.
         *
         */
        offset?: number;
        /**
         * Maximum number of records to return. The default value as well as the
         * maximum is `1000`.
         *
         */
        limit?: number;
        /**
         * If `true` or `1` then the objects are sorted by the `createdAt` field in
         * descending order. By default, they are sorted in ascending order.
         *
         */
        desc?: boolean;
    };
    url: '/v2/webhook-dispatches';
};

export type WebhookDispatchesGet2Responses = {
    200: WebhookDispatchList;
};

export type WebhookDispatchesGet2Response = WebhookDispatchesGet2Responses[keyof WebhookDispatchesGet2Responses];

export type WebhookDispatchGetData = {
    body?: never;
    path: {
        /**
         * Webhook dispatch ID.
         */
        dispatchId: string;
    };
    query?: never;
    url: '/v2/webhook-dispatches/{dispatchId}';
};

export type WebhookDispatchGetResponses = {
    200: GetWebhookDispatchResponse;
};

export type WebhookDispatchGetResponse = WebhookDispatchGetResponses[keyof WebhookDispatchGetResponses];

export type SchedulesGetData = {
    body?: never;
    path?: never;
    query?: {
        /**
         * Number of records that should be skipped at the start. The default value is `0`.
         *
         */
        offset?: number;
        /**
         * Maximum number of records to return. The default value, as well as the maximum, is `1000`.
         *
         */
        limit?: number;
        /**
         * If `true` or `1`, the objects are sorted by the `createdAt` field in
         * descending order. By default, they are sorted in ascending order.
         *
         */
        desc?: boolean;
    };
    url: '/v2/schedules';
};

export type SchedulesGetResponses = {
    200: GetListOfSchedulesResponse;
};

export type SchedulesGetResponse = SchedulesGetResponses[keyof SchedulesGetResponses];

export type SchedulesPostData = {
    body: ScheduleCreate;
    path?: never;
    query?: never;
    url: '/v2/schedules';
};

export type SchedulesPostResponses = {
    201: ScheduleResponse;
};

export type SchedulesPostResponse = SchedulesPostResponses[keyof SchedulesPostResponses];

export type ScheduleDeleteData = {
    body?: never;
    path: {
        /**
         * Schedule ID.
         */
        scheduleId: string;
    };
    query?: never;
    url: '/v2/schedules/{scheduleId}';
};

export type ScheduleDeleteResponses = {
    204: {
        [key: string]: unknown;
    };
};

export type ScheduleDeleteResponse = ScheduleDeleteResponses[keyof ScheduleDeleteResponses];

export type ScheduleGetData = {
    body?: never;
    path: {
        /**
         * Schedule ID.
         */
        scheduleId: string;
    };
    query?: never;
    url: '/v2/schedules/{scheduleId}';
};

export type ScheduleGetResponses = {
    200: ScheduleResponse;
};

export type ScheduleGetResponse = ScheduleGetResponses[keyof ScheduleGetResponses];

export type SchedulePutData = {
    body: ScheduleCreate;
    path: {
        /**
         * Schedule ID.
         */
        scheduleId: string;
    };
    query?: never;
    url: '/v2/schedules/{scheduleId}';
};

export type SchedulePutResponses = {
    200: ScheduleResponse;
};

export type SchedulePutResponse = SchedulePutResponses[keyof SchedulePutResponses];

export type ScheduleLogGetData = {
    body?: never;
    path: {
        /**
         * Schedule ID.
         */
        scheduleId: string;
    };
    query?: never;
    url: '/v2/schedules/{scheduleId}/log';
};

export type ScheduleLogGetResponses = {
    200: GetScheduleLogResponse;
};

export type ScheduleLogGetResponse = ScheduleLogGetResponses[keyof ScheduleLogGetResponses];

export type StoreGetData = {
    body?: never;
    path?: never;
    query?: {
        /**
         * Maximum number of elements to return. The default and maximum value is
         * `1,000`.
         *
         */
        limit?: number;
        /**
         * Number of elements that should be skipped at the start. The default
         * value is `0`.
         *
         */
        offset?: number;
        /**
         * String to search by. The search runs on the following fields: `title`,
         * `name`, `description`, `username`, `readme`.
         *
         */
        search?: string;
        /**
         * Specifies the field by which to sort the results. The supported values
         * are `relevance` (default), `popularity`, `newest` and `lastUpdate`.
         *
         */
        sortBy?: string;
        /**
         * Filters the results by the specified category.
         */
        category?: string;
        /**
         * Filters the results by the specified username.
         */
        username?: string;
        /**
         * Only return Actors with the specified pricing model.
         *
         */
        pricingModel?: 'FREE' | 'FLAT_PRICE_PER_MONTH' | 'PRICE_PER_DATASET_ITEM' | 'PAY_PER_EVENT';
    };
    url: '/v2/store';
};

export type StoreGetResponses = {
    200: GetListOfActorsInStoreResponse;
};

export type StoreGetResponse = StoreGetResponses[keyof StoreGetResponses];

export type LogGetData = {
    body?: never;
    path: {
        /**
         * ID of the Actor build or run.
         */
        buildOrRunId: string;
    };
    query: {
        /**
         * If `true` or `1` then the logs will be streamed as long as the run or
         * build is running.
         *
         */
        stream: boolean;
        /**
         * If `true` or `1` then the web browser will download the log file rather
         * than open it in a tab.
         *
         */
        download: boolean;
        /**
         * If `true` or `1`, the logs will be kept verbatim. By default, the API removes
         * ANSI escape codes from the logs, keeping only printable characters.
         *
         */
        raw?: boolean;
    };
    url: '/v2/logs/{buildOrRunId}';
};

export type LogGetResponses = {
    200: string;
};

export type LogGetResponse = LogGetResponses[keyof LogGetResponses];

export type UserGetData = {
    body?: never;
    path: {
        /**
         * User ID or username.
         */
        userId: string;
    };
    query?: never;
    url: '/v2/users/{userId}';
};

export type UserGetResponses = {
    200: GetPublicUserDataResponse;
};

export type UserGetResponse = UserGetResponses[keyof UserGetResponses];

export type UsersMeGetData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/v2/users/me';
};

export type UsersMeGetResponses = {
    200: GetPrivateUserDataResponse;
};

export type UsersMeGetResponse = UsersMeGetResponses[keyof UsersMeGetResponses];

export type UsersMeUsageMonthlyGetData = {
    body?: never;
    path?: never;
    query?: {
        /**
         * Date in the YYYY-MM-DD format.
         */
        date?: string;
    };
    url: '/v2/users/me/usage/monthly';
};

export type UsersMeUsageMonthlyGetResponses = {
    200: GetMonthlyUsageResponse;
};

export type UsersMeUsageMonthlyGetResponse = UsersMeUsageMonthlyGetResponses[keyof UsersMeUsageMonthlyGetResponses];

export type UsersMeLimitsGetData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/v2/users/me/limits';
};

export type UsersMeLimitsGetResponses = {
    200: GetLimitsResponse;
};

export type UsersMeLimitsGetResponse = UsersMeLimitsGetResponses[keyof UsersMeLimitsGetResponses];

export type UsersMeLimitsPutData = {
    body?: UpdateLimitsRequest;
    path?: never;
    query?: never;
    url: '/v2/users/me/limits';
};

export type UsersMeLimitsPutResponses = {
    201: {
        [key: string]: unknown;
    };
};

export type UsersMeLimitsPutResponse = UsersMeLimitsPutResponses[keyof UsersMeLimitsPutResponses];

export type ClientOptions = {
    baseUrl: 'https://api.apify.com' | (string & {});
};