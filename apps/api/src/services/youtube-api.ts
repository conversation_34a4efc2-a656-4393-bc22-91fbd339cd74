import type { youtube_v3 } from "googleapis";
import dayjs from "dayjs";
import duration from "dayjs/plugin/duration";
import { mapValues } from "@std/collections";
import { getLogger } from "@logtape/logtape";
import { redactSensitiveData } from "../lib/logging";
import { fetchWithRetry } from "../lib/fetch";

dayjs.extend(duration);

const logger = getLogger(["mx-api", "youtube", "api"]);

export interface YouTubeApiConfig {
  apiKey: string;
}

export interface YouTubeApiMetadata {
  version: "yt_api_v1";
  title: string;
  description: string;
  tags: string[];
  /**
   * in seconds
   */
  duration: number;
  /**
   * ISO 8601 format
   */
  published_at: string;
  uploader_name: string;
  /**
   * uploader's internal uid
   * @example "UC-EnprmCZ3OXyAoG7vjVNCA"
   */
  channel_id: string;
  view_count: number | null;
  like_count: number | null;
  comment_count: number | null;
  language: string;
  thumbnails: Partial<
    Record<
      "default" | "high" | "maxres" | "medium" | "standard",
      { url: string; size?: { width: number; height: number } }
    >
  >;
}

export interface YouTubeApiSubtitle {
  /** internal id, cannot cross-reference with yt-dlp */
  id: string;
  lang: string | null;
  name: string | null;
  /**
   * The caption track's type.
   *
   * Valid values for this property are:
   * * ASR – A caption track generated using automatic speech recognition.
   * * forced – A caption track that plays when no other track is selected in the player. For example, a video that shows aliens speaking in an alien language might have a forced caption track to only show subtitles for the alien language.
   * * standard – A regular caption track. This is the default value.
   */
  track_kind: v.InferOutput<typeof schema.track_kind>;
  /**
   * The type of audio track associated with the caption track.
   *
   * Valid values for this property are:
   * * commentary – The caption track corresponds to an alternate audio track that includes commentary, such as directory commentary.
   * * descriptive – The caption track corresponds to an alternate audio track that includes additional descriptive audio.
   * * primary – The caption track corresponds to the primary audio track for the video, which is the audio track normally associated with the video.
   * * unknown – This is the default value.
   */
  audio_track_type: v.InferOutput<typeof schema.audio_track_type>;
  /**
   * ISO 8601 format
   */
  last_updated: string | null;
}

import * as v from "valibot";

export const schema = {
  track_kind: v.fallback(
    v.pipe(
      v.string(),
      v.toLowerCase(),
      v.picklist(["standard", "asr", "forced"]),
    ),
    "standard",
  ),
  audio_track_type: v.fallback(
    v.picklist(["unknown", "commentary", "descriptive", "primary"]),
    "unknown",
  ),
};

export type YouTubeApiSubtitleAudioTrackType =
  | "unknown"
  | "commentary"
  | "descriptive"
  | "primary";

export class YouTubeDataAPIService {
  #config: YouTubeApiConfig;

  constructor(env: Cloudflare.Env) {
    this.#config = {
      apiKey: env.GOOGLE_API_KEY,
    };
  }

  /**
   * Get video metadata using YouTube Data API
   */
  async getVideoMetadata(videoId: string): Promise<YouTubeApiMetadata | null> {
    logger.info("Fetching YouTube video metadata", { videoId });

    if (!this.#config.apiKey) {
      logger.error("Google API key not configured", { videoId });
      throw new Error("GOOGLE_API_KEY is not configured");
    }

    const parts = ["snippet", "contentDetails", "statistics"] as const;
    const query = new URLSearchParams({
      part: parts.join(","),
      id: videoId,
      key: this.#config.apiKey,
    });

    const url = `https://www.googleapis.com/youtube/v3/videos?${query}`;
    logger.debug("Making YouTube API request", {
      videoId,
      parts: parts.join(","),
      url: url.replace(this.#config.apiKey, "[REDACTED]"),
    });

    const resp = await fetchWithRetry(url);

    if (!resp.ok) {
      const errorText = await resp.text().catch(() => "unknown error");

      logger.error("YouTube API request failed", {
        videoId,
        status: resp.status,
        statusText: resp.statusText,
        error: errorText,
      });
      throw new Error(`YouTube API error: ${resp.status} ${resp.statusText}`);
    }

    const payload = (await resp.json()) as youtube_v3.Schema$VideoListResponse;
    const videoInfo = payload.items?.[0] as SchemaRequired<
      youtube_v3.Schema$Video,
      (typeof parts)[number]
    >;

    if (!videoInfo) {
      logger.warn("Video not found in YouTube API response", {
        videoId,
        itemCount: payload.items?.length ?? 0,
      });
      return null;
    }

    logger.debug("Processing YouTube API metadata", {
      videoId,
      title: videoInfo.snippet.title,
    });

    const metadata = this.#convertToMetadata(videoInfo, videoId);

    logger.info("YouTube video metadata fetched successfully", {
      videoId,
      title: metadata.title,
    });

    return metadata;
  }

  /**
   * Get captions list using YouTube Data API
   */
  async getCaptionsList(videoId: string): Promise<YouTubeApiSubtitle[] | null> {
    logger.info("Fetching YouTube captions list", { videoId });

    if (!this.#config.apiKey) {
      logger.error("Google API key not configured for captions", { videoId });
      throw new Error("GOOGLE_API_KEY is not configured");
    }

    const parts = ["snippet", "id"] as const;
    const query = new URLSearchParams({
      part: parts.join(","),
      videoId: videoId,
      key: this.#config.apiKey,
    });

    const url = `https://www.googleapis.com/youtube/v3/captions?${query}`;
    logger.debug("Making YouTube Captions API request", {
      videoId,
      parts: parts.join(","),
      url: url.replace(this.#config.apiKey, "[REDACTED]"),
    });

    const resp = await fetchWithRetry(url);

    if (!resp.ok) {
      const errorText = await resp.text().catch(() => "unknown error");

      if (resp.status === 404) {
        logger.warn("No captions found for video", {
          videoId,
        });
        return null;
      }

      logger.error("YouTube Captions API request failed", {
        videoId,
        status: resp.status,
        statusText: resp.statusText,
        error: errorText,
      });
      throw new Error(
        `YouTube Captions API error: ${resp.status} ${resp.statusText}`,
      );
    }

    const payload =
      (await resp.json()) as youtube_v3.Schema$CaptionListResponse;

    if (!payload.items) {
      logger.info("No captions found for video", {
        videoId,
      });
      return [];
    }

    logger.debug("Processing captions data", {
      videoId,
      totalCaptions: payload.items.length,
    });

    const captionsList = (
      payload.items as SchemaRequired<
        youtube_v3.Schema$Caption,
        (typeof parts)[number]
      >[]
    )
      .filter((v) => v.snippet.status === "serving")
      .map((caption) => ({
        id: caption.id,
        lang: caption.snippet.language || null,
        name: caption.snippet.name || null,
        track_kind: v.parse(schema.track_kind, caption.snippet.trackKind),
        audio_track_type: v.parse(
          schema.audio_track_type,
          caption.snippet.audioTrackType,
        ),
        last_updated: caption.snippet.lastUpdated || null,
      }));

    logger.info("YouTube captions list fetched successfully", {
      videoId,
      totalCaptions: payload.items.length,
      servingCaptions: captionsList.length,
      languages: captionsList.map((c) => c.lang).filter(Boolean),
    });

    return captionsList;
  }

  /**
   * Convert YouTube API response to our metadata format
   */
  #convertToMetadata(
    videoInfo: SchemaRequired<
      youtube_v3.Schema$Video,
      "snippet" | "contentDetails" | "statistics"
    >,
    videoId: string,
  ): YouTubeApiMetadata {
    logger.debug("Converting YouTube API data to metadata format", {
      videoId,
      channelId: videoInfo.snippet.channelId,
      hasContentDetails: !!videoInfo.contentDetails,
      hasStatistics: !!videoInfo.statistics,
    });

    const durationSeconds = videoInfo.contentDetails?.duration
      ? dayjs.duration(videoInfo.contentDetails.duration).asSeconds()
      : 0;

    const metadata: YouTubeApiMetadata = {
      version: "yt_api_v1" as const,
      title: videoInfo.snippet.title ?? "",
      description: videoInfo.snippet.description ?? "",
      tags: videoInfo.snippet.tags || [],
      duration: durationSeconds,
      published_at: videoInfo.snippet.publishedAt ?? "",
      uploader_name: videoInfo.snippet.channelTitle ?? "",
      channel_id: videoInfo.snippet.channelId ?? "",
      language:
        videoInfo.snippet.defaultLanguage ||
        videoInfo.snippet.defaultAudioLanguage ||
        "",
      thumbnails: mapValues(videoInfo.snippet.thumbnails ?? {}, (v) => {
        if (!v.url) return;
        const size =
          v.width && v.height
            ? { width: v.width, height: v.height }
            : undefined;
        return { url: v.url, size };
      }),
      view_count: parseNullableInt(videoInfo.statistics.viewCount),
      like_count: parseNullableInt(videoInfo.statistics.likeCount),
      comment_count: parseNullableInt(videoInfo.statistics.commentCount),
    };

    logger.debug("Metadata conversion completed", {
      videoId,
      title: metadata.title,
      durationSeconds: metadata.duration,
      tagCount: metadata.tags.length,
      hasStats: !!(
        metadata.view_count ||
        metadata.like_count ||
        metadata.comment_count
      ),
      thumbnailCount: Object.keys(metadata.thumbnails).length,
    });

    return metadata;
  }
}

function parseNullableInt(v: string | undefined | null): number | null {
  if (!v) return null;
  const num = Number.parseInt(v, 10);
  return Number.isNaN(num) ? null : num;
}

type SchemaRequired<T, K extends keyof T> = Required<{
  [P in K]: NonNullable<T[P]>;
}>;
