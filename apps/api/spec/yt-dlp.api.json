{"openapi": "3.0.1", "info": {"title": "Yt Dlp", "version": "0.1", "x-build-id": "NzJ8ucUGZ0ZYpKhy4"}, "servers": [{"url": "https://api.apify.com/v2"}], "paths": {"/acts/aidenlx~yt-dlp/run-sync-get-dataset-items": {"post": {"operationId": "run-sync-get-dataset-items-aidenlx-yt-dlp", "x-openai-isConsequential": false, "summary": "Executes an Actor, waits for its completion, and returns Actor's dataset items in response.", "tags": ["Run Actor"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/inputSchema"}}}}, "parameters": [{"name": "token", "in": "query", "required": true, "schema": {"type": "string"}, "description": "Enter your Apify token here"}], "responses": {"200": {"description": "OK"}}}}, "/acts/aidenlx~yt-dlp/runs": {"post": {"operationId": "runs-sync-aidenlx-yt-dlp", "x-openai-isConsequential": false, "summary": "Executes an Actor and returns information about the initiated run in response.", "tags": ["Run Actor"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/inputSchema"}}}}, "parameters": [{"name": "token", "in": "query", "required": true, "schema": {"type": "string"}, "description": "Enter your Apify token here"}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/runsResponseSchema"}}}}}}}, "/acts/aidenlx~yt-dlp/run-sync": {"post": {"operationId": "run-sync-aidenlx-yt-dlp", "x-openai-isConsequential": false, "summary": "Executes an Actor, waits for completion, and returns the OUTPUT from Key-value store in response.", "tags": ["Run Actor"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/inputSchema"}}}}, "parameters": [{"name": "token", "in": "query", "required": true, "schema": {"type": "string"}, "description": "Enter your Apify token here"}], "responses": {"200": {"description": "OK"}}}}}, "components": {"schemas": {"inputSchema": {"type": "object", "required": ["youtube_url"], "properties": {"youtube_url": {"title": "YouTube URL", "pattern": "^https?://(www\\.)?(youtube\\.com/watch\\?v=|youtu\\.be/)[a-zA-Z0-9_-]+", "type": "string", "description": "The YouTube video URL to extract information from"}, "use_apify_proxy": {"title": "Use Apify Proxy", "type": "boolean", "description": "Whether to use Apify residential proxy", "default": true}, "custom_proxy": {"title": "Custom Proxy URL", "type": "string", "description": "Custom proxy URL (http:// or https://). If provided, this will be used instead of Apify proxy"}}}, "runsResponseSchema": {"type": "object", "properties": {"data": {"type": "object", "properties": {"id": {"type": "string"}, "actId": {"type": "string"}, "userId": {"type": "string"}, "startedAt": {"type": "string", "format": "date-time", "example": "2025-01-08T00:00:00.000Z"}, "finishedAt": {"type": "string", "format": "date-time", "example": "2025-01-08T00:00:00.000Z"}, "status": {"type": "string", "example": "READY"}, "meta": {"type": "object", "properties": {"origin": {"type": "string", "example": "API"}, "userAgent": {"type": "string"}}}, "stats": {"type": "object", "properties": {"inputBodyLen": {"type": "integer", "example": 2000}, "rebootCount": {"type": "integer", "example": 0}, "restartCount": {"type": "integer", "example": 0}, "resurrectCount": {"type": "integer", "example": 0}, "computeUnits": {"type": "integer", "example": 0}}}, "options": {"type": "object", "properties": {"build": {"type": "string", "example": "latest"}, "timeoutSecs": {"type": "integer", "example": 300}, "memoryMbytes": {"type": "integer", "example": 1024}, "diskMbytes": {"type": "integer", "example": 2048}}}, "buildId": {"type": "string"}, "defaultKeyValueStoreId": {"type": "string"}, "defaultDatasetId": {"type": "string"}, "defaultRequestQueueId": {"type": "string"}, "buildNumber": {"type": "string", "example": "1.0.0"}, "containerUrl": {"type": "string"}, "usage": {"type": "object", "properties": {"ACTOR_COMPUTE_UNITS": {"type": "integer", "example": 0}, "DATASET_READS": {"type": "integer", "example": 0}, "DATASET_WRITES": {"type": "integer", "example": 0}, "KEY_VALUE_STORE_READS": {"type": "integer", "example": 0}, "KEY_VALUE_STORE_WRITES": {"type": "integer", "example": 1}, "KEY_VALUE_STORE_LISTS": {"type": "integer", "example": 0}, "REQUEST_QUEUE_READS": {"type": "integer", "example": 0}, "REQUEST_QUEUE_WRITES": {"type": "integer", "example": 0}, "DATA_TRANSFER_INTERNAL_GBYTES": {"type": "integer", "example": 0}, "DATA_TRANSFER_EXTERNAL_GBYTES": {"type": "integer", "example": 0}, "PROXY_RESIDENTIAL_TRANSFER_GBYTES": {"type": "integer", "example": 0}, "PROXY_SERPS": {"type": "integer", "example": 0}}}, "usageTotalUsd": {"type": "number", "example": 5e-05}, "usageUsd": {"type": "object", "properties": {"ACTOR_COMPUTE_UNITS": {"type": "integer", "example": 0}, "DATASET_READS": {"type": "integer", "example": 0}, "DATASET_WRITES": {"type": "integer", "example": 0}, "KEY_VALUE_STORE_READS": {"type": "integer", "example": 0}, "KEY_VALUE_STORE_WRITES": {"type": "number", "example": 5e-05}, "KEY_VALUE_STORE_LISTS": {"type": "integer", "example": 0}, "REQUEST_QUEUE_READS": {"type": "integer", "example": 0}, "REQUEST_QUEUE_WRITES": {"type": "integer", "example": 0}, "DATA_TRANSFER_INTERNAL_GBYTES": {"type": "integer", "example": 0}, "DATA_TRANSFER_EXTERNAL_GBYTES": {"type": "integer", "example": 0}, "PROXY_RESIDENTIAL_TRANSFER_GBYTES": {"type": "integer", "example": 0}, "PROXY_SERPS": {"type": "integer", "example": 0}}}}}}}}}}