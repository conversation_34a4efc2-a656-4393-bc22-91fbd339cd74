{"$schema": "http://json-schema.org/draft-07/schema#", "title": "yt-dlp Info JSON Schema", "description": "Comprehensive schema for yt-dlp video information JSON output", "type": "object", "required": ["id", "title", "webpage_url", "extractor", "extractor_key"], "properties": {"id": {"description": "Video ID", "type": "string"}, "title": {"description": "Video title", "type": "string"}, "description": {"description": "Video description", "type": ["string", "null"]}, "format": {"description": "Format description", "type": "string"}, "_format_sort_fields": {"description": "Format sorting fields", "type": "array", "items": {"type": "string"}}, "_has_drm": {"description": "Whether content has DRM", "type": ["boolean", "null"]}, "abr": {"description": "Audio bitrate", "type": ["number", "null"]}, "acodec": {"description": "Audio codec", "type": ["string", "null"]}, "age_limit": {"description": "Age restriction limit", "type": ["integer", "null"]}, "aspect_ratio": {"description": "Aspect ratio", "type": ["number", "null"]}, "asr": {"description": "Audio sample rate", "type": ["integer", "null"]}, "audio_channels": {"description": "Number of audio channels", "type": ["integer", "null"]}, "audio_ext": {"description": "Audio file extension", "type": "string"}, "automatic_captions": {"description": "Available automatic captions by language code", "type": "object", "patternProperties": {"^[a-z]{2}(-[A-Z]{2})?(-orig)?$": {"type": "array", "items": {"type": "object", "required": ["url", "ext"], "properties": {"__yt_dlp_client": {"type": ["string", "null"]}, "ext": {"type": "string"}, "protocol": {"type": "string"}, "url": {"type": "string"}, "name": {"type": "string"}}}}}}, "availability": {"description": "Video availability status", "type": ["string", "null"], "enum": ["public", "unlisted", "private", "premium_only", "subscriber_only", "needs_auth", "not_live", null]}, "average_rating": {"description": "Average user rating", "type": ["number", "null"]}, "categories": {"description": "Video categories", "type": "array", "items": {"type": "string"}}, "channel": {"description": "Channel name", "type": ["string", "null"]}, "channel_follower_count": {"description": "Channel follower count", "type": ["integer", "null"]}, "channel_id": {"description": "Channel ID", "type": ["string", "null"]}, "channel_url": {"description": "Channel URL", "type": ["string", "null"]}, "chapters": {"description": "Video chapters", "type": "array", "items": {"type": "object", "required": ["title", "start_time", "end_time"], "properties": {"title": {"type": "string"}, "end_time": {"type": "number"}, "start_time": {"type": "number"}}}}, "comment_count": {"description": "Number of comments", "type": ["integer", "null"]}, "display_id": {"description": "Display ID", "type": "string"}, "downloader_options": {"description": "Downloader options", "type": ["object", "null"]}, "duration": {"description": "Video duration in seconds", "type": ["integer", "null"]}, "duration_string": {"description": "Duration as string (HH:MM:SS format)", "type": ["string", "null"]}, "dynamic_range": {"description": "Dynamic range (SDR/HDR)", "type": ["string", "null"], "enum": ["SDR", "HDR10", "HDR10+", "DV", null]}, "epoch": {"description": "Epoch timestamp", "type": ["integer", "null"]}, "ext": {"description": "File extension", "type": "string"}, "extractor": {"description": "Extractor name", "type": "string"}, "extractor_key": {"description": "Extractor key", "type": "string"}, "filesize": {"description": "File size in bytes", "type": ["integer", "null"]}, "filesize_approx": {"description": "Approximate file size", "type": ["integer", "null"]}, "format_id": {"description": "Selected format ID", "type": "string"}, "format_note": {"description": "Format note", "type": ["string", "null"]}, "formats": {"description": "Available video/audio formats", "type": "array", "items": {"type": "object", "required": ["format_id", "url"], "properties": {"format": {"type": "string"}, "abr": {"type": ["number", "null"]}, "acodec": {"type": ["string", "null"]}, "aspect_ratio": {"type": ["number", "null"]}, "audio_ext": {"type": "string"}, "columns": {"type": ["integer", "null"]}, "ext": {"type": "string"}, "filesize_approx": {"type": ["integer", "null"]}, "format_id": {"type": "string"}, "format_note": {"type": ["string", "null"]}, "fps": {"type": ["number", "null"]}, "fragments": {"type": ["array", "null"], "items": {"type": "object", "properties": {"duration": {"type": "number"}, "url": {"type": "string"}}}}, "height": {"type": ["integer", "null"]}, "http_headers": {"type": "object", "additionalProperties": {"type": "string"}}, "protocol": {"type": "string"}, "resolution": {"type": ["string", "null"]}, "rows": {"type": ["integer", "null"]}, "tbr": {"type": ["number", "null"]}, "url": {"type": "string"}, "vbr": {"type": ["number", "null"]}, "vcodec": {"type": ["string", "null"]}, "video_ext": {"type": "string"}, "width": {"type": ["integer", "null"]}}}}, "fps": {"description": "Frames per second", "type": ["number", "null"]}, "fulltitle": {"description": "Full title", "type": "string"}, "has_drm": {"description": "Whether content has DRM", "type": ["boolean", "null"]}, "heatmap": {"description": "Video heatmap data", "type": ["array", "null"], "items": {"type": "object", "required": ["start_time", "end_time", "value"], "properties": {"end_time": {"type": "number"}, "start_time": {"type": "number"}, "value": {"type": "number"}}}}, "height": {"description": "Video height", "type": ["integer", "null"]}, "http_headers": {"description": "HTTP headers for download", "type": ["object", "null"], "additionalProperties": {"type": "string"}}, "is_live": {"description": "Whether this is a live stream", "type": ["boolean", "null"]}, "language": {"description": "Language code", "type": ["string", "null"]}, "language_preference": {"description": "Language preference", "type": ["integer", "null"]}, "like_count": {"description": "Number of likes", "type": ["integer", "null"]}, "live_status": {"description": "Live stream status", "type": ["string", "null"], "enum": ["not_live", "is_live", "is_upcoming", "was_live", null]}, "media_type": {"description": "Media type", "type": ["string", "null"]}, "original_url": {"description": "Original URL", "type": "string"}, "playable_in_embed": {"description": "Whether video can be embedded", "type": ["boolean", "null"]}, "playlist": {"description": "Playlist name", "type": ["string", "null"]}, "playlist_index": {"description": "Index in playlist", "type": ["integer", "null"]}, "preference": {"description": "General preference", "type": ["integer", "null"]}, "protocol": {"description": "Download protocol", "type": "string"}, "quality": {"description": "Quality rating", "type": ["integer", "null"]}, "release_timestamp": {"description": "Release timestamp", "type": ["integer", "null"]}, "release_year": {"description": "Release year", "type": ["integer", "null"]}, "requested_subtitles": {"description": "Requested subtitles", "type": ["object", "null"]}, "resolution": {"description": "Video resolution", "type": ["string", "null"]}, "source_preference": {"description": "Source preference", "type": ["integer", "null"]}, "subtitles": {"description": "Available subtitles by language code", "type": "object", "patternProperties": {"^[a-z]{2}(-[A-Z]{2})?$": {"type": "array", "items": {"type": "object", "required": ["url", "ext"], "properties": {"ext": {"type": "string"}, "protocol": {"type": "string"}, "url": {"type": "string"}, "name": {"type": "string"}}}}}}, "tags": {"description": "Video tags", "type": "array", "items": {"type": "string"}}, "tbr": {"description": "Total bitrate", "type": ["number", "null"]}, "thumbnail": {"description": "Default thumbnail URL", "type": ["string", "null"]}, "thumbnails": {"description": "Available thumbnail images", "type": "array", "items": {"type": "object", "required": ["url"], "properties": {"id": {"type": "string"}, "height": {"type": ["integer", "null"]}, "preference": {"type": ["integer", "null"]}, "url": {"type": "string"}, "width": {"type": ["integer", "null"]}}}}, "timestamp": {"description": "Upload timestamp", "type": ["integer", "null"]}, "upload_date": {"description": "Upload date (YYYYMMDD format)", "type": ["string", "null"], "pattern": "^[0-9]{8}$"}, "uploader": {"description": "Uploader name", "type": ["string", "null"]}, "uploader_id": {"description": "Uploader ID", "type": ["string", "null"]}, "uploader_url": {"description": "Uploader URL", "type": ["string", "null"]}, "url": {"description": "Download URL", "type": "string"}, "vbr": {"description": "Video bitrate", "type": ["number", "null"]}, "vcodec": {"description": "Video codec", "type": ["string", "null"]}, "video_ext": {"description": "Video file extension", "type": "string"}, "view_count": {"description": "Number of views", "type": ["integer", "null"]}, "was_live": {"description": "Whether this was a live stream", "type": ["boolean", "null"]}, "webpage_url": {"description": "Original webpage URL", "type": "string"}, "webpage_url_basename": {"description": "Webpage URL basename", "type": "string"}, "webpage_url_domain": {"description": "Webpage URL domain", "type": "string"}, "width": {"description": "Video width", "type": ["integer", "null"]}}}