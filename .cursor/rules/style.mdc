---
description: rules about React 19 Best Practices for components and styling 
globs: *.tsx
alwaysApply: false
---

# React 19 Best Practices for Components and Styling

## Tailwind

We've updated from tailwind v3 to v4. You must always keep breaking changes mentioned in [tailwind-v4.md](mdc:docs/tailwind-v4.md) in mind when writing tailwind classes. Use lucide icons.

if you are setting size of component using tailwind css, use `size-*` instead of `h-* w-*`

## Shadcn

You should use shadcn components if possible. 

Components available: Accordion, Alert, <PERSON>ert <PERSON>alog, Aspect Ratio, Avatar, Badge, Breadcrumb, Button, Calendar, Card, Carousel, **Chart**, Checkbox, Collapsible, Combobox, Command, Context Menu, Data Table, Date Picker, Dialog, Drawer, Dropdown Menu, Form, Hover Card, Input, Input OTP, Label, Menubar, Navigation Menu, Pagination, Popover, Progress, Radio Group, Resizable, Scroll Area, Select, Separator, Sheet, Sidebar, Skeleton, Slider, Sonner, Switch, Table, Tabs, Textarea, Toast, Toggle, Toggle Group, Tooltip

You should assume that the required packages are already available, and write code with imports. If it reports `Cannot find module for "@/components/ui/${kebab-case-shadcn-component-name}"`, then provide command for user to install it manually before continue like `pnpm dlx shadcn@latest add ...`. For example, if missing badge and card components, provide command `pnpm dlx shadcn@latest add badge card`, 

## UI Development

### Styling
- Use Tailwind CSS v4 with a mobile-first approach
- Implement Shadcn UI and Radix UI components
- Follow consistent spacing and layout patterns
- Ensure responsive design across breakpoints
- Use CSS variables for theme customization
    

### Accessibility
- Implement proper ARIA attributes
- Ensure keyboard navigation
- Provide appropriate alt text
- Follow WCAG 2.1 guidelines
- Test with screen readers
