---
description: Generic rules for coding
globs: *.tsx, *.ts
alwaysApply: false
---
- When import from nodejs, always add prefix like `import fs from "node:fs/promises"`
- Prefer using for-of loop when array item index is not used. 
- if you are setting size of component using tailwind css, use `size-*` instead of `h-* w-*`


# General Principles

- Write concise, readable TypeScript code
- Use functional and declarative programming patterns
- Follow DRY (Don't Repeat Yourself) principle
- Implement early returns for better readability
- Structure components logically: exports, subcomponents, helpers, types
- Document code with JSDoc comments if necessary

## Naming Conventions

- Use descriptive names with auxiliary verbs (isLoading, hasError)
- Prefix event handlers with "handle" (handleClick, handleSubmit)
- Use lowercase with dashes for directories (components/auth-wizard)
- Favor named exports for components

## TypeScript Usage
- Use TypeScript for all code
- Prefer interfaces over types
- Avoid enums; use const maps instead
- Implement proper type safety and inference
- Use `satisfies` operator for type validation