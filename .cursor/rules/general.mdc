---
description: Intro about project
globs: 
alwaysApply: true
---

## Structure

This is a monorepo with: 

- apps: inside /apps
  - /apps/web: a Progressive Web Application using React, React Router v7 (Remix v3), and Shadcn.
  - /apps/extension: a Browser Extension assisting PWA to bypass certain limitation with WXT framework

- packages: common utils
  - /packages/shared: common logic/utils shared between apps
  - /packages/ext-fn: protocols for cross-process (PWA <-> Extension) function calls.

this repo use pnpm as package manager:
  - Use `pnpm dlx` instead of `npx`
  - Use `pnpm add` instead of `npm install`
  - Use `pnpm add -D` for dev dependencies